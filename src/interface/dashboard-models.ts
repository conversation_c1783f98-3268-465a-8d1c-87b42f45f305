export interface LeaveType {
  id: number;
  icon: string;
  name: string;
  name_color: string;
  available: string;
  applied_days: string;
  taken_days_for_card: string;
  taken: string;
}

export interface LeaveHistory {
  id: number;
  leave_type: string;
  start_date: string;
  end_date: string;
  duration: string;
  created_by: string;
  status: "validate" | "confirm" | "draft" | 'refuse';
  line_manager_approve: boolean;
  dept_manager_approve: boolean;
  hr_approve: boolean;
}

export interface AdjustmentHistory {
  id: number;
  request_type: string;
  employee_id: number;
  date: string;
  date1: null;
  reason: string;
  adjustment_reason: string;
  original_from_date: string;
  original_to_date: string;
  work_from_home_status: number;
  state: "requested" | "approved" | "rejected";
}

export interface DayWiseDetails {
  punch_out: any;
  punch_in: any;
  id: number;
  date: string;
  status: "Present" | "Absence" | "Public Off Day" | "Weekend" | "Earned Leave" | "Sick Leave (SL)" | "Compensatory" | "Casual Leave(CL)";
  late_status: "Yes" | "NO";
  early_status: "Yes" | "NO";
  wfh_status: boolean;
  adjustment_status:boolean;
  off_day_duty: boolean;
  over_time: boolean;
  half_day_leave_status:boolean;
  leave_name:"Present" | "Absence" | "Public Off Day" | "Weekend" | "Earned Leave" | "Sick Leave (SL)" | "Compensatory" | "Casual Leave(CL)";
  half_day_leave_period:string
}


export interface KPI {
  year: string;
  id:number;
  total_out_off: number;
  quarter_marks: number;
  cto_marks: string;
  gtco_marks: string;
  final_total: number;
  performance_grade: string;
  quarters: Quarter[];
}

export interface Quarter {
  name: string;
  out_of: number;
  mark: number;
  line_manager_technical_mark: null;
  line_manager_behavioural_mark: null;
  hr_behavioural_mark: null;
  ho_comment: null;
  lm_accomplishment_remark: string;
  lm_next_quarter_goal_remark: string;
  lm_previous_improvement_update_remarks: string;
  lm_recovery_plan_remarks: string;
  lm_improvement_areas: string;
  lm_compilation_time_line: string;
  approved_by_manager: string;
  approved_by_hr: string;
  status: string;
  cto_marks?: string;
  gtco_marks?: string;
}