import { Modal } from "antd";
import React from "react";
interface Props {
    open: boolean;
    onOk: (e: React.MouseEvent<HTMLButtonElement>) => void;
    onCancel: (e: React.MouseEvent<HTMLButtonElement>) => void;
    okText?: string;
    cancelText?: string;
    message?: string;
    messageColor?: string;
    title?: string;
}

const ConfirmationModal = ({
    open,
    onOk,
    onCancel,
    okText,
    cancelText,
    message,
    messageColor,
    title,
}: Props) => {
    return (
        <Modal
            title={title ? title : "Are you sure ?"}
            open={open}
            onCancel={onCancel}
            onOk={onOk}
            okText={okText ? okText : "Yes"}
            cancelText={cancelText ? cancelText : "No"}
            centered
        >
            <p
                className={`${messageColor ? `text-[${messageColor}]` : "text-red-500"}`}
            >
                {message}
            </p>
        </Modal>
    );
};

export default ConfirmationModal;
