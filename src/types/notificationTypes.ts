
export interface BirthdayList {
  id: number;
  unique_id: string;
  name: string;
  birthday: string; // Ensure this matches API response
  designation: string;
  image: string;
}

export interface AnnouncementsList {
    id: number;
    title: string;
    description: string;
    requested_date: string;
    start_date:string;
    end_date: string;
  }
export interface EventListType {
  name: number;
  date: string;
  location: string;
  description: string;
  }