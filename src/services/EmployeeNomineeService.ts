import { FAILED, SUCCESS } from "@/utils/common";
import { errorToast } from "@/utils/helper";
import {API} from "./apiService";

export const EmployeeNomineeService= {
    async nomineeReport(body: any) {
        try {
            const { data } = await API.post("employee/nominee/report", body);

            return {
                status: SUCCESS,
                data: data,
            };
        } catch (e: any) {
            if (e.response) {
                const responseJson = JSON.parse(e.response.request.responseText);
                const errorDetail = responseJson.detail;
                errorToast(errorDetail);
            } else {
                console.error("Error:", e.message);
            }
            return {
                status: FAILED,
                message: "Something wrong!!",
                error: e,
            };
        }
    },
};
