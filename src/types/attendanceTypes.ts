export interface AttendanceSummaryItem {
    name: string;
    count: number;
    key: string;
    percent:number
  }

  export interface LeaveGraphItem {
    month: string;
    leaveCount: number;
  }

  export interface AttendanceGraphItem {
    month: string;
    present: number;
    absent:number;
    lateEntry:number;
    earlyExit:number;
  }

  export interface JoiningResigningGraph {
    year: string;
    joinCount: number;
    resignCount:number;
  }

  

