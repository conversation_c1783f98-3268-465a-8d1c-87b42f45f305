import React from 'react';
import ReactDOM from 'react-dom';

type NotificationType = 'success' | 'error' | 'warning';

const toastNotification = (type: NotificationType, message: string) => {
  // Create a container for the toast
  const toastContainer = document.createElement('div');
  toastContainer.id = 'toast-container';
  document.body.appendChild(toastContainer);

  // Function to remove the toast from the DOM
  const removeToast = () => {
    ReactDOM.unmountComponentAtNode(toastContainer);
    document.body.removeChild(toastContainer);
  };

  // Get the appropriate background color based on the type
  const getBackgroundColor = () => {
    switch (type) {
      case 'success':
        return '#4CAF50'; // Green
      case 'error':
        return '#F44336'; // Red
      case 'warning':
        return '#FF9800'; // Orange
      default:
        return '#2196F3'; // Blue (default)
    }
  };

  // Render the toast notification
  ReactDOM.render(
    <div
      style={{
        position: 'fixed',
        top: '20px',
        right: '20px',
        backgroundColor: getBackgroundColor(),
        color: 'white',
        padding: '12px 24px',
        borderRadius: '4px',
        boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
        zIndex: 1000,
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
      }}
    >
      <span>{message}</span>
      <button
        onClick={removeToast}
        style={{
          background: 'none',
          border: 'none',
          color: 'white',
          cursor: 'pointer',
          fontSize: '16px',
        }}
      >
        &times;
      </button>
    </div>,
    toastContainer
  );

  // Automatically remove the toast after 6 seconds
  setTimeout(() => {
    removeToast();
  }, 6000);
};

export default toastNotification;