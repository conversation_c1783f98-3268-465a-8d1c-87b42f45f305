import React from "react";
import { Bar<PERSON><PERSON> } from "@mui/x-charts/BarChart";
import { getJoiningResigningGraph } from "@/services/WorkStationServices/AttendanceServices";

const NewJoinerBarChart = () => {
  const {
    data: joinResignGraphResponse,
    error: joinResignGraphError,
    isLoading: joinResignGraphLoading,
  } = getJoiningResigningGraph();
  const joinerData =
    joinResignGraphResponse?.data || [];
  return (
    <BarChart
      layout="horizontal"
      xAxis={[{ scaleType: "linear" }]}
      yAxis={[{ scaleType: "band", data: joinerData.map((item) => item.year.toString()), categoryGapRatio: 0.7, min: 0, max: Math.max(...joinerData.map((item) => item.joinCount || item.resignCount || 0), 1), }]}
      series={[
        { data: joinerData.map((item) => item.joinCount), label: "New Joiners", color: "#3b82f6" },
        { data: joinerData.map((item) => item.resignCount), label: "Resigners", color: "#ef4444" }
      ]}

      height={420}
      borderRadius={50}
    />
  );
};

export default NewJoinerBarChart;
