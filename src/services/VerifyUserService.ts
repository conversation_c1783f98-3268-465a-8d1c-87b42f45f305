import { FAILED, SUCCESS } from "@/utils/common";
import { errorToast } from "@/utils/helper";
import axios from "axios";

export const VerifyUserService = {
  async verifyUser(uuid: string) {
    const baseUrl = process.env.NEXT_PUBLIC_API_URL_FOR_REPORT;
    
    try {
      const { data } = await axios.get(`${baseUrl}/api/users/${uuid}`);

      return {
        status: SUCCESS,
        data: data,
      };
    } catch (e: any) {
      if (e.response) {
        const responseJson = JSON.parse(e.response.request.responseText);
        const errorDetail = responseJson.detail;
        errorToast(errorDetail);
      } else {
        console.error("Error:", e.message);
      }
      return {
        status: FAILED,
        message: "Something wrong!!",
        error: e,
      };
    }
  },
};
export const VerifyUserServiceForWorkspace = {
  async verifyUser(uuid: string) {
    const baseUrl = process.env.NEXT_PUBLIC_API_URL_FOR_WORKSPACE;
    try {
      const { data } = await axios.get(`${baseUrl}/api/v1/authenticate-user/${uuid}`);
       
      return {
        status: SUCCESS,
        data: data,
      };
    } catch (e: any) {
      if (e.response) {
        const responseJson = JSON.parse(e.response.request.responseText);
        const errorDetail = responseJson.detail;
        errorToast(errorDetail);
      } else {
        console.error("Error:", e.message);
      }
      return {
        status: FAILED,
        message: "Something wrong!!",
        error: e,
      };
    }
  },
};