/** @type {import('next').NextConfig} */
const nextConfig = {
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  transpilePackages: [
    'antd',
    '@ant-design/icons-svg',
    'rc-picker',
    'rc-pagination',
    'rc-tree',
    'rc-motion',
    'rc-overflow',
    'rc-menu',
    'rc-select',
    'rc-table',
    'rc-util', // Add rc-util back to the transpilePackages array
    'rc-input' // Add rc-input to fix the build error
  ],
  experimental: {
    esmExternals: "loose" ,
    serverComponentsExternalPackages: []
  },
};

export default nextConfig;