import useS<PERSON> from "swr";
import axios from "axios";
import {  AttendanceGraphItem, AttendanceSummaryItem, JoiningResigningGraph, LeaveGraphItem } from "@/types/attendanceTypes";
import { APIForWorkspace } from "../apiService";


const fetcher = async <T>(url: string): Promise<T> => {
  const response = await APIForWorkspace.get<T>(url);
  return response.data; 
};


export const getAttendanceGraph = () => {
  return useSWR<{ data: AttendanceGraphItem[] }>("dashboard/attendance-graph", fetcher);
};

export const getAttendanceSummary = (date: string, employeeID?: string) => {
  return useSWR<{ data: AttendanceSummaryItem[] }>(`dashboard/card/attendance?date=${date}&employee_id=${employeeID || ""}`, fetcher);
};

export const getAttendanceSummaryForFractionalWorkspace = (date: string, employeeID?: string) => {
  const shouldFetch = !!employeeID;
  const url = `dashboard/card/attendance?date=${date}&employee_id=${employeeID || ""}`;
  
  return useSWR<{ data: AttendanceSummaryItem[] }>(
    shouldFetch ? url : null, 
    fetcher
  );
};

export const getLeaveGraphData = () => {
  return useSWR<{ data: LeaveGraphItem[] }>("dashboard/leave-graph", fetcher);
};

export const getJoiningResigningGraph = () => {
  return useSWR<{ data: JoiningResigningGraph[] }>("dashboard/employee-joining-resign-graph", fetcher);
};

// export const getAttendanceDetails = (attendanceType: string,pageNumber?: number,pageSize?: number) => { 
//   return useSWR<{ data: [] }>(
//     `dashboard/today-attendance-data?attendance_type=${attendanceType}`, 
//     fetcher
//   );
// };

// adjust based on your project setup

export const getAttendanceDetails = async (
  attendanceType: string | null,
  employeeID?: string,
  lastId?: string | null,
  for_export?: boolean,
  selectedDate?: string,
) => {
  try {
    let url = `dashboard/today-attendance-data?attendance_type=${attendanceType}&employee_id=${employeeID || ""}&input_date=${selectedDate}`;

    if (lastId !== undefined && lastId !== null && lastId !== "") {
      url += `&last_id=${lastId}`;
    }

    if (for_export) {
      url += `&for_export=${for_export}`;
    }

    const response = await APIForWorkspace.get(url);
    return response.data;
  } catch (error) {
    console.error("Failed to fetch attendance details:", error);
    throw error;
  }
};










