import MainLayout from "@/components/layout/MainLaylout";
import { TokenVerificationService } from "@/services/TokenVarification";
import { REFRESH_TOKEN_KEY, SUCCESS, TOKEN_KEY } from "@/utils/common";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import Workspace from "@/components/workspace/workspace";
import FractionalWorkspace from "@/components/fractional-workspace/fractionalWorkspace";

interface Props {
  window?: () => Window;
  children: React.ReactElement;
}

const Index = () => {
  const router = useRouter();
  const [employeeId, setEmployeeId] = useState<string | null>(null);
  
  useEffect(() => {
    // Access localStorage only on the client side
    const storedEmployeeId = localStorage.getItem("employeeId");
    setEmployeeId(storedEmployeeId);
    
    checkCookie();

    const intervalId = setInterval(() => {
      checkCookie();
    }, 100000);

    return () => clearInterval(intervalId);
  }, []);
  
  const checkCookie = async () => {
    // Only run this in browser environment
    if (typeof window === 'undefined') return;
    
    const token = localStorage.getItem(TOKEN_KEY);
    if (token) {
      return;
    }

    const refreshToken = localStorage.getItem(REFRESH_TOKEN_KEY);
    if (refreshToken) {
      await tokenRegenerate(refreshToken);
    } else {
      router.push("/unauthorized");
    }
  };

  const tokenRegenerate = async (refreshToken: string) => {
    try {
      const { data, status } =
        await TokenVerificationService.regenerateTokenForWorkspace(
          refreshToken
        );
      if (status === SUCCESS && data?.accessToken) {
        localStorage.setItem(TOKEN_KEY, data.accessToken);

        setTimeout(() => {
          localStorage.removeItem(TOKEN_KEY);
        }, 7 * 24 * 60 * 60 * 1000);
      } else {
        router.push("/unauthorized");
      }
    } catch (error) {
      console.error("Token Refresh Error:", error);
      router.push("/unauthorized");
    }
  };

  return (
    <MainLayout>
      {typeof window !== 'undefined' && (
        employeeId === '0' ? <Workspace/> : <FractionalWorkspace/>
      )}
    </MainLayout>
  );
};

export default Index;
