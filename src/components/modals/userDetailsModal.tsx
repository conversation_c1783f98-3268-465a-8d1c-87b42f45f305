import { API_ROUTE } from "@/constants/api-routes";
import { AttendanceInfo, LocationInfo, Schedule } from "@/interface/schedule";
import { getRequest } from "@/services/apiRequestHandlers";
import { capitalizeFirstLetter, convertIsoToCustomFormat } from "@/utils/helper";
import { Button, Col, Image, Modal, Row } from "antd";
import React, { useEffect, useState } from "react";
interface Props {
    isModalVisible: boolean;
    handleCancel: (e: React.MouseEvent<HTMLButtonElement>) => void;
    details: Schedule | undefined;
}
const UserDetailsModal = ({ isModalVisible, handleCancel, details }: Props) => {
    const [attendances, setAttendances] = useState<AttendanceInfo[]>([]);

    useEffect(() => {
        if (details?.employee_id && details?.status !== "Declined")
            fetchAttendanceDetails();
    }, [details?.employee_id]);
    const fetchAttendanceDetails = async () => {
        try {
            const resp : any = await getRequest(
                `${API_ROUTE.ATTENDANCE_INFO}/${details?.schedule_id}`
            );
            if (resp) {
                setAttendances(resp);
            }
        } catch (err) {
            console.log(err);
        }
    };
    return (
        <Modal
            open={isModalVisible}
            onCancel={handleCancel}
            footer={[
                <Button key="close" onClick={handleCancel}>
                    Close
                </Button>,
            ]}
            width={800}
            centered
        >
            <div className="flex-col">
                <div className="flex-1">
                    <h3 className="pb-6 pt-2 font-semibold text-blue-800">
                        User Details
                    </h3>
                    <Row gutter={[16, 16]}>
                        <Col span={8}>
                            <strong>Employee ID</strong>
                            <div>{details?.employee_unique_id || "N/A"}</div>
                        </Col>
                        <Col span={8}>
                            <strong>Name</strong>
                            <div>{details?.employee_name || "N/A"}</div>
                        </Col>
                        <Col span={8}>
                            <strong>Department</strong>
                            <div>{details?.department_name || "N/A"}</div>
                        </Col>
                        <Col span={8}>
                            <strong>Designation</strong>
                            <div>{details?.designation || "N/A"}</div>
                        </Col>
                        <Col span={8}>
                            <strong>Type</strong>
                            <div>{details?.scheduling_type || "N/A"}</div>
                        </Col>
                        <Col span={8}>
                            <strong>Reason</strong>
                            <div>
                                {capitalizeFirstLetter(details?.reason!) || "N/A"}
                            </div>
                        </Col>
                        <Col span={8}>
                            <strong>Shift</strong>
                            <div>{details?.shift_name || "N/A"}</div>
                        </Col>
                        <Col span={8}>
                            <strong>Start</strong>
                            <div>
                                {convertIsoToCustomFormat(
                                    details?.from_date_time!
                                ) || "N/A"}
                            </div>
                        </Col>
                        <Col span={8}>
                            <strong>End</strong>
                            <div>
                                {convertIsoToCustomFormat(
                                    details?.to_date_time!
                                ) || "N/A"}
                            </div>
                        </Col>
                        <Col span={16}>
                            <strong>Locations</strong>
                            <div>
                                {details &&
                                    details?.locations?.map(
                                        (item: LocationInfo, index: number) => (
                                            <p className="md:whitespace-nowrap">{`${index + 1}.${" "}${item?.location_name}`}</p>
                                        )
                                    )}
                            </div>
                        </Col>
                        <Col span={8}>
                            <strong>Radius</strong>
                            <div>
                                {`${details?.locations?.[0]?.radius} ${details?.locations?.[0]?.measurement}` ||
                                    "N/A"}
                            </div>
                        </Col>
                        <Col span={16}>
                            <strong>Description</strong>
                            <div>
                                {`${details?.description}` ||
                                    "N/A"}
                            </div>
                        </Col>
                    </Row>
                </div>
                <div
                    className={`flex-1 ${details?.status !== "Declined" ? "" : "pt-6"}`}
                >
                    {details?.status !== "Declined" && (
                        <h3 className="py-6 font-semibold text-blue-800">
                            Attendance Info
                        </h3>
                    )}
                    {attendances.length > 0 ? (
                        <div className="flex h-[400px] flex-col gap-4 overflow-y-scroll">
                            {attendances?.map(
                                (item: AttendanceInfo, index: number) => (
                                    <div
                                        key={index}
                                        className={`${
                                            index % 2 === 0
                                                ? "bg-white"
                                                : "bg-slate-100"
                                        } p-4 shadow-md`}
                                    >
                                        <Row gutter={[16, 16]}>
                                            <Col span={8}>
                                                <div className="mr-4">
                                                    <Image
                                                        src={item?.image}
                                                        alt="User Image"
                                                        className="rounded-lg object-cover"
                                                        height={60}
                                                        width={50}
                                                    />
                                                </div>
                                            </Col>
                                            <Col span={8}>
                                                <strong>Date</strong>
                                                <div>
                                                    {item?.att_date || "N/A"}
                                                </div>
                                            </Col>
                                            <Col span={8}>
                                                <strong>
                                                    {item?.attendance_type ===
                                                    "Checkin"
                                                        ? "Check In"
                                                        : "Check Out"}
                                                </strong>
                                                <div>
                                                    {item?.att_time || "N/A"}
                                                </div>
                                            </Col>
                                            <Col span={8}>
                                                <strong>Location</strong>
                                                <div>
                                                    {item?.location || "N/A"}
                                                </div>
                                            </Col>
                                            <Col span={8}>
                                                <strong>Remarks</strong>
                                                <div>
                                                    {item?.remarks || "N/A"}
                                                </div>
                                            </Col>
                                        </Row>
                                    </div>
                                )
                            )}
                        </div>
                    ) : details?.status === "Declined" ? (
                        <div className="flex h-24 w-full flex-col items-center justify-center border-[1px] border-dashed border-red-600">
                            <p className="text-lg font-semibold text-red-600">
                                Declined
                            </p>
                            <p>Reason: {details?.decline_reason}</p>
                        </div>
                    ) : (
                        <div className="flex h-24 w-full items-center justify-center border-[1px] border-dashed">
                            No Data Found
                        </div>
                    )}
                </div>
            </div>
        </Modal>
    );
};

export default UserDetailsModal;
