"use client";
import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import { VerifyUserService } from "@/services/VerifyUserService";
import { VerifyUserServiceForWorkspace } from "@/services/VerifyUserService";
import {
  REFRESH_TOKEN_KEY,
  SUCCESS,
  TOKEN_FOR_REPORT,
  TOKEN_KEY,
} from "@/utils/common";
import Loader from "@/components/common/Loader";

export default function Home() {
  const router = useRouter();
  const { uuid, reportType, employeeId } = router.query;
  const [loading, setLoading] = useState(true);
  
  const authenticateUser = async () => {
    try {
      let apiResponse;
      if (reportType === "dashboard" || reportType === "workspace"  || reportType==="attendance-devices" || reportType==="schedule-list") {
        apiResponse = await VerifyUserServiceForWorkspace.verifyUser(
          uuid as string
        );
      } else {
        apiResponse = await VerifyUserService.verifyUser(uuid as string);
      }

      if (apiResponse?.status === SUCCESS) {
        if (
          apiResponse.data?.access_token &&
          (reportType === "employee" || reportType === "attendance")
        ) {
          localStorage.setItem(TOKEN_FOR_REPORT, apiResponse.data.access_token);
          localStorage.setItem(
            REFRESH_TOKEN_KEY,
            apiResponse.data.refresh_token
          );
          if (reportType) localStorage.setItem("rTyp", reportType);

          setTimeout(() => {
            localStorage.removeItem(TOKEN_KEY);
          }, 7 * 24 * 60 * 60 * 1000);

          setTimeout(() => {
            localStorage.removeItem(REFRESH_TOKEN_KEY);
          }, 30 * 24 * 60 * 60 * 1000);
        } else {
          localStorage.setItem(TOKEN_KEY, apiResponse.data.accessToken);
          localStorage.setItem("uId", apiResponse.data.userId);
          if (employeeId)
            localStorage.setItem("employeeId", employeeId.toString());
          localStorage.setItem("showSearch", apiResponse.data.show_search);
          localStorage.setItem(
            REFRESH_TOKEN_KEY,
            apiResponse.data.refreshToken
          );

          setTimeout(() => {
            localStorage.removeItem(TOKEN_KEY);
          }, 7 * 24 * 60 * 60 * 1000);

          setTimeout(() => {
            localStorage.removeItem(REFRESH_TOKEN_KEY);
          }, 30 * 24 * 60 * 60 * 1000);
        }
        router.replace(
          reportType === "dashboard" || reportType === "workspace" || reportType==="attendance-devices" || reportType==="schedule-list"
            ? reportType
            : "/report"
        );
      }
    } catch (error) {
      console.error("Authentication Error:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    localStorage.clear();
    if (!uuid || !reportType) return;
    else {
      authenticateUser();
    }
  }, [uuid, reportType, router, employeeId]);

  ////////// Need For Iframe fire //////////////
  useEffect(() => {
    if (reportType && uuid) {
      const handleMessage = (event: MessageEvent) => {
        if (event.origin !== process.env.NEXT_PUBLIC_ODOO_URL) return;
      };
      window.addEventListener("message", handleMessage);
      return () => window.removeEventListener("message", handleMessage);
    }
  }, [uuid, reportType]);

  if (loading) return <Loader />;

  return <></>;
}
