import React, { useState } from "react";
import GroupsIcon from "@mui/icons-material/Groups";
import ContactPageIcon from "@mui/icons-material/ContactPage";
import GroupAddIcon from "@mui/icons-material/GroupAdd";
import DescriptionIcon from "@mui/icons-material/Description";
import ManageSearchIcon from "@mui/icons-material/ManageSearch";
import { Box, Typography } from "@mui/material";
import AttendanceChart from "./attendanceChart";
import ApprovalRequestTable from "./approvalRequestTable";
import LeaveChart from "./leaveChart";
import NewJoinerBarChart from "./newJoinerBarChart";
import ReusableModal from "../common/Modal";
import AnnouncementCard from "./announcementCard";
import BirthdayCard from "./birthDayCard";
import EventCard from "./eventCard";
import { getAttendanceSummary } from "@/services/WorkStationServices/AttendanceServices";
import { getApprovalRequestSummary } from "@/services/WorkStationServices/ApprovalServices";
import { AttendanceSummaryItem } from "@/types/attendanceTypes";
import {
  getAnnouncementsList,
  getBirthdayList,
} from "@/services/WorkStationServices/NotificationServies";
import { AnnouncementsList, BirthdayList } from "@/types/notificationTypes";
import { ApprovalRequestSummary } from "@/types/ApprovalTypes";
import HistoryModal from "../common/historyModal";
import { CircularProgress } from "@mui/material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import isSameDay from "date-fns/isSameDay";
import isAfter from "date-fns/isAfter";
import { format } from "date-fns";
type approvalRequestType = "Leave" | "Attendance" | "Self Service";
const Workspace = () => {
  const [openModal, setOpenModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState({ name: "", key: "" });
  const [approvalRequestName, setApprovalRequestName] =
    useState<approvalRequestType>("Leave");
  const [activeHistoryPage, setActiveHistoryPage] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | null>(new Date());
  const formattedDate = selectedDate ? format(selectedDate, "yyyy-MM-dd") : "";
  const { data: attendanceResponse, error: attendanceError, isLoading: attendanceLoading } = getAttendanceSummary(formattedDate);


  const {
    data: approvalResponse,
    error: approvalError,
    isLoading: approvalLoading,
  } = getApprovalRequestSummary();

  const {
    data: birthdayListResponse,
    error: birthdayListError,
    isLoading: birthdayListLoading,
  } = getBirthdayList();

  const {
    data: announcementsListResponse,
    error: announcementsListError,
    isLoading: announcementsListLoading,
  } = getAnnouncementsList();

  const attendanceSummaryList: AttendanceSummaryItem[] =
    attendanceResponse?.data || [];
  const pendingRequestSummaryList: ApprovalRequestSummary[] =
    approvalResponse?.data || [];

  const birthdaysList: BirthdayList[] = birthdayListResponse?.data || [];
  const announcementsList: AnnouncementsList[] =
    announcementsListResponse?.data || [];

  const handleModalClick = (item: any) => {
    setOpenModal(true);
    setSelectedItem(item);
  };

  const handleRequestClick = (item: any) => {
    item.name !== "History"
      ? setApprovalRequestName(item.name)
      : setActiveHistoryPage(true);
  };

  const disableNextDay = (date: Date) => {
    return isAfter(date, new Date()); // disallow future dates
  };

  return (
    <div className="ml-6 py-4 pr-0 md_lg:pl-12 lg:pl-[50px]">
      <div className="flex flex-col md:flex-row gap-4 px-4">
        {/* Today's Attendance Summary */}
        <div className="w-full xl:w-3/5 2xl:w-full p-3 bg-white shadow-md">
          <div className="flex justify-between items-center gap-4 pb-2">
            <Typography className="text-sm xl:text-lg !font-bold pb-2 text-sky-800">
             Attendance Summary
            </Typography>

            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label="Pick a date"
                value={selectedDate}
                onChange={(newValue) => {
                  if (newValue) setSelectedDate(newValue);
                }}
                shouldDisableDate={disableNextDay}
                slotProps={{
                  textField: {
                    size: "small",
                    className: "bg-white",
                  },
                }}
              />
            </LocalizationProvider>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4 gap-3">
            {attendanceSummaryList?.map((item) => (
              <div
                key={item.name}
                className="relative text-start shadow-lg flex items-center p-2 md:p-2 xl:p-3 transition-transform duration-300 ease-in-out transform hover:scale-105"
                onClick={() => handleModalClick(item)}
              >
                <div className="absolute top-0 left-0 w-1 h-full bg-green-600" />

                {/* Content Wrapper */}
                <div className="flex flex-row items-center justify-start w-full">
                  <div className="relative w-16 h-16 flex items-center justify-center">
                    <Box position="relative" display="inline-flex">
                      <CircularProgress
                        variant="determinate"
                        value={100}
                        sx={{ color: "#E0E0E0" }}
                        size={64}
                        thickness={3}
                      />
                      <CircularProgress
                        variant="determinate"
                        value={item?.percent}
                        sx={{
                          color: getProgressColor(item.name),
                          position: "absolute",
                          left: 0,
                        }}
                        size={64}
                        thickness={3}
                      />
                      <Box
                        position="absolute"
                        top={0}
                        left={0}
                        bottom={0}
                        right={0}
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                      >
                        <div
                          className={`absolute flex items-center justify-center w-full h-full ${getColor(
                            item.name
                          )}`}
                        >
                          <span className="text-sm font-bold">
                            {item?.percent}%
                          </span>
                        </div>
                      </Box>
                    </Box>
                  </div>

                  {/* Text Content */}
                  <div className="flex flex-col pl-4">
                    <div className="text-xs sm:text-sm md:text-sm lg:text-md xl:text-md font-semibold text-sky-800">
                      {item?.name}
                    </div>
                    <div className=" font-bold text-lg text-sky-800">
                      {item?.count}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          <ReusableModal
            open={openModal}
            onClose={() => setOpenModal(false)}
            title={selectedItem}
            selectedDate={formattedDate}
            setSelectedItem={setSelectedItem}
          />
        </div>
        {/* Pending Approval Requests */}
        <div className="w-full xl:w-2/5 2xl:w-full p-3 bg-white shadow-md">
          <Typography className="text-sm xl:text-lg !font-bold pb-2 text-orange-600">
            Pending Approval Requests
          </Typography>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-2">
            {pendingRequestSummaryList?.map((item) => (
              <div
                key={item?.name}
                className="relative text-start shadow-lg flex items-center p-2 md:p-2 xl:p-3 transition-transform duration-300 ease-in-out transform hover:scale-105"
                onClick={() => handleRequestClick(item)}
              >
                <div className="absolute top-0 left-0 w-1 h-full bg-orange-600" />

                {/* Content Wrapper */}
                <div className="flex flex-row items-center justify-between w-full">
                  {/* Text Content */}
                  <div className="flex flex-col pl-2">
                    <div className="text-xs sm:text-sm md:text-sm lg:text-md xl:text-md font-semibold text-sky-800">
                      {item?.name}
                    </div>
                    <div className=" font-bold text-lg text-sky-800">
                      {item?.count}
                    </div>
                  </div>

                  {/* Icon */}
                  <div className="text-3xl shrink-0 pt-5 pr-1">
                    {getIcon(item?.name)}
                  </div>
                </div>
              </div>
            ))}
          </div>
          <HistoryModal
            open={activeHistoryPage}
            onClose={() => setActiveHistoryPage(false)}
            approvalRequestName={approvalRequestName}
          />
        </div>
      </div>
      <div className="flex flex-col xl2:flex-row gap-4 justify-center items-center xl2:items-start xl2:m-5 m-2">
        {/* Pending Approval Requests List */}
        <div className="order-1 xl2:order-2 w-full lg_xl:w-full xl2:w-[60%] bg-white shadow-lg min-h-[300px]">
          <Typography className="text-sm xl2:text-lg !font-bold pb-5 text-orange-600 pl-3">
            Pending Approval Requests List
          </Typography>
          <ApprovalRequestTable approvalRequestName={approvalRequestName} />
        </div>

        {/* Attendance Graph */}
        <div className="order-2 xl2:order-1 w-full lg_xl:w-full xl2:w-[40%] bg-white shadow-lg">
          <Typography className="text-lg !font-bold pb-5 pl-5 text-sky-800">
            Attendance Graph (Last 30 days)
          </Typography>
          <AttendanceChart />
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-4 justify-center items-center md:items-start md:m-5 m-2">
        <div className="w-full md:w-1/2 lg:w-1/2 xl:1/2  bg-white shadow-lg">
          <Typography className="text-sm xl:text-lg !font-bold  pl-5 pb-5 text-orange-600">
            Leave Graph (Last 6 Month)
          </Typography>
          <LeaveChart />
        </div>
        <div className="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 bg-white shadow-lg">
          <Typography className="text-sm xl:text-lg !font-bold  pb-5 pl-5 text-sky-800">
            New Joiners and Resigners Per Year
          </Typography>
          <NewJoinerBarChart />
        </div>
      </div>
      <div className="flex flex-col md:flex-row gap-4 justify-center items-center md:items-start md:m-5 m-2">
        <div className="w-full md:w-1/2 lg:w-1/2 xl:1/3 bg-white shadow-lg">
          <Typography className="text-sm xl:text-lg !font-bold  pl-5 pb-5 text-orange-600">
            Announcement Board
          </Typography>
          <AnnouncementCard announcements={announcementsList} />
        </div>
        <div className="w-full md:w-1/2 lg:w-1/2 xl:w-1/3 bg-white shadow-lg">
          <Typography className="text-sm xl:text-lg !font-bold  pb-5 pl-5 text-green-600">
            Birthday Board
          </Typography>
          <BirthdayCard birthdays={birthdaysList} />
        </div>
        <div className="w-full md:w-1/2 lg:w-1/2 xl:w-1/3 bg-white shadow-lg">
          <Typography className="text-sm xl:text-lg !font-bold  pb-5 pl-5 text-sky-800">
            Events List
          </Typography>
          <EventCard />
        </div>
      </div>
    </div>
  );
};

const getIcon = (name: string) => {
  switch (name) {
    case "Leave":
      return <ContactPageIcon className="text-red-500" fontSize="large" />;
    case "Attendance":
      return <GroupAddIcon className="text-green-500" fontSize="large" />;
    case "Self Service":
      return <DescriptionIcon className="text-sky-500" fontSize="large" />;
    case "History":
      return <ManageSearchIcon className="text-teal-500" fontSize="large" />;
    default:
      return <GroupsIcon className="text-gray-400" fontSize="large" />; // Default icon
  }
};

const getColor = (name: string) => {
  switch (name) {
    case "My Teams":
      return "text-blue-600";
    case "Absent":
      return "text-red-600";
    case "Present":
      return "text-green-600";
    case "Late":
      return "text-yellow-500";
    case "Early Exit":
      return "text-teal-500";
    case "On Leave":
      return "text-lime-400";
    default:
      return "text-gray-600";
  }
};

const getProgressColor = (name: string) => {
  switch (name) {
    case "My Teams":
      return "#2563eb";
    case "Absent":
      return "#dc2626";
    case "Present":
      return "#16a34a";
    case "Late":
      return "#eab308";
    case "Early Exit":
      return "#14b8a6";
    case "On Leave":
      return "#65a30d";
    default:
      return "#6b7280";
  }
};

export default Workspace;
