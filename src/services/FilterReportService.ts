import { FAILED, SUCCESS } from "@/utils/common";
import { errorToast } from "@/utils/helper";
import {API} from "./apiService";

export const FilterReportService = {
  async getCompany() {
    try {
      const { data } = await API.get(`user/companies`);

      return {
        status: SUCCESS,
        data: data,
      };
    } catch (e: any) {
      if (e.response) {
        const responseJson = JSON.parse(e.response.request.responseText);
        const errorDetail = responseJson.detail;
        errorToast(errorDetail);
      } else {
        console.error("Error:", e.message);
      }
      return {
        status: FAILED,
        message: "Something wrong!!",
        error: e,
      };
    }
  },
  async getDivision(id: number) {
    try {
      const { data } = await API.get(`user/division?id=${id}`);

      return {
        status: SUCCESS,
        data: data,
      };
    } catch (e: any) {
      if (e.response) {
        const responseJson = JSON.parse(e.response.request.responseText);
        const errorDetail = responseJson.detail;
        errorToast(errorDetail);
      } else {
        console.error("Error:", e.message);
      }
      return {
        status: FAILED,
        message: "Something wrong!!",
        error: e,
      };
    }
  },
  async getDepartment(id: any, divisionId: any) {
    try {
      let url = `user/department?id=${id}`;

      if (divisionId) {
        url += `&div_id=${divisionId}`;
      }
      const { data } = await API.get(url);
      return {
        status: SUCCESS,
        data: data,
      };
    } catch (e: any) {
      if (e.response) {
        const responseJson = JSON.parse(e.response.request.responseText);
        const errorDetail = responseJson.detail;
        errorToast(errorDetail);
      } else {
        console.error("Error:", e.message);
      }
      return {
        status: FAILED,
        message: "Something wrong!!",
        error: e,
      };
    }
  },
  async getLocations(id: any) {
    try {
      const { data } = await API.get(`user/work/location?id=${id}`);

      return {
        status: SUCCESS,
        data: data,
      };
    } catch (e: any) {
      if (e.response) {
        const responseJson = JSON.parse(e.response.request.responseText);
        const errorDetail = responseJson.detail;
        errorToast(errorDetail);
      } else {
        console.error("Error:", e.message);
      }
      return {
        status: FAILED,
        message: "Something wrong!!",
        error: e,
      };
    }
  },
  async getEmployees(
    id: any,
    divisionId: any,
    departmentId: any,
    locationId: any,
    isSeparated: boolean
  ) {
    try {
      let url = "user/employee/list?id=" + id;

      if (divisionId) {
        url += "&div=" + divisionId;
      }

      if (departmentId) {
        url += "&dept=" + departmentId;
      }

      if (locationId) {
        url += "&location=" + locationId;
      }
      if (isSeparated) {
        url += "&isSeparated=" + isSeparated;
      } else {
        url += "&isSeparated=" + isSeparated;
      }
      const { data } = await API.get(url);

      return {
        status: SUCCESS,
        data: data,
      };
    } catch (e: any) {
      if (e.response) {
        const responseJson = JSON.parse(e.response.request.responseText);
        const errorDetail = responseJson.detail;
        errorToast(errorDetail);
      } else {
        console.error("Error:", e.message);
      }
      return {
        status: FAILED,
        message: "Something wrong!!",
        error: e,
      };
    }
  },
};
