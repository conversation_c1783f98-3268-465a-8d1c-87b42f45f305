import React from "react";
import dayjs from "dayjs";
import { FaHandScissors, FaStethoscope } from "react-icons/fa";
import { FaHandHoldingDollar } from "react-icons/fa6";
import { MdRunningWithErrors, MdTimerOff } from "react-icons/md";
import { IoMdTime } from "react-icons/io";
import { RiHomeOfficeFill } from "react-icons/ri";

const DashboardComponent: React.FC = () => {
  const leaveTypes = [
    {
      name: "Compensatory",
      available: 7,
      taken: 7,
      icon: <IoMdTime />,
      color: "bg-blue-200",
    },
    {
      name: "Sick Leave (SL)",
      available: 10,
      taken: 0,
      icon: <FaStethoscope />,
      color: "bg-yellow-200",
    },
    {
      name: "Earned Leave",
      available: 31,
      taken: 0,
      icon: <FaHandHoldingDollar />,
      color: "bg-green-200",
    },
    {
      name: "Casual Leave (CL)",
      available: 4,
      taken: 0,
      icon: <FaHandScissors />,
      color: "bg-red-200",
    },
    {
        name: "Work From Home",
        taken: 0,
        icon: <RiHomeOfficeFill />,
        color: "bg-red-200",
    },
    {
      name: "Late Status",
      taken: 0,
      icon: <MdTimerOff />,
      color: "bg-red-200",
    },
    {
      name: "Early Exit",
      taken: 0,
      icon: <MdRunningWithErrors />,
      color: "bg-red-200",
    },
  ];

  // Generate Calendar Months
  const months = Array.from({ length: 12 }, (_, i) =>
    dayjs().month(i).format("MMMM YYYY")
  );

  // Generate Days for the Calendar
  const generateDays = (month: string) => {
    const startOfMonth = dayjs(month).startOf("month");
    const endOfMonth = dayjs(month).endOf("month");
    const days = [];

    for (let i = startOfMonth.date(); i <= endOfMonth.date(); i++) {
      days.push(i);
    }

    return days;
  };

  // Get Available/Used leave for a specific date
  const getLeaveColor = (date: number) => {
    if (date <= 7) return "bg-blue-500"; // Compensatory
    if (date <= 10) return "bg-yellow-500"; // Sick Leave
    if (date <= 4) return "bg-green-500"; // Earned Leave
    return "bg-red-500"; // Casual Leave
  };

  return (
    <div className="flex items-start text-[#063c7a]">
      <div className="py-3 pl-[72px] pr-2">
        {/* Leave Summary */}
        <div className="grid grid-cols-1 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-7 gap-4 mb-6">
          {leaveTypes.map((leave, index) => (
            <div
              key={index}
              className="bg-white shadow-md px-3 py-2 flex flex-col gap-1 items-center text-center text-base"
            >
              <div className="flex flex-row gap-2 items-center">
                <div className="text-2xl">{leave.icon}</div>
                <h3 className="font-semibold whitespace-nowrap">{leave.name}</h3>
              </div>
              <p className="text-green-500">{leave.available} - AVAILABLE</p>
              <p className="text-red-500">{leave.taken} - TAKEN</p>
            </div>
          ))}
        </div>
        <div className="flex flex-col md:flex-row gap-2">
          {/* Calendar */}
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {months.map((month, index) => (
              <div key={index} className="bg-white shadow p-2 w-full md:w- ">
                <h3 className="font-bold text-center text-base mb-2">
                  {month}
                </h3>
                <div className="grid grid-cols-7 gap-1 text-center text-gray-700 text-sm">
                  {["S", "M", "T", "W", "T", "F", "S"].map((day) => (
                    <div key={day} className="font-semibold">
                      {day}
                    </div>
                  ))}
                  {generateDays(month).map((date, i) => (
                    <div key={i} className={`p-1 md:px-2 md:py-1 rounded-md`}>
                      {date}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
          {/* Leave Policy */}
          <div className="p-4 bg-gray-100">
            <h3 className="font-bold mb-2">Leave Policy</h3>
            <p className="text-sm ">Casual Leave = 10 days (per year)</p>
            <p className="text-sm ">Sick Leave = 14 days (per year)</p>
            <p className="text-sm ">
              Leave applies on pro-rata basis for confirmed employees.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardComponent;
