import ReportFilter from "@/components/features/ReportFilter";
import Header from "@/components/layout/Header";
import MainLayout from "@/components/layout/MainLaylout";
import { TokenVerificationService } from "@/services/TokenVarification";
import { VerifyUserService } from "@/services/VerifyUserService";
import { REFRESH_TOKEN_KEY, SUCCESS, TOKEN_KEY } from "@/utils/common";
import {
  Container,
  CssBaseline,
  Toolbar,
  useScrollTrigger,
} from "@mui/material";
import { getCookie, setCookie } from "cookies-next";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import { Boun<PERSON>, ToastContainer } from "react-toastify";

interface Props {
  window?: () => Window;
  children: React.ReactElement;
}

export default function Home() {
  const router = useRouter();

  const [loading, setLoading] = useState(true);

    const authenticateUser = async (uuid: string) => {
        // if (!userId) {
        //   router.push("/unauthorized");
        // }
        if (uuid) {
            const {data, status} = await VerifyUserService.verifyUser(uuid);

      if (status === SUCCESS) {
        if (data?.access_token) {
          setCookie(TOKEN_KEY, data?.access_token, { maxAge: 10 * 60 });
          setCookie(REFRESH_TOKEN_KEY, data?.refresh_token, {
            maxAge: 30 * 60,
          });
        }
      }
    }
  };

  const { uuid } = router.query;

  

  useEffect(() => {
    const token = getCookie(TOKEN_KEY);
    if (!token) {
      if (uuid && typeof uuid === "string") {
        authenticateUser(uuid);
      }
    } else {
      tokenRegenerate(token);
    }

    // else {
    //   authenticateUser(null)
    // }
  }, [router.query]);

  useEffect(() => {
    const checkToken = () => {
      const token = getCookie(TOKEN_KEY);
      if (token) {
        setLoading(false);
      } else {
        setTimeout(checkToken, 1000); // Check again in 1 second
        setLoading(true);
      }
    };

    checkToken(); // Initial check when component mounts

    return () => {};
  }, []);

  const checkCookie = () => {
    const cookieValue = getCookie(TOKEN_KEY);
    if (cookieValue) {
      return true;
      // You can perform further actions here
    } else {
      const refresh_token = getCookie(REFRESH_TOKEN_KEY);
      if (refresh_token) {
        tokenRegenerate(refresh_token);
      } else {
        // router.push("/unauthorized");
      }
    }
  };

  useEffect(() => {
    // Check the cookie immediately on component mount
    checkCookie();

    // Set up an interval to check the cookie every 10 minutes (600000 milliseconds)
    const intervalId = setInterval(checkCookie, 600000);

    // Clean up the interval on component unmount
    return () => clearInterval(intervalId);
  }, []);

  function ElevationScroll(props: Props) {
    const { children, window } = props;
    const trigger = useScrollTrigger({
      disableHysteresis: true,
      threshold: 0,
      target: window ? window() : undefined,
    });

    return React.cloneElement(children, {
      elevation: trigger ? 4 : 0,
    });
  }

  const tokenRegenerate = async (token: string) => {
    const { data, status } = await TokenVerificationService.regenerateToken(
      token
    );
    try {
      if (status === SUCCESS) {
        setCookie(TOKEN_KEY, data?.access_token, { maxAge: 10 * 60 });
      }
    } catch (e) {
      console.log("Verification: ", e);
    }
  };

  if (loading) {
    return <div>Loading</div>;
  } else {
    return (
      <>
        <CssBaseline />
        <ToastContainer
          position="top-right"
          autoClose={2000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme="light"
          transition={Bounce}
        />
        <MainLayout>
          <ElevationScroll>
            <Header />
          </ElevationScroll>
          <Toolbar />
          <Container>
            <ReportFilter />
          </Container>
        </MainLayout>
      </>
    );
  }
}
