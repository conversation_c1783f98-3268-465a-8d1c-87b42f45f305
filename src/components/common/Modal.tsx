import React, { useState, useEffect, useRef } from "react";
import {
  Modal,
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Avatar,
  Button,
  Menu,
  MenuItem,
  Checkbox,
  FormControlLabel,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import DownloadIcon from "@mui/icons-material/Download";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { getAttendanceDetails } from "@/services/WorkStationServices/AttendanceServices";

const ReusableModal = ({ open, onClose, title,selectedDate,setSelectedItem }: any) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedColumns, setSelectedColumns] = useState<string[]>([]);
  const [attendanceDetails, setAttendanceDetails] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [lastId, setLastId] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const scrollRef = useRef<HTMLDivElement | null>(null);
  const attendanceType = title?.key;
 
  
  const defaultColumns = [
    "Name",
    "ID",
    "Department",
    "Designation",
    "Line Manager",
    ...(title?.name === "My Teams" ||
    title?.name === "Absent" ||
    title?.name === "On Leave"
      ? ["Phone Number", "Email", "Work Location"]
      : ["Check In Time", "Check Out Time"]),
  ];

  useEffect(() => {
    if (selectedColumns.length === 0) {
      setSelectedColumns(defaultColumns);
    }
  }, [defaultColumns]);

  useEffect(() => {
    if (open) {
      setAttendanceDetails([]);
      setLastId(null);
      setHasMore(true);
            // Pass true to force loading regardless of hasMore state
            loadAttendanceData(null, true);
    }
  }, [open]);

  const loadAttendanceData = async (id: string | null, forceLoad = false)  => {
    if (!forceLoad && (!hasMore || isLoading)) return;
    setIsLoading(true);
    try {
      const res = await getAttendanceDetails(attendanceType,"", id, false,selectedDate);
      setTotal(res?.total);
      const newData = res?.data || [];

      setAttendanceDetails((prev) => [...prev, ...newData]);

      if (newData.length > 0) {
        const newLastId = newData[newData.length - 1]?.id || null;
        setLastId(newLastId);
      } else {
        setHasMore(false);
      }
    } catch (error) {
      console.error("Failed to fetch data:", error);
      setHasMore(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleScroll = () => {
    if (!scrollRef.current || isLoading) return;
    const { scrollTop, scrollHeight, clientHeight } = scrollRef.current;
    if (scrollTop + clientHeight >= scrollHeight - 20) {
      loadAttendanceData(lastId);
    }
  };

  const handleDownload = async () => {
    try {
      const res = await getAttendanceDetails(attendanceType, "","", true,selectedDate);
      const fullData = res?.data || [];
      const filteredData = fullData.map((employee: any) => {
        const row: Record<string, string> = {};
        selectedColumns.forEach((col) => {
          switch (col) {
            case "Name":
              row["Name"] = employee?.employee_name || "";
              break;
            case "ID":
              row["ID"] = employee?.unique_id || "";
              break;
            case "Department":
              row["Department"] = employee?.department || "";
              break;
            case "Designation":
              row["Designation"] = employee?.designation || "";
              break;
            case "Line Manager":
              row["Line Manager"] = employee?.line_manager || "--";
              break;
            case "Phone Number":
              row["Phone Number"] = employee?.phone_number || "--";
              break;
            case "Email":
              row["Email"] = employee?.email || "--";
              break;
            case "Work Location":
              row["Work Location"] = employee?.work_location || "--";
              break;
            case "Check In Time":
              row["Check In Time"] = employee?.check_in_time || "--";
              break;
            case "Check Out Time":
              row["Check Out Time"] = employee?.check_out_time || "--";
              break;
          }
        });
        return row;
      });

      const worksheet = XLSX.utils.json_to_sheet(filteredData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Attendance");
      const excelBuffer = XLSX.write(workbook, {
        bookType: "xlsx",
        type: "array",
      });
      const file = new Blob([excelBuffer], {
        type: "application/octet-stream",
      });
      saveAs(file, `${title?.name || "attendance"}_report.xlsx`);
      handleClose();
    } catch (err) {
      console.error("Download failed:", err);
    }
  };

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
    setSelectedItem({});
  };

  const handleToggleColumn = (col: string) => {
    setSelectedColumns((prev) => {
      const updated = prev.includes(col)
        ? prev.filter((c) => c !== col)
        : [...prev, col];
  
      // Ensure the order matches defaultColumns
      return defaultColumns.filter((dc) => updated.includes(dc));
    });
  };
  

  return (
    <Modal open={open} onClose={onClose}>
      <Box
        sx={{
          width: "70%",
          bgcolor: "background.paper",
          pt: 3,
          pb: 1,
          px:3,
          m: "auto",
          mt: { xs: 2, lg: 5 , xl: 10},
          boxShadow: 24,
          maxHeight: "700px",
          borderRadius: "10px",
          outline: "none",
        }}
      >
        <Box sx={{ display: "flex", justifyContent: "space-between", mb: 2 }}>
          <Typography variant="h5" className="text-sky-800 font-semibold pl-2">
            {`${title?.name} List`}
          </Typography>
          <Box>
            <Button
              variant="outlined"
              size="small"
              onClick={handleClick}
              startIcon={<DownloadIcon />}
              sx={{ mr: 1 }}
            >
              Download
            </Button>
            <IconButton
              onClick={onClose}
              size="small"
              sx={{
                color: "red",
                "&:hover": { bgcolor: "red", color: "white" },
              }}
            >
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>

        <TableContainer
          component={Paper}
          ref={scrollRef}
          onScroll={handleScroll}
          sx={{ maxHeight: "500px" }}
        >
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                {selectedColumns.map((header) => (
                  <TableCell
                    key={header}
                    sx={{
                      paddingLeft: 2,
                      fontWeight: "bold",
                      border: "none",
                      position: "sticky",
                      top: 0,
                      backgroundColor: "background.paper",
                      zIndex: 2,
                      whiteSpace: "nowrap",
                      textAlign: "center",
                    }}
                  >
                    {header}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            {
              attendanceDetails.length > 0 ? (
                <TableBody>
              {attendanceDetails.map((employee: any, rowIndex: number) => (
                <TableRow
                  key={employee.id || rowIndex}
                  className="!text-center"
                  sx={{
                    backgroundColor: rowIndex % 2 === 0 ? "#f9fafb" : "#f3f4f6"
                  }}
                >
                  {selectedColumns.map((col) => {
                    const value = (() => {
                      switch (col) {
                        case "Name":
                          return (
                            <Box display="flex" alignItems="center" gap={1}>
                              <Avatar
                                src={employee.image}
                                alt={employee.name}
                              />
                              <Typography variant="body2" fontWeight="bold">
                                {employee?.employee_name}
                              </Typography>
                            </Box>
                          );
                        case "ID":
                          return employee?.unique_id;
                        case "Department":
                          return employee?.department;
                        case "Designation":
                          return employee?.designation;
                        case "Line Manager":
                          return employee?.line_manager || "--";
                        case "Phone Number":
                          return employee?.phone_number || "--";
                        case "Email":
                          return employee?.email || "--";
                        case "Work Location":
                          return employee?.work_location || "--";
                        case "Check In Time":
                          return employee?.check_in_time || "--";
                        case "Check Out Time":
                          return employee?.check_out_time || "--";
                        default:
                          return null;
                      }
                    })();
                    return (
                      <TableCell key={col} sx={{ p: 1, border: "none" , whiteSpace: "nowrap" , textAlign: "center"}}>
                        {value}
                      </TableCell>
                    );
                  })}
                </TableRow>
              ))}
            </TableBody>
              ) : (
                <TableBody>
                  <TableRow>
                    <TableCell
                      colSpan={selectedColumns.length}
                      sx={{ textAlign: "center", py: 3 }}
                    >
                      No records found
                    </TableCell>
                  </TableRow>
                </TableBody>
              )
            }
          </Table>
        </TableContainer>
        <Box className="flex justify-end p-3">Total Record : {total}</Box>
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleClose}
        >
          {defaultColumns.map((col) => (
            <MenuItem key={col}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={selectedColumns.includes(col)}
                    onChange={() => handleToggleColumn(col)}
                  />
                }
                label={col}
              />
            </MenuItem>
          ))}
          <MenuItem>
            <Button variant="contained" fullWidth onClick={handleDownload}>
              Export Excel
            </Button>
          </MenuItem>
        </Menu>
      </Box>
    </Modal>
  );
};

export default ReusableModal;
