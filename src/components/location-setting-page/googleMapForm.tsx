
import { GoogleLocation, RadiusInfo } from "@/interface/schedule";
import {
    Autocomplete,
    Circle,
    GoogleMap,
    Marker,
} from "@react-google-maps/api";
import { Input, Tag } from "antd";
import React, { useRef, useState } from "react";
const mapContainerStyle = {
    width: "100%",
    height: "400px",
};
const center = {
    lat: 23.8103,
    lng: 90.4125,
};

interface Props {
    locations: GoogleLocation[];
    radiusInfo: RadiusInfo | undefined;
    setLocations: React.Dispatch<React.SetStateAction<GoogleLocation[]>>;
}
const GoogleMapForm = ({ locations, setLocations, radiusInfo }: Props) => {
    const mapRef = useRef<any>(null);
    const [autocomplete, setAutocomplete] = useState<any>(null);
    const [mapKey, setMapKey] = useState<any>(0); // This key will force re-rendering map
    const removeLocation = (index: number) => {
        setLocations((prev) => {
            const newLocations = prev.filter((_, i) => i !== index);
            return newLocations;
        });
        setMapKey((prevKey: any) => prevKey + 1);
    };

    const getGeocodedAddress = (lat: number, lng: number) => {
        const geocoder = new window.google.maps.Geocoder();
        geocoder.geocode({ location: { lat, lng } }, (results, status) => {
            if (status === "OK" && results?.[0]) {
                const address = results[0].formatted_address;
                setLocations((prev) => [...prev, { lat, lng, address }]);
            } else {
                console.error("Geocoder failed: ", status);
            }
        });
    };
    const onMapClick = (event: google.maps.MapMouseEvent) => {
        const lat = event.latLng?.lat();
        const lng = event.latLng?.lng();

        if (lat && lng) {
            // Get Address and Add to Locations
            getGeocodedAddress(lat, lng);
        }
    };

    const handlePlaceSelect = () => {
        if (autocomplete) {
            const place = autocomplete.getPlace();
            const location = place.geometry?.location;

            if (location) {
                const lat = location.lat();
                const lng = location.lng();

                // Get Address and Add to Locations
                getGeocodedAddress(lat, lng);
                mapRef.current?.panTo({ lat, lng });
            }
        }
    };

    return (
        <div className="space-y-2">
            <label className="font-semibold">Search Location</label>
            <div className="relative rounded-lg bg-slate-100 p-1">
                {/* Google Map */}
                <GoogleMap
                    key={mapKey}
                    mapContainerStyle={mapContainerStyle}
                    center={center}
                    zoom={15}
                    onClick={onMapClick}
                    onLoad={(map: google.maps.Map) => {
                        mapRef.current = map;
                    }}
                >
                    {locations.length > 0 ? (
                        locations.map((location, index) => (
                            <React.Fragment key={index}>
                                {/* Circle around the marker */}
                                <Circle
                                    center={{
                                        lat: location.lat,
                                        lng: location.lng,
                                    }}
                                    radius={radiusInfo?.default_radius} // 500 meters
                                    options={{
                                        fillColor: "red",
                                        fillOpacity: 0.05, // Adjust opacity as needed
                                        strokeColor: "red",
                                        strokeOpacity: 0.5,
                                        strokeWeight: 1,
                                    }}
                                />

                                <Marker
                                    position={{
                                        lat: location.lat,
                                        lng: location.lng,
                                    }}
                                />
                            </React.Fragment>
                        ))
                    ) : (
                        <></>
                    )}
                </GoogleMap>
                <div className="absolute left-1/2 top-0 w-1/2 -translate-x-1/2 transform rounded-xl bg-transparent py-3">
                    <Autocomplete
                        onLoad={(ac) => setAutocomplete(ac)}
                        onPlaceChanged={handlePlaceSelect}
                    >
                        <Input
                            placeholder="Search by Google Map"
                            className="w-full rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </Autocomplete>
                </div>
            </div>
            <div className="mt-4">
                {locations.map((location: GoogleLocation, index: number) => (
                    <Tag
                        key={location.address}
                        closable
                        onClose={() => removeLocation(index)}
                        className="mb-2 block"
                    >
                        <strong>{`Address ${index + 1}:`}</strong>{" "}
                        {location.address}
                    </Tag>
                ))}
            </div>
        </div>
    );
};

export default GoogleMapForm;
