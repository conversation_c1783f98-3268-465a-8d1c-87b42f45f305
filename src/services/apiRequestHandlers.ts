import { AxiosResponse } from "axios";
import { API, APIForWorkspace } from "./apiService";

// ==================== WORKSPACE API HANDLERS ====================
// These use the APIForWorkspace instance for workspace-related endpoints

export const getRequest = async <R>(url: string): Promise<R> => {
    const response: AxiosResponse<R> = await APIForWorkspace.get(url);
    return response.data;
};

export const postRequest = async <T, R>(url: string, data: T): Promise<R> => {
    const response: AxiosResponse<R> = await APIForWorkspace.post(url, data);
    return response.data;
};

export const postRequestFormData = async (
    url: string,
    data: FormData
): Promise<any> => {
    const response: AxiosResponse<any> = await APIForWorkspace.post(url, data, {
        headers: {
            "Content-Type": "multipart/form-data",
        },
    });
    return response.data;
};

export const patchRequest = async <T, R>(url: string, data: T): Promise<R> => {
    const response: AxiosResponse<R> = await APIForWorkspace.patch(url, data);
    return response.data;
};

export const putRequest = async <T, R>(url: string, data: T): Promise<R> => {
    const response: AxiosResponse<R> = await APIForWorkspace.put(url, data);
    return response.data;
};

export const getRequestWithParams = async <R>(
    url: string,
    params: Record<string, any>,
    config: Record<string, any> = {}
): Promise<R> => {
    const response = await APIForWorkspace.get(url, { params, ...config });
    return response.data;
};

export const deleteRequest = async <R>(url: string): Promise<R> => {
    const response: AxiosResponse<R> = await APIForWorkspace.delete(url);
    return response.data;
};

// ==================== REPORT API HANDLERS ====================
// These use the API instance for report-related endpoints

export const getReportRequest = async <R>(url: string): Promise<R> => {
    const response: AxiosResponse<R> = await API.get(url);
    return response.data;
};

export const postReportRequest = async <T, R>(url: string, data: T): Promise<R> => {
    const response: AxiosResponse<R> = await API.post(url, data);
    return response.data;
};

export const patchReportRequest = async <T, R>(url: string, data: T): Promise<R> => {
    const response: AxiosResponse<R> = await API.patch(url, data);
    return response.data;
};

export const putReportRequest = async <T, R>(url: string, data: T): Promise<R> => {
    const response: AxiosResponse<R> = await API.put(url, data);
    return response.data;
};

export const deleteReportRequest = async <R>(url: string): Promise<R> => {
    const response: AxiosResponse<R> = await API.delete(url);
    return response.data;
};
