import React, { useEffect, useState } from "react";
import { LineChart } from "@mui/x-charts/LineChart";
import { useEmployeeContext } from "@/store/EmployeeContext";
import { getRequest } from "@/services/apiRequestHandlers";
import { API_ROUTE } from "@/constants/api-routes";
import { Typography } from "@mui/material";
const LeaveGraph = () => {
  const [leaveData, setLeaveData] = useState<any[]>([]);
  const { employeeId } = useEmployeeContext();
  const getEmployeeLeaveDetails = async (employeeId: string) => {
    const userId = localStorage.getItem("userEmployeeId");
    try {
      let response:any;
      if(employeeId==userId){
        response = await getRequest<{data: any[]}>(
          `${API_ROUTE.DASHBOARD_LEAVE_GRAPH}`
        );
      } else {
        response = await getRequest<{data: any[]}>(
          `${API_ROUTE.DASHBOARD_LEAVE_GRAPH}?employee_id=${employeeId}`
        );
      }
      if (response && response.data) {
        setLeaveData(response.data);
      }
    } catch (error) {
      console.error("Error fetching leave graph data:", error);
    }
  };

  useEffect(() => {
    getEmployeeLeaveDetails(employeeId);
  }, [employeeId]);

  return (
    <div className=" bg-white shadow-md w-full h-full text-[#063c7a]">
      <Typography className="text-lg !font-bold text-center p-4">
        Last 6 Months(Approved)
      </Typography>
      <LineChart
        xAxis={[
          { scaleType: "point", data: leaveData?.map((item) => item?.month) },
        ]}
        yAxis={[
          {
            min: 0,
            max: Math.max(...leaveData.map((item) => item.leaveCount || 0), 1),
          },
        ]}
        series={[
          {
            data: leaveData?.map((item) => item?.leaveCount),
            color: "#f97316",
          },
        ]}
        sx={{ boxShadow: 1 }}
        height={430}
      />
    </div>
  );
};

export default LeaveGraph;
