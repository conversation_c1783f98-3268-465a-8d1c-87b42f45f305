import React, { useEffect, useMemo, useState } from "react";
import {
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Tooltip,
  TableSortLabel,
} from "@mui/material";
import { AccessTime, AlarmOn } from "@mui/icons-material";
import AddAlarmIcon from "@mui/icons-material/AddAlarm";
import { MdRunningWithErrors, MdTimerOff } from "react-icons/md";
import dayjs from "dayjs";
import { getRequest } from "@/services/apiRequestHandlers";
import { API_ROUTE } from "@/constants/api-routes";
import { useEmployeeContext } from "@/store/EmployeeContext";
import SwapVertIcon from "@mui/icons-material/SwapVert";
interface TimeCardProps {
  flipMonthIndex: number;
  currentYear: number;
  type: string;
}

const TimeCard: React.FC<TimeCardProps> = ({
  flipMonthIndex,
  currentYear,
  type,
}) => {
  const { employeeId } = useEmployeeContext();
  const [data, setData] = useState<any[]>([]);
  const [order, setOrder] = useState<"asc" | "desc">("asc");
  const [total, setTotal] = useState<any>();

  const handleSort = () => {
    setOrder((prev) => (prev === "asc" ? "desc" : "asc"));
  };

  const sortedData = useMemo(() => {
    return [...data].sort((a, b) => {
      const dateA = new Date(a.date).getTime();
      const dateB = new Date(b.date).getTime();
      return order === "asc" ? dateA - dateB : dateB - dateA;
    });
  }, [data, order]);

  const getData = async (
    year: number,
    from: number,
    to: number,
    employeeId: string
  ) => {
    const userId = localStorage.getItem("userEmployeeId");
    try {
      let response: any;
      if (employeeId == userId) {
        response = await getRequest<{data: {data: any[], extra_working_hour: string}}>(
          `${API_ROUTE.DASHBOARD_MY_WORKSPACE_DATA}?year=${year}&from_month=${from}&to_month=${to}`
        );
      } else {
        response = await getRequest<{data: {data: any[], extra_working_hour: string}}>(
          `${API_ROUTE.DASHBOARD_MY_WORKSPACE_DATA}?year=${year}&from_month=${from}&to_month=${to}&employee_id=${employeeId}`
        );
      }

      if (response && response.data) {
        setData(response.data.data);
        setTotal(response.data.extra_working_hour);
      }
    } catch (error) {
      console.error("Error fetching time card data:", error);
    }
  };

  useEffect(() => {
    if (employeeId) {
      getData(currentYear, flipMonthIndex + 1, flipMonthIndex + 1, employeeId);
    }
  }, [flipMonthIndex, employeeId]);

  return (
    <div
      className={`shadow-none border border-gray-200 overflow-auto ${
        type === "timeCard" ? "min-h-[500px]" : "h-[395px]"
      }`}
    >
      <Table size="small">
        <TableHead className="sticky top-0 z-10 bg-white">
          <TableRow>
            <TableCell
              className="!font-bold bg-gray-50 "
              sx={{ fontSize: "12px", whiteSpace: "nowrap", border: "none" }}
            >
              <TableSortLabel
                active
                direction={order}
                onClick={handleSort}
                IconComponent={SwapVertIcon}
              >
                <span className="text-gray-400"> Date</span>
              </TableSortLabel>
            </TableCell>

            <TableCell
              className="!font-bold bg-gray-50"
              sx={{ fontSize: "12px", whiteSpace: "nowrap", border: "none" }}
            >
              <Tooltip title="Check In / Out">
                <span className="flex items-center gap-1">
                  <AccessTime fontSize="small" className="text-gray-400" />{" "}
                  <span className="text-gray-400">In / Out</span>
                </span>
              </Tooltip>
            </TableCell>

            <TableCell
              className="!font-bold bg-gray-50"
              sx={{ fontSize: "12px", whiteSpace: "nowrap", border: "none" }}
            >
              <Tooltip title="Working Hour">
                <span className="flex items-center gap-1">
                  <AlarmOn fontSize="small" className="text-gray-400" />{" "}
                  <span className="text-gray-400">WH</span>
                </span>
              </Tooltip>
            </TableCell>

            <TableCell
              className="!font-bold bg-gray-50"
              sx={{ fontSize: "12px", whiteSpace: "nowrap", border: "none" }}
            >
              <Tooltip title="Extra Working Hour">
                <span className="flex items-center gap-1">
                  <AddAlarmIcon fontSize="small" className="text-gray-400" />{" "}
                  <span className="text-gray-400">{`EWH (${
                    total ? total : "0h 0m"
                  })`}</span>
                  <span></span>
                </span>
              </Tooltip>
            </TableCell>

            <TableCell
              className="!font-bold bg-gray-50"
              sx={{ fontSize: "12px", whiteSpace: "nowrap", border: "none" }}
            >
              <Tooltip title="Late Time">
                <span className="flex items-center gap-1">
                  <MdTimerOff fontSize="medium" className="text-gray-400" />{" "}
                  <span className="text-gray-400">LT </span>
                </span>
              </Tooltip>
            </TableCell>

            <TableCell
              className="!font-bold bg-gray-50"
              sx={{ fontSize: "12px", whiteSpace: "nowrap", border: "none" }}
            >
              <Tooltip title="Early Exit Time">
                <span className="flex items-center gap-1">
                  <MdRunningWithErrors
                    fontSize="small"
                    className="text-gray-400"
                  />{" "}
                  <span className="text-gray-400">ET </span>
                </span>
              </Tooltip>
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {sortedData?.map((row, idx) => {
            const isLate = row?.late_status === "Yes";
            const isEarly = row?.early_status === "Yes";
            const isbelowThresholdHour =
              row?.working_time < 9.2 && row?.status === "Present" && !row.half_day_leave_status;
            const isWeekend = ["fri", "sat"].includes(
              row?.week_day?.toLowerCase()
            );

            return (
              <TableRow
                key={idx}
                className={`${
                  isbelowThresholdHour ? "bg-cyan-50 !text-sky-800" : "hover:bg-gray-50"
                }`}
                sx={{
                  borderBottom: "1px solid #e5e7eb",
                  "& td": {
                    border: "none",
                  },
                }}
              >
                <TableCell
                  className={`border border-gray-300 ${
                    isWeekend ? "!text-indigo-600" : "!text-black"
                  }`}
                  sx={{ fontSize: "12px", whiteSpace: "nowrap" }}
                >
                  <span
                    className={`inline-block px-2 py-0.5 rounded-full text-xs mr-1 border ${
                      isWeekend ? "!border-indigo-400" : "border-gray-300"
                    }`}
                  >
                    {row.week_day}
                  </span>
                  {dayjs(row?.date).format("D MMM")}
                </TableCell>
                <TableCell
                  className="border border-gray-300"
                  sx={{ fontSize: "12px", whiteSpace: "nowrap" }}
                >
                  <div
                    className={`${isLate ? "text-red-600 font-semibold" : ""}`}
                  >
                    {row?.check_in || "--"}
                  </div>
                  <div
                    className={`${
                      isEarly ? "text-blue-600 font-semibold" : ""
                    }`}
                  >
                    {row?.check_out || "--"}
                  </div>
                </TableCell>

                <TableCell
                  className="border border-gray-300"
                  sx={{ fontSize: "12px", whiteSpace: "nowrap" }}
                >
                  {row?.working_hours || "--"}
                </TableCell>

                <TableCell
                  className="border border-gray-300"
                  sx={{ fontSize: "12px", whiteSpace: "nowrap" }}
                >
                  {row?.extra_working_hour || "--"}
                </TableCell>

                <TableCell
                  className="border border-gray-300"
                  sx={{ fontSize: "12px", whiteSpace: "nowrap" }}
                >
                  {row?.late_time || "--"}
                </TableCell>

                <TableCell
                  className="border border-gray-300"
                  sx={{ fontSize: "12px", whiteSpace: "nowrap" }}
                >
                  {row?.early_exit_time || "--"}
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
};

export default TimeCard;
