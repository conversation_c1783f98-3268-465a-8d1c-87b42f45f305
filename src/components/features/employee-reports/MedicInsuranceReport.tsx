import { capitalizeFirstLetter } from "@/utils/helper";
import { Workbook } from "exceljs";

export const medicalInsuranceReport = async (
  data: any,
  reportName: any,
  company: any,
  departmentName: any,
  companyId: any
) => {

  const workbook = new Workbook();
  const worksheet = workbook.addWorksheet("Medical Insurance");

  // Merging cells A2:S2 for the company name (updated range due to column removal)
  worksheet.mergeCells("A2:S2");
  const companyName = worksheet.getRow(2);
  companyName.height = 25;
  companyName.getCell(1).value = reportName + " of " + company;
  companyName.getCell(1).font = { size: 14, bold: true };
  companyName.getCell(1).alignment = {
    vertical: "middle",
    horizontal: "center",
  };

  // Merging cells A3:S3 for the report name (updated range due to column removal)
  worksheet.mergeCells("A3:S3");
  const fullName = worksheet.getRow(3);
  fullName.getCell(1).value = "Medical Insurance Report";
  fullName.getCell(1).font = { size: 11, bold: true };
  fullName.getCell(1).alignment = {
    vertical: "middle",
    horizontal: "center",
  };

  // Define headers (removed "Employee's Name" - column 5)
  const headers = [
    "SL. No. (EID)",
    "SL. No. (Sub ID)",
    "Employee ID",
    "Sub ID",
    "Name of Member",
    "Relationship",
    "Gender",
    "Marital Status",
    "Designation",
    "Department/ Work Area",
    "DOB",
    "Age",
    "Employee's Mobile Number",
    "Employee's e-mail",
    "Employee's Name in Bank Account",
    "Employee's A/C No.",
    "Bank Name",
    "Branch Name",
    "Routing No.",
  ];

  // Add headers to row 5
  headers.forEach((header, index) => {
    const col = index + 1;
    const cell = worksheet.getCell(5, col);
    cell.value = header;
    cell.font = { bold: true, size: 11 };
    cell.alignment = {
      horizontal: "center",
      vertical: "middle",
      wrapText: true,
    };
    cell.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "B3E5DC" },
    };
    cell.border = {
      top: { style: "thin" },
      left: { style: "thin" },
      bottom: { style: "thin" },
      right: { style: "thin" },
    };
    worksheet.getColumn(col).width = 20;
  });

  worksheet.getRow(5).height = 40;

  let currentRowIndex = 6; // Start from row 6, as row 5 is for headers
  let globalSubId = 1;

  data.forEach((item: any, index: number) => {
    const eid = index + 1;

    // Store employee information for dependents
    const employeeInfo = {
      mobile: item?.work_information?.work_mobile || "",
      email: item?.work_information?.email || "",
      accountName: item?.bank_information?.account_name || "",
      accountNo: item?.bank_information?.account_no || "",
      bankName: item?.bank_information?.bank_name || "",
      branchName: item?.bank_information?.branch_name || "",
      routeName: item?.bank_information?.route_name || ""
    };

    // --- Main Employee Row ---
    const mainRow = worksheet.getRow(currentRowIndex++);
    mainRow.getCell(1).value = eid; // SL. No. (EID)
    mainRow.getCell(2).value = globalSubId++; // Sub ID = 1 for the employee row
    mainRow.getCell(3).value = item?.work_information?.employee_id || "";
    mainRow.getCell(4).value = `${item?.work_information?.employee_id.split("-")[1]}-0` || "";
    mainRow.getCell(5).value = item?.work_information?.name || ""; // Name of Member
    mainRow.getCell(6).value = "Self"; // Relationship
    mainRow.getCell(7).value =
      capitalizeFirstLetter(item?.work_information?.gender) || "";
    mainRow.getCell(8).value = capitalizeFirstLetter(item?.work_information?.maritial_status) || "";
    mainRow.getCell(9).value = item?.work_information?.designation || "";
    mainRow.getCell(10).value = item?.work_information?.department || "";
    mainRow.getCell(11).value = item?.work_information?.dob || "";
    mainRow.getCell(12).value = item?.work_information?.age || "";
    mainRow.getCell(13).value = employeeInfo.mobile;
    mainRow.getCell(14).value = employeeInfo.email;
    mainRow.getCell(15).value = employeeInfo.accountName;
    mainRow.getCell(16).value = employeeInfo.accountNo;
    mainRow.getCell(17).value = employeeInfo.bankName;
    mainRow.getCell(18).value = employeeInfo.branchName;
    mainRow.getCell(19).value = employeeInfo.routeName;

    mainRow.alignment = { horizontal: "center", vertical: "middle" };

    // --- Spouse Rows ---
    item.spouse_information?.forEach((spouse: any, sIndex: number) => {
      const spouseRow = worksheet.getRow(currentRowIndex++);

      // Calculate spouse age if DOB is available
      let spouseAge = "";
      if (spouse.spouse_dob) {
        try {
          const dobDate = new Date(spouse.spouse_dob);
          const today = new Date();

          // Check if the date is valid
          if (!isNaN(dobDate.getTime())) {
            let age = today.getFullYear() - dobDate.getFullYear();
            const monthDiff = today.getMonth() - dobDate.getMonth();

            // Adjust age if birthday hasn't occurred this year
            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dobDate.getDate())) {
              age--;
            }

            spouseAge = age >= 0 ? age.toString() : "";
          }
        } catch (error) {
          // If date parsing fails, leave age empty
          spouseAge = "";
        }
      }

      spouseRow.getCell(1).value = "";
      spouseRow.getCell(2).value = globalSubId++; // Sub ID 2+ for spouse
      spouseRow.getCell(3).value = "";
      spouseRow.getCell(4).value =
        `${item?.work_information?.employee_id.split("-")[1]}-${sIndex + 1}` || "";
      spouseRow.getCell(5).value = spouse.spouse_name || ""; // Name of Member
      spouseRow.getCell(6).value = "Spouse"; // Relationship
      spouseRow.getCell(7).value = spouse.spouse_gender || "";
      spouseRow.getCell(8).value = "Married";
      spouseRow.getCell(9).value = "Dependent"; // Designation for Spouse
      spouseRow.getCell(10).value = "Dependent";
      spouseRow.getCell(11).value = spouse?.spouse_dob || "";
      spouseRow.getCell(12).value = spouseAge;
      // Use employee's information for dependent
      spouseRow.getCell(13).value = employeeInfo.mobile;
      spouseRow.getCell(14).value = employeeInfo.email;
      spouseRow.getCell(15).value = employeeInfo.accountName;
      spouseRow.getCell(16).value = employeeInfo.accountNo;
      spouseRow.getCell(17).value = employeeInfo.bankName;
      spouseRow.getCell(18).value = employeeInfo.branchName;
      spouseRow.getCell(19).value = employeeInfo.routeName;

      spouseRow.alignment = { horizontal: "center", vertical: "middle" };
    });

    // --- Children Rows ---
    item.children_information?.slice(0, 2).forEach((child: any, cIndex: number) => {
      const childRow = worksheet.getRow(currentRowIndex++);

      // Determine relationship based on gender
      const childGender = capitalizeFirstLetter(child.child_gender) || "";
      let relationship = "Child"; // Default fallback
      if (childGender.toLowerCase() === "male") {
        relationship = "Son";
      } else if (childGender.toLowerCase() === "female") {
        relationship = "Daughter";
      }

      // Calculate child age if DOB is available
      let childAge = "";
      if (child.child_dob) {
        try {
          const dobDate = new Date(child.child_dob);
          const today = new Date();

          // Check if the date is valid
          if (!isNaN(dobDate.getTime())) {
            let age = today.getFullYear() - dobDate.getFullYear();
            const monthDiff = today.getMonth() - dobDate.getMonth();

            // Adjust age if birthday hasn't occurred this year
            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dobDate.getDate())) {
              age--;
            }

            childAge = age >= 0 ? age.toString() : "";
          }
        } catch (error) {
          // If date parsing fails, leave age empty
          childAge = "";
        }
      }

      childRow.getCell(1).value = "";
      childRow.getCell(2).value = globalSubId++; // Sub ID continues after spouse
      childRow.getCell(3).value = "";
      childRow.getCell(4).value =
        `${item?.work_information?.employee_id.split("-")[1]}-${
          (item.spouse_information?.length || 0) + cIndex + 1
        }` || "";
      childRow.getCell(5).value = child.children_name || ""; // Name of Member
      childRow.getCell(6).value = relationship; // Relationship based on gender
      childRow.getCell(7).value = childGender;
      childRow.getCell(8).value = "";
      childRow.getCell(9).value = "Dependent"; // Designation for Child
      childRow.getCell(10).value = "Dependent";
      childRow.getCell(11).value = child.child_dob || "";
      childRow.getCell(12).value = childAge; // Optional: calculate age from DOB
      // Use employee's information for dependent
      childRow.getCell(13).value = employeeInfo.mobile;
      childRow.getCell(14).value = employeeInfo.email;
      childRow.getCell(15).value = employeeInfo.accountName;
      childRow.getCell(16).value = employeeInfo.accountNo;
      childRow.getCell(17).value = employeeInfo.bankName;
      childRow.getCell(18).value = employeeInfo.branchName;
      childRow.getCell(19).value = employeeInfo.routeName;

      childRow.alignment = { horizontal: "center", vertical: "middle" };
    });
  });

  // Save the file
  const buffer = await workbook.xlsx.writeBuffer();

  // Create a Blob from the buffer and trigger download using the browser's built-in API
  const blob = new Blob([buffer], { type: "application/octet-stream" });

  // Create a link element
  const link = document.createElement("a");

  // Create an object URL from the Blob
  const url = window.URL.createObjectURL(blob);

  // Set download attributes
  link.href = url;
  link.download = "Medical-Insurance-Report.xlsx";

  // Trigger a click event to download the file
  link.click();

  // Clean up the object URL after download
  window.URL.revokeObjectURL(url);
};