import { FAILED, SUCCESS } from "@/utils/common";
import { errorToast } from "@/utils/helper";
import axios from "axios";

export const TokenVerificationService = {
    async tokenVerification(token: string) {
        const baseUrl = process.env.NEXT_PUBLIC_API_URL_FOR_REPORT;
        try {
            const { data } = await axios.post(`${baseUrl}/api/access-token-verification`, token);

            return {
                status: SUCCESS,
                data: data,
            };
        } catch (e: any) {
            if (e.response) {
                const responseJson = JSON.parse(e.response.request.responseText);
                const errorDetail = responseJson.detail;
                errorToast(errorDetail);
            } else {
                console.error("Error:", e.message);
            }
            return {
                status: FAILED,
                message: "Something wrong!!",
                error: e,
            };
        }
    },

    async regenerateToken(refreshToken: string) {
        const baseUrl = process.env.NEXT_PUBLIC_API_URL_FOR_REPORT;
        try {
            const { data } = await axios.post(`${baseUrl}/api/regenerate-token/?refresh_token=${refreshToken}`, refreshToken);

            return {
                status: SUCCESS,
                data: data,
            };
        } catch (e: any) {
            if (e.response) {
                const responseJson = JSON.parse(e.response.request.responseText);
                const errorDetail = responseJson.detail;
                errorToast(errorDetail);
            } else {
                console.error("Error:", e.message);
            }
            return {
                status: FAILED,
                message: "Something wrong!!",
                error: e,
            };
        }
    },

    async regenerateTokenForWorkspace(refreshToken: string) {
        const baseUrl = process.env.NEXT_PUBLIC_API_URL_FOR_WORKSPACE; 
        try {
            const { data } = await axios.post(`${baseUrl}/api/v1/auth/refresh`, {
                refresh_token: refreshToken,
              });
             
             
            return {
                status: SUCCESS,
                data: data,
            };
        } catch (e: any) {
            if (e.response) {
                const responseJson = JSON.parse(e.response.request.responseText);
                const errorDetail = responseJson.detail;
                errorToast(errorDetail);
            } else {
                console.error("Error:", e.message);
            }
            return {
                status: FAILED,
                message: "Something wrong!!",
                error: e,
            };
        }
    },
};
