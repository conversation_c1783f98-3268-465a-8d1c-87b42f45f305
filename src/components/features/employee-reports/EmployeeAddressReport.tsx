import { dateTimeFormatForExcel, getComanyLogoById } from "@/utils/helper";
import ExcelJS from "exceljs";
const headers = [
  "Employee ID",
  "Employee Name",
  "Department",
  "Location",
  "Division",
  "Present Address",
  "Permanent Address",
];
export async function EmployeeAddressReport(
  data: any,
  reportName: any,
  company: any,
  departmentName: any,
  companyId: any
): Promise<void> {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet("Attendance");

  const fetchLogoAndGenerateExcel = async (): Promise<string> => {
    if (companyId) {
      const data = await getComanyLogoById(companyId);
      return data;
    } else {
      console.log("No Comapny Id");
      return "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAAAAAAAD/4QA6RXhpZgAATU0AKgAAAAgAA1IBAAAAAAIAAABSAwABAAAAAQAAABoBBQABAAAAkgAAABsBBQABAAAAmgAAACgBAwABAAAAAgAAADEBAgAIAAAAAQAAAGmHBAABAAAA2gAAACkBAwABAAAAAQAAACmJAABIAAAAQAAAGqJAAABAAAAtAAAAGyJAABIAAAAAQAAAGmKAAABAAAAuAAAAGyNAAABAAAAwAAAAKACAAQAAAABAAAJKJKS0BHUk9VUAAKAAAAABAAAABIdpAAQAAAABAAAAKpAAAAASAAAAAEAAABKSAAAAAAAABAAAGJydAAEAAAAeAAAAEJydAAEAAAAiAAAAE5ydAAEAAAAhAAAAFJydAAEAAAAgAAAAFpYWVogAAAAAAAABAAAABW9iaiB4AAEAAAAeAAAAFBob3R0AAEAAAAKAAAAEhpc3RvcnkAAQAAAAMAAAAQAAAAEAAAAAEgAAAABAAAAAQAAAAEAAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAAQABADAREAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwDzKwkmrYopCoAMaYopSYoopWaiinGkxiqMCMakDGkxiqAwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQwGmMcZpDAaYxhmkMBozGGaQ";
    }
  };

  worksheet.mergeCells(`A1:L1`);
  const logoRow = worksheet.getRow(1);
  let companyImage = await fetchLogoAndGenerateExcel();

  let companyLogo = workbook.addImage({
    base64: companyImage,
    extension: "jpeg",
  });

  worksheet.addImage(companyLogo, {
    tl: { col: 0, row: 0 },
    ext: { width: 200, height: 100 },
  });
  logoRow.height = 90;

  worksheet.mergeCells(`A2:L3`);
  const headerRow = worksheet.getRow(2);
  headerRow.height = 25;
  headerRow.getCell(1).value = reportName + " of " + company;
  headerRow.getCell(1).font = { size: 11, bold: true };
  headerRow.getCell(1).alignment = { horizontal: "center", vertical: "middle" };
  // date
  const today = new Date();

  worksheet.mergeCells(`A4:L4`);
  const printDate = worksheet.getRow(4);
  printDate.getCell(1).value = `Print Date: ${dateTimeFormatForExcel(today)}`;
  printDate.getCell(1).font = { size: 11 };
  printDate.getCell(1).alignment = { horizontal: "center", vertical: "middle" };

  worksheet.mergeCells(`A5:L5`);
  const department = worksheet.getRow(5);
  department.getCell(1).value = departmentName
    ? `Department of ${departmentName}`
    : "";
  department.getCell(1).font = { size: 11 };
  department.getCell(1).alignment = {
    horizontal: "center",
    vertical: "middle",
  };

  worksheet.addRow(headers);
  headers.forEach((item, i) => {
    if (item === "Permanent Address" || item === "Present Address") {
      worksheet.columns[i].width = 60;
    } else {
      worksheet.columns[i].width = 25;
    }
  });

  let count = 0;

  data[0]?.response?.forEach((value: any, index: any) => {
    worksheet.getCell(count + 7, 1).value = value?.unique_id;
    worksheet.getCell(count + 7, 2).value = value?.employee_name;
    worksheet.getCell(count + 7, 3).value = value?.department
      ? value.department
      : "";
    worksheet.getCell(count + 7, 4).value = value?.work_location;
    worksheet.getCell(count + 7, 5).value = value?.division;

    if (value?.present_country == "Bangladesh") {
      // Construct the present address string
      let presentAddress = [
        value?.present_village,
        value?.present_upazila,
        value?.present_district,
        value?.present_division,
        value?.present_country,
      ]
        .filter(Boolean)
        .join(",");

      worksheet.getCell(count + 7, 6).value = presentAddress;

      // Construct the permanent address string
      let permanentAddress = [
        value?.permanent_village,
        value?.permanent_upazila,
        value?.permanent_district,
        value?.permanent_division,
        value?.permanent_country,
      ]
        .filter(Boolean)
        .join(",");

      worksheet.getCell(count + 7, 7).value = permanentAddress;
    } else {
      // Construct the present address string
      let presentAddress = [
        value?.present_province,
        value?.present_state,
        value?.present_city,
        value?.present_country,
      ]
        .filter(Boolean)
        .join(",");

      worksheet.getCell(count + 7, 6).value = presentAddress;

      // Construct the permanent address string
      let permanentAddress = [
        value?.permanent_province,
        value?.permanent_state,
        value?.permanent_city,
        value?.permanent_country,
      ]
        .filter(Boolean)
        .join(",");

      worksheet.getCell(count + 7, 7).value = permanentAddress;
    }

    count += 1;
  });

  try {
    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer();

    // Trigger file download
    const blob = new Blob([buffer], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${reportName ? reportName : "Report"}.xlsx`;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
  } catch (e) {
    console.log(e);
  }
}
