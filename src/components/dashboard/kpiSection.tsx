import { KPI, Quarter } from "@/interface/dashboard-models";
import { getRequest } from "@/services/apiRequestHandlers";
import { API_ROUTE } from "@/constants/api-routes";
import Accordion from "@mui/material/Accordion";
import AccordionDetails from "@mui/material/AccordionDetails";
import AccordionSummary from "@mui/material/AccordionSummary";
import Typography from "@mui/material/Typography";
import React, { useEffect, useState } from "react";
import { MdExpandMore } from "react-icons/md";
import KpiGraph from "./kpiGraph";
import { useEmployeeContext } from "@/store/EmployeeContext";

const KpiSection = () => {
  const [expanded, setExpanded] = useState<any>();
  const [kpis, setKpis] = useState<KPI[]>([]);
  const { employeeId } = useEmployeeContext();
  const [rows, setRows] = useState(4);

  const getEmployeeAttendanceDetails = async (employee_id: string) => {
    const userId = localStorage.getItem("userEmployeeId");
    try {
      let response:any;
      if(employee_id==userId){
        response = await getRequest<{data: KPI[]}>(
          `${API_ROUTE.DASHBOARD_MY_WORKSPACE_KPI_DATA}`
        );
      } else {
        response = await getRequest<{data: KPI[]}>(
          `${API_ROUTE.DASHBOARD_MY_WORKSPACE_KPI_DATA}?employee_id=${employee_id}`
        );
      }
      if (response && response.data) {
        setKpis(response.data);
      }
    } catch (error) {
      console.error("Error fetching KPI data:", error);
    }
  };

  useEffect(() => {
    getEmployeeAttendanceDetails(employeeId);
  }, [employeeId]);

  useEffect(() => {
    const updateRows = () => {
      const width = window.innerWidth;
      if (width >= 1024) {
        setRows(2);
      } else {
        setRows(4);
      }
    };
    updateRows();
    window.addEventListener("resize", updateRows);

    return () => window.removeEventListener("resize", updateRows);
  }, []);
  const handleChange =
    (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
      setExpanded(isExpanded ? panel : false);
    };

  return (
    <div className="my-4 w-full">
      <h3 className="text-lg font-bold text-center pb-4">
        Performance Overview
      </h3>
      {/* {kpis?.length > 0 ? (
        <div className="flex flex-col lg:flex-row gap-2 justify-between">
          <div className="bg-white shadow-lg w-full max-h-screen overflow-auto flex-1">
            <div className="flex flex-col md:flex-row">
              <div className="w-full flex flex-col gap-4">
                {kpis?.map((kpi: KPI, i: number) => (
                  <div className="w-[80%] flex items-center flex-row space-x-10 px-4  ">

                    <div className="relative flex flex-col space-y-2">
                      {kpi.quarters.map((quar: Quarter, i: number) => (
                        <div key={i} className="relative flex items-center">

                          <div className="absolute right-[-40px] w-10 h-[1px] bg-gray-300"></div>

                          <Accordion
                            expanded={
                              expanded === (kpi?.id || 100) + (quar?.name || "")
                            }
                            onChange={handleChange(
                              (kpi?.id || 100) + (quar?.name || "")
                            )}
                            sx={{
                              width: { xs: "100%", md: "500px", lg: "850px" },
                              color: "#063c7a",
                            }}
                            className="!bg-gray-100"
                          >
                            <AccordionSummary
                              aria-controls="panel1bh-content"
                              id="panel1bh-header"
                              expandIcon={<MdExpandMore />}
                            >
                              <Typography
                                component="span"
                                sx={{
                                  width: { xs: "50%", sm: "75%", md: "65%" },
                                  flexShrink: 0,
                                  fontWeight: "bold",
                                }}
                              >
                                {quar.cto_marks
                                  ? "CTO & GCTO Markings"
                                  : quar?.name || "N/A"}
                              </Typography>
                              <Typography
                                component="span"
                                sx={{ color: "#063c7a" }}
                              >
                                {quar?.cto_marks ? (
                                  ""
                                ) : (
                                  <div className="flex flex-col lg:flex-row justify-center items-center gap-2">
                                    <p>
                                      Marks: {quar?.mark || 0} /{" "}
                                      {quar?.out_of || 0}
                                    </p>
                                    {quar?.status && (
                                      <p
                                        className={`${
                                          quar?.status === "Approved"
                                            ? "bg-green-600"
                                            : "bg-yellow-500"
                                        } text-white text-center font-semibold text-sm px-2 h-5`}
                                      >
                                        {quar?.status}
                                      </p>
                                    )}
                                  </div>
                                )}
                              </Typography>
                            </AccordionSummary>
                            <AccordionDetails>
                              <Typography>
                                {quar?.cto_marks ? (
                                  <div className="flex flex-row w-full justify-between">
                                    <p>
                                      <span className="font-semibold">
                                        CTO:{" "}
                                      </span>
                                      {quar?.cto_marks || "N/A"}
                                    </p>
                                    <p>
                                      <span className="font-semibold">
                                        GCTO:{" "}
                                      </span>
                                      {quar?.gtco_marks || "N/A"}
                                    </p>
                                  </div>
                                ) : (
                                  <div>
                                    <div className="flex flex-col lg:flex-row gap-2 lg:gap-0 w-full justify-between text-sm">
                                      <div className="flex flex-col gap-2">
                                        <p>
                                          <span className="font-semibold">
                                            Manager:{" "}
                                          </span>
                                          {quar?.approved_by_manager || "N/A"}
                                        </p>
                                        <table className="text-xs ">
                                          <thead>
                                            <th className="text-center">
                                              Technical
                                            </th>
                                            <th className="text-center">
                                              Behavioural
                                            </th>
                                          </thead>
                                          <tbody>
                                            <tr>
                                              <td className="text-center">
                                                {quar?.line_manager_technical_mark ||
                                                  "N/A"}
                                              </td>
                                              <td className="text-center">
                                                {quar?.line_manager_behavioural_mark ||
                                                  "N/A"}
                                              </td>
                                            </tr>
                                          </tbody>
                                        </table>
                                      </div>
                                      <div className="flex flex-col gap-2">
                                        <p>
                                          <span className="font-semibold">
                                            HR:{" "}
                                          </span>
                                          {quar?.approved_by_hr || "N/A"}
                                        </p>
                                        <table className="text-xs ">
                                          <thead>
                                            <th className="text-center">
                                              Behavioural
                                            </th>
                                          </thead>
                                          <tbody>
                                            <tr>
                                              <td className="text-center">
                                                {quar?.hr_behavioural_mark ||
                                                  "N/A"}
                                              </td>
                                            </tr>
                                          </tbody>
                                        </table>
                                      </div>
                                    </div>
                                    <div className="w-full text-xs mt-2">
                                      <h3 className="font-semibold text-sm text-center pb-1">
                                        Comments:{" "}
                                      </h3>
                                      <div className="flex flex-col gap-2">
                                        <div className="flex flex-col lg:flex-row lg:gap-2">
                                          <span className="underline w-32">
                                            Accomplishments:{" "}
                                          </span>
                                          <textarea
                                            className="w-full "
                                            rows={rows}
                                            readOnly
                                            value={
                                              quar?.lm_accomplishment_remark ||
                                              "N/A"
                                            }
                                          />
                                        </div>
                                        <div className="flex flex-col lg:flex-row lg:gap-2">
                                          <span className="underline w-32">
                                            Compilation Time Line:{" "}
                                          </span>
                                          <textarea
                                            className="w-full"
                                            rows={rows}
                                            readOnly
                                            value={
                                              quar?.lm_compilation_time_line ||
                                              "N/A"
                                            }
                                          />
                                        </div>
                                        <div className="flex flex-col lg:flex-row lg:gap-2">
                                          <span className="underline w-32">
                                            Improvement Areas:{" "}
                                          </span>
                                          <textarea
                                            className="w-full"
                                            rows={rows}
                                            readOnly
                                            value={
                                              quar?.lm_improvement_areas ||
                                              "N/A"
                                            }
                                          />
                                        </div>
                                        <div className="flex flex-col lg:flex-row lg:gap-2">
                                          <span className="underline w-32">
                                            Previous Improvement Updates:{" "}
                                          </span>
                                          <textarea
                                            className="w-full"
                                            rows={rows}
                                            readOnly
                                            value={
                                              quar?.lm_previous_improvement_update_remarks ||
                                              "N/A"
                                            }
                                          />
                                        </div>
                                        <div className="flex flex-col lg:flex-row lg:gap-2">
                                          <span className="underline w-32">
                                            Next Quarter Goal:{" "}
                                          </span>
                                          <textarea
                                            className="w-full"
                                            rows={rows}
                                            readOnly
                                            value={
                                              quar?.lm_next_quarter_goal_remark ||
                                              "N/A"
                                            }
                                          />
                                        </div>
                                        <div className="flex flex-col lg:flex-row lg:gap-2">
                                          <span className="underline w-32">
                                            Recovery Plan:{" "}
                                          </span>
                                          <textarea
                                            className="w-full"
                                            rows={rows}
                                            readOnly
                                            value={
                                              quar?.lm_recovery_plan_remarks ||
                                              "N/A"
                                            }
                                          />
                                        </div>
                                        <div className="flex flex-col lg:flex-row lg:gap-2">
                                          <span className="underline w-32">
                                            HO Remarks:{" "}
                                          </span>
                                          <textarea
                                            className="w-full"
                                            rows={rows}
                                            readOnly
                                            value={quar?.ho_comment || "N/A"}
                                          />
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                )}
                              </Typography>
                            </AccordionDetails>
                          </Accordion>
                        </div>
                      ))}
                    </div>



                    <div className="relative flex flex-col items-center h-[540px] sm:h-[440px] md:h-[430px] lg:h-[400px]">
                      <div className="absolute top-0 bottom-0 left-1/2 w-[1px] bg-gray-300"></div>
                    </div>


                    <div className="relative">
                      <div className="absolute left-[-40px] w-10 h-[1px] bg-gray-300 top-1/2"></div>

                      <Accordion
                        sx={{
                          width: "70px",
                          color: "#063c7a",
                        }}
                        className="!bg-gray-100 right-4"
                      >
                        <AccordionSummary>
                          <Typography>{kpi?.year}</Typography>
                        </AccordionSummary>
                      </Accordion>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
          <div className="w-full lg:w-[40%] flex-1">
            <KpiGraph graphData={kpis} />
          </div>
        </div>
      ) : (
        <div className="h-28 bg-white flex justify-center items-center shadow-md">
          No KPIs found
        </div>
      )} */}
      {kpis?.length > 0 ? (
        <div className="flex flex-col lg:flex-row gap-2 w-full overflow-x-auto">
          {/* Accordion Section (Left) */}
          <div className="bg-white shadow-lg w-full flex-1">
            <div className="flex flex-col md:flex-row">
              <div className="w-full flex flex-col gap-4 min-w-[1000px]">
                {kpis?.map((kpi: KPI, i: number) => (
                  <div
                    key={i}
                    className="w-full flex items-center space-x-10 px-4"
                  >
                    {/* Left Side: Primary Accordions */}
                    <div className="relative flex flex-col space-y-2">
                      {kpi.quarters.map((quar: Quarter, qIdx: number) => (
                        <div key={qIdx} className="relative flex items-center">
                          <div className="absolute right-[-40px] w-10 h-[1px] bg-gray-300"></div>

                          <Accordion
                            expanded={
                              expanded === (kpi?.id || 100) + (quar?.name || "")
                            }
                            onChange={handleChange(
                              (kpi?.id || 100) + (quar?.name || "")
                            )}
                            sx={{
                              width: { xs: "100%", md: "500px", lg: "850px" },
                              color: "#063c7a",
                            }}
                            className="!bg-gray-100"
                          >
                            <AccordionSummary
                              aria-controls="panel1bh-content"
                              id="panel1bh-header"
                              expandIcon={<MdExpandMore />}
                            >
                              <Typography
                                component="span"
                                sx={{
                                  width: { xs: "50%", sm: "75%", md: "65%" },
                                  flexShrink: 0,
                                  fontWeight: "bold",
                                }}
                              >
                                {quar.cto_marks
                                  ? "CTO & GCTO Markings"
                                  : quar?.name || "N/A"}
                              </Typography>
                              <Typography
                                component="span"
                                sx={{ color: "#063c7a" }}
                              >
                                {quar?.cto_marks ? (
                                  ""
                                ) : (
                                  <div className="flex flex-col lg:flex-row justify-center items-center gap-2">
                                    <p>
                                      Marks: {quar?.mark || 0} /{" "}
                                      {quar?.out_of || 0}
                                    </p>
                                    {quar?.status && (
                                      <p
                                        className={`${
                                          quar?.status === "Approved"
                                            ? "bg-green-600"
                                            : "bg-yellow-500"
                                        } text-white text-center font-semibold text-sm px-2 h-5`}
                                      >
                                        {quar?.status}
                                      </p>
                                    )}
                                  </div>
                                )}
                              </Typography>
                            </AccordionSummary>
                            <AccordionDetails>
                              <Typography>
                                {quar?.cto_marks ? (
                                  <div className="flex flex-row w-full justify-between">
                                    <p>
                                      <span className="font-semibold">
                                        CTO:{" "}
                                      </span>
                                      {quar?.cto_marks || "N/A"}
                                    </p>
                                    <p>
                                      <span className="font-semibold">
                                        GCTO:{" "}
                                      </span>
                                      {quar?.gtco_marks || "N/A"}
                                    </p>
                                  </div>
                                ) : (
                                  <div>
                                    <div className="flex flex-col lg:flex-row gap-2 lg:gap-0 w-full justify-between text-sm">
                                      <div className="flex flex-col gap-2">
                                        <p>
                                          <span className="font-semibold">
                                            Manager:{" "}
                                          </span>
                                          {quar?.approved_by_manager || "N/A"}
                                        </p>
                                        <table className="text-xs">
                                          <thead>
                                            <tr>
                                              <th className="text-center">
                                                Technical
                                              </th>
                                              <th className="text-center">
                                                Behavioural
                                              </th>
                                            </tr>
                                          </thead>
                                          <tbody>
                                            <tr>
                                              <td className="text-center">
                                                {quar?.line_manager_technical_mark ||
                                                  "N/A"}
                                              </td>
                                              <td className="text-center">
                                                {quar?.line_manager_behavioural_mark ||
                                                  "N/A"}
                                              </td>
                                            </tr>
                                          </tbody>
                                        </table>
                                      </div>
                                      <div className="flex flex-col gap-2">
                                        <p>
                                          <span className="font-semibold">
                                            HR:{" "}
                                          </span>
                                          {quar?.approved_by_hr || "N/A"}
                                        </p>
                                        <table className="text-xs">
                                          <thead>
                                            <tr>
                                              <th className="text-center">
                                                Behavioural
                                              </th>
                                            </tr>
                                          </thead>
                                          <tbody>
                                            <tr>
                                              <td className="text-center">
                                                {quar?.hr_behavioural_mark ||
                                                  "N/A"}
                                              </td>
                                            </tr>
                                          </tbody>
                                        </table>
                                      </div>
                                    </div>
                                    <div className="w-full text-xs mt-2">
                                      <h3 className="font-semibold text-sm text-center pb-1">
                                        Comments:
                                      </h3>
                                      <div className="flex flex-col gap-2">
                                        {[
                                          [
                                            "Accomplishments",
                                            "lm_accomplishment_remark",
                                          ],
                                          [
                                            "Compilation Time Line",
                                            "lm_compilation_time_line",
                                          ],
                                          [
                                            "Improvement Areas",
                                            "lm_improvement_areas",
                                          ],
                                          [
                                            "Previous Improvement Updates",
                                            "lm_previous_improvement_update_remarks",
                                          ],
                                          [
                                            "Next Quarter Goal",
                                            "lm_next_quarter_goal_remark",
                                          ],
                                          [
                                            "Recovery Plan",
                                            "lm_recovery_plan_remarks",
                                          ],
                                          ["HO Remarks", "ho_comment"],
                                        ].map(([label, key]) => (
                                          <div
                                            key={label}
                                            className="flex flex-col lg:flex-row lg:gap-2"
                                          >
                                            <span className="underline w-32">
                                              {label}:
                                            </span>
                                            <textarea
                                              className="w-full"
                                              rows={rows}
                                              readOnly
                                              value={
                                                (quar as any)?.[key] || "N/A"
                                              }
                                            />
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  </div>
                                )}
                              </Typography>
                            </AccordionDetails>
                          </Accordion>
                        </div>
                      ))}
                    </div>

                    {/* Middle Connecting Line */}
                    <div className="relative flex flex-col items-center h-[400px]">
                      <div className="absolute top-0 bottom-0 left-1/2 w-[1px] bg-gray-300"></div>
                    </div>

                    {/* Right Side Output Accordion */}
                    <div className="relative">
                      <div className="absolute left-[-40px] w-10 h-[1px] bg-gray-300 top-1/2"></div>
                      <Accordion
                        sx={{ width: "70px", color: "#063c7a" }}
                        className="!bg-gray-100 right-4"
                      >
                        <AccordionSummary>
                          <Typography>{kpi?.year}</Typography>
                        </AccordionSummary>
                      </Accordion>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* KPI Graph Section (Right) */}
          <div className="w-full lg:w-[40%] flex-1 min-w-[600px]">
            <div className="overflow-x-auto">
              <KpiGraph graphData={kpis} />
            </div>
          </div>
        </div>
      ) : (
        <div className="h-28 bg-white flex justify-center items-center shadow-md">
          No KPIs found
        </div>
      )}
    </div>
  );
};

export default KpiSection;
