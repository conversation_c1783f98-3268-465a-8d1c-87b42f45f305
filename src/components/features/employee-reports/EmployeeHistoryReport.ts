import {dateTimeFormatForExcel, getComanyLogoById} from "@/utils/helper";
import ExcelJS from "exceljs";

const headers = [
    "Employee ID",
    "Employee Name",
    "Department",
    "Contact",
    "Designation",
    "Organization",
    "Country",
    "Position",
    "Start Date",
    "End Date"
];

export async function EmployeeHistoryReport(
    data: any,
    reportName: any,
    company: any,
    department: any,
    companyId: any
): Promise<void> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Attendance");

    function getDayOfWeek(dateString: any) {
        // Create a Date object from the provided string
        const date = new Date(dateString);

        // Array of weekday names
        const weekdays = [
            "Sunday",
            "Monday",
            "Tuesday",
            "Wednesday",
            "Thursday",
            "Friday",
            "Saturday",
        ];

        // Get the day of the week (0-6 corresponds to Sunday-Saturday)
        const dayOfWeek = date.getDay();

        // Return the weekday name
        return weekdays[dayOfWeek];
    }

    const fetchLogoAndGenerateExcel = async (): Promise<string> => {
        if (companyId) {
            const data = await getComanyLogoById(companyId);
            return data;
        } else {
            console.log("No Comapny Id");
            return "data:image/jpeg;base64,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";
        }
    };

    worksheet.mergeCells(`A1:I1`);
    const logoRow = worksheet.getRow(1);
    let companyImage = await fetchLogoAndGenerateExcel();

    let companyLogo = workbook.addImage({
        base64: companyImage,
        extension: "jpeg",
    });

    worksheet.addImage(companyLogo, {
        tl: {col: 0, row: 0},
        ext: {width: 200, height: 100},
    });
    logoRow.height = 90;

    worksheet.mergeCells(`A2:I3`);
    const headerRow = worksheet.getRow(2);
    headerRow.height = 25;
    headerRow.getCell(1).value = reportName + " of " + company;
    headerRow.getCell(1).font = {size: 11, bold: true};
    headerRow.getCell(1).alignment = {horizontal: "center", vertical: "middle"};

    worksheet.mergeCells(`A4:I4`);
    const blankCell = worksheet.getRow(4);
    blankCell.getCell(1).font = {size: 11};
    blankCell.getCell(1).alignment = {horizontal: "center", vertical: "middle"};
    // form
    const today = new Date();

    worksheet.mergeCells(`A5:I5`);
    const printDate = worksheet.getRow(5);
    printDate.getCell(1).value = `Print Date: ${dateTimeFormatForExcel(today)}`;
    printDate.getCell(1).font = {size: 11};
    printDate.getCell(1).alignment = {horizontal: "center", vertical: "middle"};
    printDate.font = {bold: true};

    worksheet.mergeCells(`A6:I6`);
    const blankRow = worksheet.getRow(6);
    blankRow.getCell(1).value = ``;

    worksheet.addRow(headers);
    headers.forEach((item, i) => {
        worksheet.columns[i].width = 17;
    });

    let rowNo = 9;
    let colorPop: any = [];

    function range(start: number, end: number) {
        return Array.from({length: end - start + 1}, (_, i) => start + i);
    }

    data?.map((value: any, index: any) => {
        value?.response?.map((item: any, itemIndex: number) => {
            let rowLength = item?.job_history?.length;
            console.log(rowLength);

            worksheet.mergeCells(`A${rowNo}:A${rowNo + (rowLength - 1)}`);
            let employeeNameRow = worksheet.getRow(rowNo);
            employeeNameRow.getCell(1).value = item.unique_id;
            employeeNameRow.getCell(1).alignment = {
                horizontal: "center",
                vertical: "middle",
            };
            worksheet.mergeCells(`B${rowNo}:B${rowNo + (rowLength - 1)}`);
            let designationRow = worksheet.getRow(rowNo);
            designationRow.getCell(2).value = item.employee_name;
            designationRow.getCell(2).alignment = {
                horizontal: "center",
                vertical: "middle",
            };

            worksheet.mergeCells(`C${rowNo}:C${rowNo + (rowLength - 1)}`);
            let locationRow = worksheet.getRow(rowNo);
            locationRow.getCell(3).value = item?.department;
            locationRow.getCell(3).alignment = {
                horizontal: "center",
                vertical: "middle",
            };

            worksheet.mergeCells(`D${rowNo}:D${rowNo + (rowLength - 1)}`);
            let totalDaysRow = worksheet.getRow(rowNo);
            totalDaysRow.getCell(4).value = item?.work_contact;
            totalDaysRow.getCell(4).alignment = {
                horizontal: "center",
                vertical: "middle",
            };

            worksheet.mergeCells(`E${rowNo}:E${rowNo + (rowLength - 1)}`);
            let divisionRow = worksheet.getRow(rowNo);
            divisionRow.getCell(5).value = item.designation; // division
            divisionRow.getCell(5).alignment = {
                horizontal: "center",
                vertical: "middle",
            };

            if (itemIndex % 2 === 0) {
                worksheet.getRow(rowNo).fill = {
                    type: "pattern",
                    pattern: "solid",
                    fgColor: {
                        argb: "98FB98",
                    },
                };
                colorPop.push(range(rowNo, rowNo + (rowLength - 1)));
            }

            rowNo += rowLength;
        })

    });

    colorPop.map((value: any, index: any) => {
        value.map((item: any) => {
            worksheet.getRow(item).fill = {
                type: "pattern",
                pattern: "solid",
                fgColor: {
                    argb: "FFD9E2F3",
                },
            };
            worksheet.getRow(item).border = {
                top: {style: "thin", color: {argb: "FFB1B1B1"}}, // Border color is red
                left: {style: "thin", color: {argb: "FFB1B1B1"}},
                bottom: {style: "thin", color: {argb: "FFB1B1B1"}},
                right: {style: "thin", color: {argb: "FFB1B1B1"}},
            };
        });
    });

    let organizationNo = 9;
    data?.forEach((value: any) => {
        let colNo = 6;
        value?.response?.forEach((item: any) => {
            item?.job_history?.map((result: any) => {
                worksheet.getCell(organizationNo, colNo).value = result?.organization;
                organizationNo += 1;
            })
        });
    });

    let countryNo = 9;
    data?.forEach((value: any) => {
        let colNo = 7;
        value?.response?.forEach((item: any) => {
            item?.job_history?.map((result: any) => {
                worksheet.getCell(countryNo, colNo).value = result?.country_name;
                countryNo += 1;
            });
        });
    });

    let positionNo = 9;
    data?.forEach((value: any) => {
        let colNo = 8;
        value?.response?.forEach((item: any) => {
            item?.job_history?.map((result: any) => {
                worksheet.getCell(positionNo, colNo).value = result?.position;
                positionNo += 1;
            });
        });
    });

    let startDateNo = 9;
    data?.forEach((value: any) => {
        let colNo = 9;
        value?.response?.forEach((item: any) => {
            item?.job_history?.map((result: any) => {
                worksheet.getCell(startDateNo, colNo).value = result?.start_date;
                startDateNo += 1;
            })
        });
    });

    let endDateNo = 9;
    data?.forEach((value: any) => {
        let colNo = 10;
        value?.response?.forEach((item: any) => {
            item?.job_history?.map((result: any) => {
                worksheet.getCell(endDateNo, colNo).value = result?.end_date;
                endDateNo += 1;
            });
        });
    });

    try {
        // Generate buffer
        const buffer = await workbook.xlsx.writeBuffer();

        // Trigger file download
        const blob = new Blob([buffer], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `${reportName ? reportName : "Report"}.xlsx`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
    } catch (e) {
        console.log(e);
    }
}
