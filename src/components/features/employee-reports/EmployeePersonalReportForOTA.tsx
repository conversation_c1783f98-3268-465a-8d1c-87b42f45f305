import {dateTimeFormatForExcel, getComanyLogoById} from "@/utils/helper";
// @ts-ignore
import ExcelJS from "exceljs";

export async function EmployeePersonalReportForOTA(
    data: any,
    reportName: any,
    company: any,
    departmentName: any,
    companyId: any
): Promise<void> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Attendance");

    const fetchLogoAndGenerateExcel = async (): Promise<string> => {
        if (companyId) {
            const data = await getComanyLogoById(companyId);
            return data;
        } else {
            return "data:image/jpeg;base64,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";
        }
    };

    worksheet.mergeCells(`A1:L1`);
    const logoRow = worksheet.getRow(1);
    let companyImage = await fetchLogoAndGenerateExcel();

    let companyLogo = workbook.addImage({
        base64: companyImage,
        extension: "jpeg",
    });

    worksheet.addImage(companyLogo, {
        tl: {col: 0, row: 0},
        ext: {width: 200, height: 100},
    });
    logoRow.height = 90;

    worksheet.mergeCells(`A2:L3`);
    const headerRow = worksheet.getRow(2);
    headerRow.height = 25;
    headerRow.getCell(1).value = reportName + " of " + company;
    headerRow.getCell(1).font = {size: 11, bold: true};
    headerRow.getCell(1).alignment = {horizontal: "center", vertical: "middle"};
    // date
    const today = new Date();

    worksheet.mergeCells(`A4:L4`);
    const printDate = worksheet.getRow(4);
    printDate.getCell(1).value = `Print Date: ${dateTimeFormatForExcel(today)}`;
    printDate.getCell(1).font = {size: 11};
    printDate.getCell(1).alignment = {horizontal: "center", vertical: "middle"};

    worksheet.mergeCells(`A5:L5`);
    const department = worksheet.getRow(5);
    department.getCell(1).value = departmentName
        ? `Department of ${departmentName}`
        : "";
    department.getCell(1).font = {size: 11};
    department.getCell(1).alignment = {
        horizontal: "center",
        vertical: "middle",
    };

    const alphabet = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'];
    const page1HeaderString = ['SL', 'ID', 'Name', 'Designation', 'Department', 'Date of Joining', 'Joining Designation', 'Work Location']; // These will be added; 'Joining Salary', 'Current Salary'

    for (let i = 0; i < alphabet.length; i++) {
        // Merge the cells from row 7 to 8 in the respective column
        worksheet.mergeCells(`${alphabet[i]}7:${alphabet[i]}8`);

        // Get the cell in row 7, column i+1 (convert alphabet to index)
        let cell = worksheet.getCell(`${alphabet[i]}7`);

        // Set the cell value
        cell.value = page1HeaderString[i];

        // Set the font size
        cell.font = {size: 11};

        // Set the alignment
        cell.alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        cell.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: {
                argb: "FFC6E0B4",
            },
        };

        worksheet.getColumn(alphabet[i]).width = page1HeaderString[i].length + 2;
    }

    const page2LowerHeaderString = ['Joining Salary', 'Current Salary','Salary Increment','Effective Date', ]
    worksheet.mergeCells(`I7:L7`);
    const salaryHistoryDetails = worksheet.getCell(`I7`);
    salaryHistoryDetails.value = 'Salary History'
    salaryHistoryDetails.font = {size: 11, bold:true};
    salaryHistoryDetails.alignment = {
        horizontal: "center",
        vertical: "middle",
    };
    salaryHistoryDetails.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: {
            argb: "FFC6E0B4",
        },
    }

    const educationalHeaderString = ['Exam', 'Major', 'Year', 'Result', 'Institution']

    worksheet.mergeCells(`M7:Q7`);
    const educationalDetails = worksheet.getCell(`M7`);
    educationalDetails.value = 'Educational Details'
    educationalDetails.font = {size: 11,bold:true};
    educationalDetails.alignment = {
        horizontal: "center",
        vertical: "middle",
    };
    educationalDetails.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: {
            argb: "FFC6E0B4",
        },
    }
    // worksheet.getColumn('J7').width = "Educational Details".length + 2;

    const page2LowerHeaderIndex = ['I', 'J', 'K', 'L']

    for (let i = 0; i < page2LowerHeaderString.length; i++) {
        worksheet.mergeCells(`${page2LowerHeaderIndex[i]}8:${page2LowerHeaderIndex[i]}8`);
        // Get the cell in row 7, column i+1 (convert alphabet to index)
        let cell = worksheet.getCell(`${page2LowerHeaderIndex[i]}8`);

        // Set the cell value
        cell.value = page2LowerHeaderString[i];

        // Set the font size
        cell.font = {size: 11};

        // Set the alignment
        cell.alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        cell.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: {
                argb: "FFC6E0B4",
            },
        };
    }

    const educationalHeaderIndex = ['M','N','O','P', 'Q']

    for (let i = 0; i < educationalHeaderString.length; i++) {
        worksheet.mergeCells(`${educationalHeaderIndex[i]}8:${educationalHeaderIndex[i]}8`);
        // Get the cell in row 7, column i+1 (convert alphabet to index)
        let cell = worksheet.getCell(`${educationalHeaderIndex[i]}8`);

        // Set the cell value
        cell.value = educationalHeaderString[i];

        // Set the font size
        cell.font = {size: 11};

        // Set the alignment
        cell.alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        cell.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: {
                argb: "FFC6E0B4",
            },
        };
    }
    console.log("Everything is alright till now!");

    const experienceHeaderString = ['Employer', 'Position', 'From', 'To', 'Duration']

    worksheet.mergeCells(`R7:V7`);
    const experienceDetails = worksheet.getCell(`R7`);
    experienceDetails.value = 'Experience Details'
    experienceDetails.font = {size: 11 ,bold:true};
    experienceDetails.alignment = {
        horizontal: "center",
        vertical: "middle",
    };
    experienceDetails.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: {
            argb: "FFC6E0B4",
        },
    }

    const experienceHeaderIndex = ['R','S','T','U','V']

    for (let i = 0; i < experienceHeaderString.length; i++) {
        worksheet.mergeCells(`${experienceHeaderIndex[i]}8:${experienceHeaderIndex[i]}8`);
        // Get the cell in row 7, column i+1 (convert alphabet to index)
        let cell = worksheet.getCell(`${experienceHeaderIndex[i]}8`);

        // Set the cell value
        cell.value = experienceHeaderString[i];

        // Set the font size
        cell.font = {size: 11};

        // Set the alignment
        cell.alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        cell.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: {
                argb: "FFC6E0B4",
            },
        };
    }

    const workDurationIndex = ['W', 'X'];
    const workDurationString = ['Previous Airlines/Travel Agency Experience','USBA working Duration']

    for (let i = 0; i < workDurationIndex.length; i++) {
        // Merge the cells from row 7 to 8 in the respective column
        // const mergeCells = `${workDurationIndex[i]}7:${workDurationIndex[i]}8`;
        worksheet.mergeCells(`${workDurationIndex[i]}7:${workDurationIndex[i]}8`);

        // Get the cell in row 7, column i+1 (convert alphabet to index)
        let cell = worksheet.getCell(`${workDurationIndex[i]}7`);

        // Set the cell value
        cell.value = workDurationString[i];

        // Set the font size
        cell.font = {size: 11};

        // Set the alignment
        cell.alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        cell.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: {
                argb: "FFC6E0B4",
            },
        };

        worksheet.getColumn(workDurationIndex[i]).width = workDurationString[i].length + 2;
    }
    // worksheet.mergeCells(`V7:V8`);
    // const previousAgencyExperience = worksheet.getCell(`V7`);
    // previousAgencyExperience.value = 'Previous Airlines/Travel Agency Experience'
    // previousAgencyExperience.font = {size: 11};
    // previousAgencyExperience.alignment = {
    //     horizontal: "center",
    //     vertical: "middle",
    // };
    // previousAgencyExperience.fill = {
    //     type: "pattern",
    //     pattern: "solid",
    //     fgColor: {
    //         argb: "FFC6E0B4",
    //     },
    // }
    // // worksheet.getColumn('R7').width = "Previous Airline/Travel Agency Experience".length + 2;

    // worksheet.mergeCells(`W7:W8`);
    // const workingDuration = worksheet.getCell(`W7`);
    // workingDuration.value = 'USBA working Duration'
    // workingDuration.font = {size: 11};
    // workingDuration.alignment = {
    //     horizontal: "center",
    //     vertical: "middle",
    // };
    // workingDuration.fill = {
    //     type: "pattern",
    //     pattern: "solid",
    //     fgColor: {
    //         argb: "FFC6E0B4",
    //     },
    // }

    console.log("All okay till W7");

    worksheet.mergeCells(`Y7:AF7`);
    const page3PersonalDetails = worksheet.getCell(`Y7`);
    page3PersonalDetails.value = "Personal Details";
    page3PersonalDetails.font = {size: 11, bold: true};
    page3PersonalDetails.alignment = {horizontal: "center", vertical: "middle"};
    page3PersonalDetails.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: {
            argb: "FFC6E0B4",
        },
    }

    const page3LowerHeaderString = ['NID', 'Present Address', 'Permanent Address', 'Date of Birth', 'Marital Status', 'Fathers Name', 'Mothers Name', 'Contact']
    const page3LowerHeaderIndex = ['Y', 'Z', 'AA', 'AB', 'AC','AD','AE','AF']

    for (let i = 0; i < page3LowerHeaderString.length; i++) {
        worksheet.mergeCells(`${page3LowerHeaderIndex[i]}8:${page3LowerHeaderIndex[i]}8`);
        // Get the cell in row 7, column i+1 (convert alphabet to index)
        let cell = worksheet.getCell(`${page3LowerHeaderIndex[i]}8`);

        // Set the cell value
        cell.value = page3LowerHeaderString[i];

        // Set the font size
        cell.font = {size: 11};

        // Set the alignment
        cell.alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        cell.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: {
                argb: "FFC6E0B4",
            },
        };
    }

    console.log("All okay till AE7");

    worksheet.mergeCells(`AG7:AL7`);
    const professionalCertification = worksheet.getCell(`AG7`);
    professionalCertification.value = "Professional Certification";
    professionalCertification.font = {size: 11, bold: true};
    professionalCertification.alignment = {horizontal: "center", vertical: "middle"};
    professionalCertification.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: {
            argb: "FFC6E0B4",
        },
    }

    const professionalCertificationString = ['Certification Name','Institute Name','Result', 'Start Date', 'End Date' ,'Duration'];
    const professionalCertificationIndex = ['AG', 'AH', 'AI','AJ','AK', 'AL']

    for (let i = 0; i < professionalCertificationString.length; i++) {
        worksheet.mergeCells(`${professionalCertificationIndex[i]}8:${professionalCertificationIndex[i]}8`);
        // Get the cell in row 7, column i+1 (convert alphabet to index)
        let cell = worksheet.getCell(`${professionalCertificationIndex[i]}8`);

        // Set the cell value
        cell.value = professionalCertificationString[i];

        // Set the font size
        cell.font = {size: 11};

        // Set the alignment
        cell.alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        cell.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: {
                argb: "FFC6E0B4",
            },
        };
    }

    worksheet.columns.forEach((item: any) => {
        item.border = {
            top: {style: "thin", color: {argb: "FFB1B1B1"}}, // Border color is red
            left: {style: "thin", color: {argb: "FFB1B1B1"}},
            bottom: {style: "thin", color: {argb: "FFB1B1B1"}},
            right: {style: "thin", color: {argb: "FFB1B1B1"}},
        }
    })

    let rowNo = 9;
    let colorPop: any = [];

    function range(start: number, end: number) {
        return Array.from({length: end - start + 1}, (_, i) => start + i);
    }

    let rowLengthForMerge: any[] = []

    data?.map((value: any, index: any) => {
        let educationInfoLength = value?.educational_info?.length;
        let jobHistoryLength = value?.job_history?.length;
        let salaryHistoryLength = value?.salary_history?.length;
        let rowLength = Math.max(educationInfoLength, jobHistoryLength, salaryHistoryLength);
        rowLengthForMerge.push(rowLength)

        worksheet.mergeCells(`A${rowNo}:A${rowNo + (rowLength - 1)}`);
        let slRow = worksheet.getRow(rowNo);
        slRow.getCell(1).value = index + 1;
        slRow.getCell(1).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        worksheet.mergeCells(`B${rowNo}:B${rowNo + (rowLength - 1)}`);
        let employeeIdRow = worksheet.getRow(rowNo);
        employeeIdRow.getCell(2).value = value?.work_information.employee_id;
        employeeIdRow.getCell(2).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        worksheet.mergeCells(`C${rowNo}:C${rowNo + (rowLength - 1)}`);
        let employeeNameRow = worksheet.getRow(rowNo);
        employeeNameRow.getCell(3).value = value?.work_information.name;
        employeeNameRow.getCell(3).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        worksheet.mergeCells(`D${rowNo}:D${rowNo + (rowLength - 1)}`);
        let employeeDesignationRow = worksheet.getRow(rowNo);
        employeeDesignationRow.getCell(4).value = value?.work_information.job_title;
        employeeDesignationRow.getCell(4).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        worksheet.mergeCells(`E${rowNo}:E${rowNo + (rowLength - 1)}`);
        let employeeDepartmentRow = worksheet.getRow(rowNo);
        employeeDepartmentRow.getCell(5).value = value?.work_information.department;
        employeeDepartmentRow.getCell(5).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        worksheet.mergeCells(`F${rowNo}:F${rowNo + (rowLength - 1)}`);
        let employeeDojRow = worksheet.getRow(rowNo);
        employeeDojRow.getCell(6).value = value?.work_information?.joining_date;
        employeeDojRow.getCell(6).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        worksheet.mergeCells(`G${rowNo}:G${rowNo + (rowLength - 1)}`);
        let employeeJoiningDesignationRow = worksheet.getRow(rowNo);
        employeeJoiningDesignationRow.getCell(7).value = value?.work_information?.joining_designation;
        employeeJoiningDesignationRow.getCell(7).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        worksheet.mergeCells(`H${rowNo}:H${rowNo + (rowLength - 1)}`);
        let workLocationRow = worksheet.getRow(rowNo);
        workLocationRow.getCell(8).value = value?.work_information?.work_location;
        workLocationRow.getCell(8).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        worksheet.mergeCells(`I${rowNo}:I${rowNo + (rowLength - 1)}`);
        let employeeJoiningSalaryRow = worksheet.getRow(rowNo);
        employeeJoiningSalaryRow.getCell(9).value = value?.work_information?.joining_salary;
        employeeJoiningSalaryRow.getCell(9).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        worksheet.mergeCells(`J${rowNo}:J${rowNo + (rowLength - 1)}`);
        let employeeCurrentSalaryRow = worksheet.getRow(rowNo);
        employeeCurrentSalaryRow.getCell(10).value = value?.work_information?.running_salary;
        employeeCurrentSalaryRow.getCell(10).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        // worksheet.mergeCells(`K${rowNo}:K${rowNo + (rowLength - 1)}`);
        // let employeeIncrementSalaryRow = worksheet.getRow(rowNo);
        // employeeIncrementSalaryRow.getCell(11).value = value?.salary_history?.increment_salary;
        // employeeIncrementSalaryRow.getCell(11).alignment = {
        //     horizontal: "center",
        //     vertical: "middle",
        // };

        rowNo += rowLength;
    })

    let salaryIncrementNo = 9;
    data?.map((value: any) => {
        let colNo = 11;
        let educationInfoLength = value?.educational_info?.length;
        let jobHistoryLength = value?.job_history?.length;
        let salaryHistoryLength = value?.salary_history?.length;
        let rowLength = Math.max(educationInfoLength, jobHistoryLength, salaryHistoryLength);
        for (let i = 0; i < rowLength; i++) {
            if (value?.salary_history[i]) {
                worksheet.getCell(salaryIncrementNo, colNo).value = value?.salary_history[i]?.increment_salary;
                salaryIncrementNo += 1;
            }
            else {
                salaryIncrementNo += 1;
            }
        }
    });

    let incrementEffectiveNo = 9;
    data?.map((value: any) => {
        let colNo = 12;
        let educationInfoLength = value?.educational_info?.length;
        let jobHistoryLength = value?.job_history?.length;
        let salaryHistoryLength = value?.salary_history?.length;
        let rowLength = Math.max(educationInfoLength, jobHistoryLength, salaryHistoryLength);

        for (let i = 0; i < rowLength; i++) {
            if (value?.salary_history[i]) {
                worksheet.getCell(incrementEffectiveNo, colNo).value = value?.salary_history[i]?.updated_date;
                incrementEffectiveNo += 1;
            }
            else {
                incrementEffectiveNo += 1;
            }
        }
    });

    let educationNo = 9;
    data?.map((value: any, index: any) => {
        let colNo = 13;
        console.log(`${value?.work_information?.name} = ${value?.educational_info?.length}`)

        let educationInfoLength = value?.educational_info?.length;
        let jobHistoryLength = value?.job_history?.length;
        let salaryHistoryLength = value?.salary_history?.length;
        let rowLength = Math.max(educationInfoLength, jobHistoryLength, salaryHistoryLength);

        for (let i = 0; i < rowLength; i++) {
            if (value?.educational_info[i]) {
                worksheet.getCell(educationNo, colNo).value = value?.educational_info[i]?.exam_title ? value?.educational_info[i]?.exam_title : "";
                worksheet.getCell(educationNo, colNo+1).value = value?.educational_info[i]?.subject ? value?.educational_info[i]?.subject : "";
                worksheet.getCell(educationNo, colNo+2).value = value?.educational_info[i]?.year ? value?.educational_info[i]?.year : "";
                worksheet.getCell(educationNo, colNo+3).value = value?.educational_info[i]?.result ? value?.educational_info[i]?.result : "";
                worksheet.getCell(educationNo, colNo+4).value = value?.educational_info[i]?.institute ? value?.educational_info[i]?.institute : "";
                educationNo += 1;
            }
            else {
                educationNo += 1;
            }
        }
    });

    let jobHistoryNo: any;
    let rs = 9;
    let rs_increment = 0;
    data?.map((value: any) => {
        let colNo = 18;
        jobHistoryNo = rs;
        value?.job_history?.map((item: any, index: any) => {
            worksheet.getCell(jobHistoryNo, colNo).value = item?.organization;
            worksheet.getCell(jobHistoryNo, colNo+1).value = item?.position;
            worksheet.getCell(jobHistoryNo, colNo+2).value = item?.start_date;
            worksheet.getCell(jobHistoryNo, colNo+3).value = item?.end_date;
            worksheet.getCell(jobHistoryNo, colNo+4).value = item?.job_duration;
            // worksheet.getCell(jobHistoryNo, colNo+3).value = item?.industry_type === "airlines" ? item?.job_duration : "";
            // worksheet.getCell(jobHistoryNo, colNo+3).value = item?.industry_type === "airlines" ? "Airlines" : "";
            // worksheet.getCell(jobHistoryNo, colNo+5).value = value?.work_information?.airlines_duration; // work_duration
            jobHistoryNo += 1;
        })
        rs += rowLengthForMerge[rs_increment];
        rs_increment += 1;
    });

    let workDurationNo = 9;

    data?.map((value: any) => {
        let educationInfoLength = value?.educational_info?.length || 0;
        let jobHistoryLength = value?.job_history?.length || 0;
        let salaryHistoryLength = value?.salary_history?.length || 0;
        let rowLength = Math.max(educationInfoLength, jobHistoryLength, salaryHistoryLength);

        worksheet.mergeCells(`W${workDurationNo}:W${workDurationNo + (rowLength - 1)}`);
        let airlinesRow = worksheet.getRow(workDurationNo);
        airlinesRow.getCell(23).value = value?.work_information?.airlines_duration;
        airlinesRow.getCell(23).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        // let usbaDurationRow = worksheet.getRow(workDurationNo);
        // usbaDurationRow.getCell(23).value = value?.work_information?.working_duration; // Column W
        // usbaDurationRow.getCell(23).alignment = { horizontal: "center", vertical: "middle" };

        worksheet.mergeCells(`X${workDurationNo}:X${workDurationNo + (rowLength - 1)}`);
        let usbaDurationRow = worksheet.getRow(workDurationNo);
        usbaDurationRow.getCell(24).value = value?.work_information?.working_duration;
        usbaDurationRow.getCell(24).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        workDurationNo += rowLength;
    });




    let personalDetailsNo = 9;
    data?.map((value: any, index: any) => {
        let colNo = 21;

        let educationInfoLength = value?.educational_info?.length;
        let jobHistoryLength = value?.job_history?.length;
        let salaryHistoryLength = value?.salary_history?.length;
        let rowLength = Math.max(educationInfoLength, jobHistoryLength, salaryHistoryLength);

        worksheet.mergeCells(`Y${personalDetailsNo}:Y${personalDetailsNo + (rowLength - 1)}`);
        let NIDRow = worksheet.getRow(personalDetailsNo);
        NIDRow.getCell(25).value = value?.private_information?.nid;
        NIDRow.getCell(25).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        worksheet.mergeCells(`Z${personalDetailsNo}:Z${personalDetailsNo + (rowLength - 1)}`);
        let presentAddressRow = worksheet.getRow(personalDetailsNo);
        let present_village = value?.private_information?.present_village !== null ? value?.private_information?.present_village + ", " : "";
        let present_upazila = value?.private_information?.present_upazila !== null ? value?.private_information?.present_upazila + ", " : "";
        let present_district = value?.private_information?.present_district !== null ? value?.private_information?.present_district + ", " : "";
        let present_division = value?.private_information?.present_division !== null ? value?.private_information?.present_division : "";
        let presentAddress = present_village  + present_upazila + present_district + present_division;
        presentAddressRow.getCell(26).value = presentAddress;
        presentAddressRow.getCell(26).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        worksheet.mergeCells(`AA${personalDetailsNo}:AA${personalDetailsNo + (rowLength - 1)}`);
        let permanentAddressRow = worksheet.getRow(personalDetailsNo);
        let permanent_village = value?.private_information?.permanent_village !== null ? value?.private_information?.permanent_village + ", " : "";
        let permanent_upazila = value?.private_information?.permanent_upazila !== null ? value?.private_information?.permanent_upazila + ", " : "";
        let permanent_district = value?.private_information?.permanent_district !== null ? value?.private_information?.permanent_district + ", " : "";
        let permanent_division = value?.private_information?.permanent_division !== null ? value?.private_information?.permanent_division : "";
        let permanentAddress = permanent_village  + permanent_upazila + permanent_district + permanent_division;
        permanentAddressRow.getCell(27).value = permanentAddress;
        permanentAddressRow.getCell(27).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        worksheet.mergeCells(`AB${personalDetailsNo}:AB${personalDetailsNo + (rowLength - 1)}`);
        let maritalStatusRow = worksheet.getRow(personalDetailsNo);
        maritalStatusRow.getCell(28).value = value?.private_information?.birthday;
        maritalStatusRow.getCell(28).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        worksheet.mergeCells(`AC${personalDetailsNo}:AC${personalDetailsNo + (rowLength - 1)}`);
        let fatherNameRow = worksheet.getRow(personalDetailsNo);
        fatherNameRow.getCell(29).value = value?.private_information?.maritial_status;
        fatherNameRow.getCell(29).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        worksheet.mergeCells(`AD${personalDetailsNo}:AD${personalDetailsNo + (rowLength - 1)}`);
        let motherNameRow = worksheet.getRow(personalDetailsNo);
        motherNameRow.getCell(30).value = value?.private_information?.father_name;
        motherNameRow.getCell(30).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        worksheet.mergeCells(`AE${personalDetailsNo}:AE${personalDetailsNo + (rowLength - 1)}`);
        let phoneRow = worksheet.getRow(personalDetailsNo);
        phoneRow.getCell(31).value = value?.private_information?.mother_name;
        phoneRow.getCell(31).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        worksheet.mergeCells(`AF${personalDetailsNo}:AF${personalDetailsNo + (rowLength - 1)}`);
        let dobRow = worksheet.getRow(personalDetailsNo);
        dobRow.getCell(32).value = value?.work_information?.work_phone ? value?.work_information?.work_mobile : value?.work_information?.work_mobile ? value?.work_information?.work_mobile : value?.work_information?.personal_mobile;
        dobRow.getCell(32).alignment = {
            horizontal: "center",
            vertical: "middle",
        };
        // worksheet.getCell(personalDetailsNo, colNo).value = value?.private_information?.passport_no;
        // worksheet.getCell(personalDetailsNo, colNo+1).value = value?.private_information?.nid;
        // worksheet.getCell(personalDetailsNo, colNo+2).value = value?.private_information?.present_address;
        // worksheet.getCell(personalDetailsNo, colNo+3).value = value?.private_information?.permanent_address;
        // worksheet.getCell(personalDetailsNo, colNo+4).value = value?.marital_status;
        // worksheet.getCell(personalDetailsNo, colNo+5).value = value?.private_information?.father_details;
        // worksheet.getCell(personalDetailsNo, colNo+6).value = value?.private_information?.mother_name;
        // worksheet.getCell(personalDetailsNo, colNo+7).value = value?.private_information?.phone;
        // worksheet.getCell(personalDetailsNo, colNo+8).value = value?.private_information?.emargency_contact;
        personalDetailsNo += rowLength;
    });

    let professionalCertificationNo = 9;
    data?.map((value: any, index: any) => {
        if (value.professional_certificate){
            let educationInfoLength = value?.educational_info?.length;
            let jobHistoryLength = value?.job_history?.length;
            let salaryHistoryLength = value?.salary_history?.length;
            const certificationsLength = value?.professional_certificate?.length;
            let rowLength = Math.max(educationInfoLength, jobHistoryLength, salaryHistoryLength,certificationsLength);

            const certifications = value?.professional_certificate;
            certifications.forEach((cert:any, certIndex:any) => {
                const rowOffset = certIndex + professionalCertificationNo;
                worksheet.mergeCells(`AG${rowOffset}:AG${rowOffset}`);
                worksheet.getCell(`AG${rowOffset}`).value = cert?.certificate_name;
                worksheet.getCell(`AG${rowOffset}`).alignment = { horizontal: "center", vertical: "middle" };

                worksheet.mergeCells(`AH${rowOffset}:AH${rowOffset}`);
                worksheet.getCell(`AH${rowOffset}`).value = cert?.certification_institute_name;
                worksheet.getCell(`AH${rowOffset}`).alignment = { horizontal: "center", vertical: "middle" };

                worksheet.mergeCells(`AI${rowOffset}:AI${rowOffset}`);
                worksheet.getCell(`AI${rowOffset}`).value = cert?.certification_result;
                worksheet.getCell(`AI${rowOffset}`).alignment = { horizontal: "center", vertical: "middle" };

                worksheet.mergeCells(`AJ${rowOffset}:AJ${rowOffset}`);
                worksheet.getCell(`AJ${rowOffset}`).value = cert?.certification_start_date;
                worksheet.getCell(`AJ${rowOffset}`).alignment = { horizontal: "center", vertical: "middle" };

                worksheet.mergeCells(`AK${rowOffset}:AK${rowOffset}`);
                worksheet.getCell(`AK${rowOffset}`).value = cert?.certification_end_date;
                worksheet.getCell(`AK${rowOffset}`).alignment = { horizontal: "center", vertical: "middle" };

                worksheet.mergeCells(`AL${rowOffset}:AL${rowOffset}`);
                worksheet.getCell(`AL${rowOffset}`).value = cert?.certification_duration;
                worksheet.getCell(`AL${rowOffset}`).alignment = { horizontal: "center", vertical: "middle" };
            });

            professionalCertificationNo += rowLength;
        }
    });

    worksheet.columns.forEach((column, index) => {
        if (index >= 1 && index <= 37) {
            column.width = 25;
        }
    });

    try {
        // Generate buffer
        const buffer = await workbook.xlsx.writeBuffer();

        // Trigger file download
        const blob = new Blob([buffer], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `${reportName ? reportName : "Report"}.xlsx`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
    } catch (e) {
        console.log(e);
    }
}
