export interface Schedule {
    schedule_id: number;
    is_check_in: boolean;
    check_in_time: string;
    check_out_time: string;
    status: string;
    decline_reason: null;
    employee_name: string;
    employee_id: string;
    employee_unique_id: string;
    department_name: null;
    designation: null;
    scheduling_type: string;
    reason: string;
    description: string;
    date: string;
    shift_id: number;
    shift_name: string;
    from_date_time: string;
    to_date_time: string;
    locations: LocationInfo[];
}

export interface LocationInfo {
    location_id: number;
    location_name: string;
    location_latitude: number;
    location_longitude: number;
    radius: number;
    measurement: string;
}

export interface LocationData {
    name: string;
    latitude: number;
    longitude: number;
    radius: number;
    measurement: string;
}

export interface GoogleLocation {
    lat: number;
    lng: number;
    address: string;
}

export interface AttendanceInfo {
    id: number;
    image: string;
    remarks: string;
    lang: number;
    lat: number;
    att_date: string;
    att_time: string;
    attendance_type: string;
    location: string;
}

export interface RadiusInfo {
    default_radius: number;
    measurement: string;
    remarks: string;
}
