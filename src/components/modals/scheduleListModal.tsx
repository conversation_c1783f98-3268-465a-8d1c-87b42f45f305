import { API_ROUTE } from "@/constants/api-routes";
import { User } from "@/interface/user";
import { getRequestWithParams } from "@/services/apiRequestHandlers";
import { Button, Col, Modal, Row, Table } from "antd";
import React, { useEffect, useState } from "react";

interface ScheduleInfo {
    id: number;
    name: string;
    work_day: string;
}
interface Props {
    isModalVisible: boolean;
    handleCancel: (e: React.MouseEvent<HTMLButtonElement>) => void;
    details: User | undefined;
    singleDate: any;
    dateRange: any;
    schedulingType: string | undefined;
}
const ScheduleListModal = ({
    isModalVisible,
    handleCancel,
    details,
    singleDate,
    dateRange,
    schedulingType,
}: Props) => {
    const [schedules, setSchedules] = useState<ScheduleInfo[]>([]);

    useEffect(() => {
        if (details) fetchList();
    }, [details]);
    const fetchList = async () => {
        try {
            const resp: any = await getRequestWithParams(
                `${API_ROUTE.GET_EMPLOYEE_SCHEDULES}`,
                {
                    employee_id: details?.employee_id,
                    start_date:
                        schedulingType === "Repetitive"
                            ? dateRange[0]?.format("YYYY-MM-DD")
                            : singleDate?.format("YYYY-MM-DD"),
                    end_date:
                        schedulingType === "Repetitive"
                            ? dateRange[1]?.format("YYYY-MM-DD")
                            : singleDate?.format("YYYY-MM-DD"),
                }
            );
            if (resp) {
                setSchedules(resp?.data);
            } else {
                setSchedules([]);
            }
        } catch (err) {
            console.log(err);
        }
    };

    const colums = [
        {
            title: "Schedule Name",
            dataIndex: "name",
        },
        {
            title: "Work Day",
            dataIndex: "work_day",
        },
    ];
    return (
        <Modal
            open={isModalVisible}
            onCancel={handleCancel}
            footer={[
                <Button key="close" onClick={handleCancel}>
                    Close
                </Button>,
            ]}
            title="Schedule Info"
            width={800}
            centered
        >
            <div className="flex-col">
                <div className={`flex-1`}>
                    {schedules?.length > 0 ? (
                        <Table
                            dataSource={schedules.map((item) => ({
                                ...item,
                                key: item.id,
                            }))}
                            columns={colums}
                            pagination={false}
                            rowClassName={(_record: any, index) =>
                                index % 2 === 0
                                    ? "slate-background"
                                    : "white-background"
                            }
                            key={"id"}
                            scroll={{ y: 400 }}
                        />
                    ) : (
                        <div className="flex h-24 w-full items-center justify-center border-[1px] border-dashed">
                            No Data Found
                        </div>
                    )}
                </div>
            </div>
        </Modal>
    );
};

export default ScheduleListModal;
