export const API_ROUTE = {
    // Authentication
    AUTHENTICATE: "authenticate-user",
    REGENERATE_ACCESS_TOKEN: "auth/refresh",
    LOG_OUT: "auth/logout/",
    ACCESS_TOKEN_VERIFICATION: "access-token-verification",

    // Employee
    GET_EMPLOYEE: "employee",
    GET_USER_DETAILS: "profile/users",
    SEARCH_EMPLOYEE: "employee/dropdown",
    E<PERSON><PERSON>OYEE_DROPDOWN_WITH_IMAGE: "employee/dropdown-with-image",

    // Company
    COMPANY_LIST: "company/dropdown",
    GET_COMPANY: "user/companies",
    COMPANY_LOGO: "company/logo",

    // Shift
    GET_SHIFT_LIST: "company-shift/dropdown",
    GET_REASONS_LIST: "outstation-reasons/list",

    // Schedule
    SCHEDULE_LIST: "out-station/schedules",
    SCHEDULE_CREATE: "out-station/schedule-add",
    GET_SCHEDULE_RADIUS: "out-station/schedule/change-radius",
    UPDATE_SCHEDULE_RADIUS: "out-station/schedule/change-radius",
    BU<PERSON><PERSON>_UPDATE_SCHEDULE_RADIUS: "out-station/schedule/bulk-change-radius",
    DECLINE_SCHEDULE: "out-station/schedule/status-update",
    BULK_DECLINE_SCHEDULE: "out-station/schedule/bulk-status-update",
    GET_EMPLOYEE_SCHEDULES: "out-station/schedule/existing-shift",

    // Attendance
    ATTENDANCE_INFO: "out-station-attendance",
    DEVICE_LIST: "device-attendance/devices",
    CREATE_DEVICE: "device-attendance/devices",
    UPDATE_DEVICE: "device-attendance/devices",
    DELETE_DEVICE: "device-attendance/devices",
    ATTENDANCE_REPORT: "attendance/report",
    ATTENDANCE_STATUS_REPORT: "attendance/status/report",
    SAME_CHECKIN_CHECKOUT: "same/checkin/checkout/attendances",
    MOVEMENT_LOG_REPORT: "movement/log/report",

    // Dashboard
    DASHBOARD_ATTENDANCE_GRAPH: "dashboard/attendance-graph",
    DASHBOARD_CARD_ATTENDANCE: "dashboard/card/attendance",
    DASHBOARD_LEAVE_GRAPH: "dashboard/leave-graph",
    DASHBOARD_EMPLOYEE_JOINING_RESIGN_GRAPH: "dashboard/employee-joining-resign-graph",
    DASHBOARD_TODAY_ATTENDANCE_DATA: "dashboard/today-attendance-data",
    DASHBOARD_BIRTHDAYS: "dashboard/birthdays",
    DASHBOARD_EVENTS: "dashboard/get-events",
    DASHBOARD_ANNOUNCEMENTS: "dashboard/announcements",
    DASHBOARD_PENDING_APPROVALS: "dashboard/card/pending-approvals",
    DASHBOARD_PENDING_LEAVE_APPROVAL_LIST: "dashboard/pending-leave-approval-list",
    DASHBOARD_PENDING_ATTENDANCE_APPROVAL_LIST: "dashboard/pending-attendance-approval-list",
    DASHBOARD_PENDING_SELF_SERVICE_APPROVAL_LIST: "dashboard/pending-self-service-approval-list",
    DASHBOARD_MY_WORKSPACE_CARD: "dashboard/my-work-space-card",
    DASHBOARD_MY_WORKSPACE_DATA: "dashboard/my-work-space-data",
    DASHBOARD_MY_WORKSPACE_KPI_DATA: "dashboard/my-work-space-kpi-data",
    DASHBOARD_ADJUSTMENTS_GRAPH: "dashboard/adjustments-graph",

    // Leave
    LEAVE_LEAVES: "leave/leaves",

    // Attendance Adjustment
    ATTENDANCE_ADJUSTMENT: "attendance_adjustment/adjustments",

    // Configuration
    GET_CONFIG: "configuration/get-config",
    UPDATE_CONFIG: "configuration/update",

    // Reports
    EMPLOYEE_PERSONAL_DETAILS_REPORT: "employee/personal/details/report",
    EMPLOYEE_SEPARATION_DETAILS_REPORT: "employee/separation/details/report",
    EMPLOYEE_SALARY_HISTORY_REPORT: "employee/salary/history/report",
    EMPLOYEE_NOMINEE_REPORT: "employee/nominee/report",
    EMPLOYEE_EDUCATIONAL_INFO_REPORT: "employee/educational/info/report",
    EMPLOYEE_CV_REPORT: "employee/cv/report",
    EMPLOYEE_LIST_REPORT: "employee/list/report",
    MEDICAL_INSURANCE_REPORT: "employee/medical/insurance/report",
};
