import Header from "@/components/layout/Header";
import MainLayout from "@/components/layout/MainLaylout";
import { TokenVerificationService } from "@/services/TokenVarification";
import { REFRESH_TOKEN_KEY, SUCCESS, TOKEN_KEY } from "@/utils/common";
import { useRouter } from "next/router";
import React, { useEffect } from "react";
import DashboardComponent from "@/components/dashboard/dashboardComponent";
import { getEmployeeDetails } from "@/services/WorkStationServices/NotificationServies";
import { EmployeeProvider, useEmployeeContext } from "@/store/EmployeeContext";

interface Props {
  window?: () => Window;
  children: React.ReactElement;
}

const Dashboard = () => {
  const router = useRouter();
  const checkStorage = async () => {
    const token = localStorage.getItem(TOKEN_KEY);
    if (token) {
      return;
    }

    const refreshToken = localStorage.getItem(REFRESH_TOKEN_KEY);
    if (refreshToken) {
      await tokenRegenerate(refreshToken);
    } else {
      router.push("/unauthorized");
    }
  };

  useEffect(() => {
    checkStorage();

    const intervalId = setInterval(() => {
      checkStorage();
    }, 100000);

    return () => clearInterval(intervalId);
  }, []);

  const tokenRegenerate = async (refreshToken: string) => {
    try {
      const { data, status } =
        await TokenVerificationService.regenerateTokenForWorkspace(
          refreshToken
        );
      if (status === SUCCESS && data?.accessToken) {
        localStorage.setItem(TOKEN_KEY, data.accessToken);
        
        setTimeout(() => {
          localStorage.removeItem(TOKEN_KEY);
        }, 7 * 24 * 60 * 60 * 1000);    
      } else {
        router.push("/unauthorized");
      }
    } catch (error) {
      console.error("Token Refresh Error:", error);
      router.push("/unauthorized");
    }
  };

  return (
    <MainLayout>
      <EmployeeProvider>
        <DashboardComponent />
      </EmployeeProvider>
    </MainLayout>
  );
};

export default Dashboard;
