"use client";
import React from "react";
import { Card, CardContent, Typography, Avatar } from "@mui/material";
import { format, parseISO } from "date-fns";

interface BirthdayList {
  id: number;
  unique_id: string;
  name: string;
  birthday: string; // API provides full date (YYYY-MM-DD)
  designation: string;
  image: string;
}

interface BirthdayListProps {
  birthdays: BirthdayList[];
}

const BirthdayCard: React.FC<BirthdayListProps> = ({ birthdays }) => {
  // Get today's month and day
  const today = new Date();
  const todayMonth = today.getMonth();
  const todayDay = today.getDate();

  // Filter today's birthdays
  const todayBirthdays = birthdays.filter((b) => {
    const birthDate = parseISO(b.birthday);
    return birthDate.getMonth() === todayMonth && birthDate.getDate() === todayDay;
  });

  // Filter upcoming birthdays (after today in the current year)
  const upcomingBirthdays = birthdays.filter((b) => {
    const birthDate = parseISO(b.birthday);
    return (
      birthDate.getMonth() > todayMonth ||
      (birthDate.getMonth() === todayMonth && birthDate.getDate() > todayDay)
    );
  });

  return (
    <Card className="w-full shadow-lg !rounded-none">
      <CardContent>
        <Typography variant="h6" className="font-semibold text-center">
          🎂 Happy Birthdays!
        </Typography>

        <div className="mt-3 p-1 h-[420px] overflow-y-auto">
          {/* Today's Birthdays */}
          {todayBirthdays.length > 0 && (
            <>
              <Typography variant="subtitle1" className="font-semibold text-sky-600 mb-3">
                🎉 Today’s Birthdays
              </Typography>
              {todayBirthdays.map((bday) => (
                <div
                key={bday.id}
                className="flex items-center justify-between bg-gradient-to-r from-blue-100 to-sky-200 p-3 rounded-md mb-2 shadow-md"
              >
                <div className="flex items-center">
                  <Avatar src={bday.image} alt={bday.name} className="w-12 h-12 mr-3" />
                  <div>
                    <Typography className="text-gray-800 font-semibold">{bday.name}</Typography>
                    <Typography className="text-gray-600 text-sm">{bday.designation}</Typography>
                  </div>
                </div>
                <div className="flex items-center h-full">
                  <Typography className="ml-auto text-sky-600 font-bold flex items-center justify-center h-full">
                    {format(parseISO(bday.birthday), "dd MMM")}
                  </Typography>
                </div>
              </div>
              ))}
            </>
          )}

          {/* Upcoming Birthdays */}
          {upcomingBirthdays.length > 0 && (
            <>
              <Typography variant="subtitle1" className="font-semibold text-purple-600 mt-4 mb-3">
                🔜 Upcoming Birthdays
              </Typography>
              {upcomingBirthdays.map((bday) => (
                 <div
                 key={bday.id}
                 className="flex items-center justify-between bg-gradient-to-r from-purple-50 to-violet-100 p-3 rounded-md mb-2 shadow-md"
               >
                 <div className="flex items-center">
                   <Avatar src={bday.image} alt={bday.name} className="w-12 h-12 mr-3" />
                   <div>
                     <Typography className="text-gray-800 font-semibold">{bday.name}</Typography>
                     <Typography className="text-gray-600 text-sm">{bday.designation}</Typography>
                   </div>
                 </div>
                 <div className="flex items-center h-full">
                   <Typography className="ml-auto text-sky-600 font-bold flex items-center justify-center h-full">
                     {format(parseISO(bday.birthday), "dd MMM")}
                   </Typography>
                 </div>
               </div>
              ))}
            </>
          )}

          {/* No Birthdays Available */}
          {todayBirthdays.length === 0 && upcomingBirthdays.length === 0 && (
            <Typography className="text-gray-500 text-center mt-4">
              No upcoming birthdays 🎈
            </Typography>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default BirthdayCard;

