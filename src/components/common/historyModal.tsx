import React, { useEffect, useState, useRef, useCallback } from "react";
import {
  Avatar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  Tab,
  Box,
  Checkbox,
  CircularProgress,
  Typography,
  Modal,
  TextField,
  IconButton,
  InputAdornment,
  useMediaQuery,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import CloseIcon from "@mui/icons-material/Close";
import { EmployeeTableData } from "@/types/ApprovalTypes";
import { getPendingApprovalList } from "@/services/WorkStationServices/ApprovalServices";
import { ClearIcon } from "@mui/x-date-pickers";
import toastNotification from "@/utils/notificationToster";

type approvalRequestType = "Leave" | "Attendance" | "Self Service";

type ApprovalRequestTableProps = {
  open: boolean;
  onClose: () => void;
  approvalRequestName: approvalRequestType;
};

const HistoryModal: React.FC<ApprovalRequestTableProps> = ({
  open,
  onClose,
  approvalRequestName,
}) => {
  const isSmallScreen = useMediaQuery("(max-width: 600px)");
  const defaultCategories = ["Leave", "Attendance", "Self Services"];
  const initialTabIndex = approvalRequestName
    ? defaultCategories.indexOf(approvalRequestName)
    : 0;

  const [activeTab, setActiveTab] = useState(initialTabIndex);
  const [employeeData, setEmployeeData] = useState<EmployeeTableData[]>([]);
  const [loading, setLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [tempSearchQuery, setTempSearchQuery] = useState("");
  const [lastId, setLastId] = useState<number | undefined>(undefined);
  const [hasMore, setHasMore] = useState(true);
  const [showWarning, setShowWarning] = useState(false);

  // Reset data when the active tab changes
  useEffect(() => {
    setLastId(undefined);
    setEmployeeData([]);
    setHasMore(true); // Reset hasMore when the tab changes
  }, [activeTab]);

  // Reset search and warning state when modal opens
  useEffect(() => {
    if (open) {
      setShowWarning(false);
      setTempSearchQuery("");
      setSearchQuery("");
    }
  }, [open]);

  // Intersection Observer for infinite scroll
  const observer = useRef<IntersectionObserver>();
  const lastEmployeeElementRef = useCallback(
    (node: HTMLTableRowElement) => {
      if (isLoadingMore || !hasMore) return;

      if (observer.current) observer.current.disconnect();
      observer.current = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting && hasMore) {
          const lastEmployeeId = employeeData[employeeData.length - 1]?.last_id;
          if (lastEmployeeId) {
            setLastId(lastEmployeeId);
            setIsLoadingMore(true);
          }
        }
      });

      if (node) observer.current.observe(node);
    },
    [isLoadingMore, hasMore, employeeData]
  );

  // Fetch employee data
  const fetchEmployeeData = async (isNewFetch: boolean = false) => {
    console.log(isNewFetch);
    
    if (isNewFetch) {
      setLoading(true);
      setEmployeeData([]); // Reset employeeData for new fetch
      setLastId(undefined); // Reset lastId for new fetch
    } else {
      setIsLoadingMore(true);
    }
    try {
      const data = await getPendingApprovalList(
        defaultCategories[activeTab],
        "",
        searchQuery,
        isNewFetch ? undefined : lastId
      );

      if (data) {
        // If no more data is available, set hasMore to false
        if (data.data.length === 0) {
          setHasMore(false);
        } else {
          setEmployeeData((prev) =>
            isNewFetch ? data.data : [...prev, ...data.data]
          );
          // Append new data to the existing employeeData
          // setEmployeeData((prev) => [...prev, ...(data.data as EmployeeTableData[])]);
        }
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      if (isNewFetch) {
        setLoading(false);
      } else {
        setIsLoadingMore(false);
      }
    }
  };

  // Fetch data when activeTab, searchQuery, or lastId changes
  useEffect(() => {
    fetchEmployeeData(lastId ? false: true);
  }, [activeTab, searchQuery, lastId]);

  // Handle search
  const handleSearch = () => {
    if(!tempSearchQuery){
     setShowWarning(true);
      return;
    }

    // Force a refresh even if the search query is the same
    // by temporarily setting searchQuery to a different value and then to the actual value
    if (tempSearchQuery === searchQuery) {
      // Use a timestamp to ensure the value is different
      setSearchQuery(tempSearchQuery + "_" + Date.now());
      // Use setTimeout to ensure state updates in sequence
      setTimeout(() => {
        setSearchQuery(tempSearchQuery);
      }, 10);
    } else {
      setSearchQuery(tempSearchQuery);
    }
    setEmployeeData([]);
    setLastId(undefined);
    setHasMore(true); // Reset hasMore when performing a new search
  };

  // Handle clear search
  const handleClearSearch = () => {
    setTempSearchQuery("") ;
    setSearchQuery("");
    setLastId(undefined);
    setHasMore(true); // Reset hasMore when clearing the search
    // Reset warning when search is cleared
    if (showWarning) setShowWarning(false);
  };

  const tableHeaders = [
    ["Name", "Id", "Department", "Designation", "Leave Type" , "Start", "End", "LM", "HOD", "HR", "Status" , "Phone Number", "Email", "Work Location"], // Leave
    ["Name", "Id", "Department", "Designation", "Request Type", "Request Date", "Actual Punch In", "Actual Punch Out", "LM", "HOD", "HR", "Status"], // Attendance
    ["Name", "Id", "Department", "Designation", "Job Duration", "Work Location", "Service Type", "Application Date & Time", "Dept. Head", "HR", "Hr Head", "Status"], // Self Service
  ];

  const currentHeaders = tableHeaders[activeTab];
  return (
    <Modal sx={{ position: "absolute" , top: { sm: "-100px", md: "-50px" , lg:"-100px"} }} open={open} onClose={()=>{
      onClose();
      // Reset warning and search queries when modal is closed
      setShowWarning(false);
      setTempSearchQuery("");
      setSearchQuery("");
    }}>
      <Box
        sx={{
          width: "80%",
          bgcolor: "background.paper",
          p: 2,
          m: "auto",
          mt: "10%",
          boxShadow: 24,
          maxHeight: "800px",
          overflow: "auto",
          border: "none",
          outline: "none",
          borderRadius: "10px",
        }}
      >
        <div className="w-full overflow-x-auto">
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              mb: 2,
              overflowX: "auto",
            }}
          >
            <Typography
              variant={isSmallScreen ? "h6" : "h5"}
              sx={{ whiteSpace: "nowrap" }}
            >
              {`History Table`}
            </Typography>
            <IconButton
              onClick={() => {
                // Reset warning and search queries when modal is closed via X button
                setShowWarning(false);
                setTempSearchQuery("");
                setSearchQuery("");
                onClose();
              }}
              size="small"
              sx={{
                color: "red",
                "&:hover": { bgcolor: "red", color: "white" },
              }}
            >
              <CloseIcon />
            </IconButton>
          </Box>
          <Box
            display="flex"
            justifyContent="space-between"
            alignItems="center"
            my={2}
            overflow={"auto"}
          >
            <Tabs
              value={activeTab}
              onChange={(_:any, newValue:any) => setActiveTab(newValue)}
              variant="scrollable"
              scrollButtons="auto"
              sx={{
                borderBottom: "2px solid #ddd",
                "& .MuiTabs-flexContainer": { justifyContent: "flex-start" },
                "& .MuiTab-root": {
                  textTransform: "none",
                  fontWeight: 600,
                  fontSize: "1rem",
                  color: "#333",
                  transition: "0.3s",
                  whiteSpace: "nowrap",
                  "&:hover": {
                    background: "linear-gradient(45deg, #FFA726, #FF7043)",
                    color: "white",
                  },
                  "&.Mui-selected": { color: "#FF5722" },
                },
                "& .MuiTabs-indicator": { backgroundColor: "#FF5722" },
              }}
            >
              {defaultCategories.map((category, index) => (
                <Tab key={index} label={category} />
              ))}
            </Tabs>

            <div>
            <Box
              display="flex"
              alignItems="center"
              border="1px solid #ccc"
              borderRadius="5px"
              overflow="hidden"
              sx={{
                "&:hover": {
                  borderColor: "#1976d2",
                },
              }}
            >
              <TextField
                size="small"
                variant="outlined"
                placeholder="Search..."
                value={tempSearchQuery}
                onChange={(e: any) => {
                  setTempSearchQuery(e.target.value);
                  // Reset warning when user starts typing
                  if (showWarning) setShowWarning(false);
                }}
                onKeyDown={(e: any) => e.key === "Enter" && handleSearch()}
                sx={{
                  flex: 1,
                  "& fieldset": { border: "none" },
                }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      {tempSearchQuery && (
                        <IconButton onClick={handleClearSearch} size="small">
                          <ClearIcon fontSize="small" />
                        </IconButton>
                      )}
                    </InputAdornment>
                  ),
                }}
              />
              <IconButton
                onClick={handleSearch}
                color="primary"
                sx={{
                  transition: "transform 0.3s ease",
                  "&:hover": {
                    transform: "scale(1.4)",
                    backgroundColor: "#1976d2",
                    color: "#FFFFFF",
                  },
                }}
              >
                <SearchIcon />
              </IconButton>
            </Box>
            <p className=" text-xs text-red-600">{showWarning && "Input value for search !"}</p>
            </div>
          </Box>
        </div>

        {loading ? (
            <Box
              display="flex"
              justifyContent="center"
              alignItems="center"
              py={3}
            >
              <CircularProgress />
            </Box>
          ) : (
<TableContainer
              sx={{
                maxHeight: 440,
                overflowY: "auto",
                "&::-webkit-scrollbar": {
                  width: "5px",
                  height: "5px",
                },
                "&::-webkit-scrollbar-thumb": {
                  backgroundColor: "#7ae075",
                  borderRadius: "10px",
                },
                "&::-webkit-scrollbar-track": {
                  backgroundColor: "#f3f4f6",
                },
              }}
            >
              <Table stickyHeader className="table-scroll-container">
                <TableHead>
                  <TableRow sx={{ backgroundColor: "#f5f5f5" , whiteSpace: "nowrap" , textAlign: "center" }}>
                    {currentHeaders.map((header) => (
                      <TableCell
                        key={header}
                        sx={{
                          p: 1,
                          textAlign: "center",
                          fontWeight: "bold",
                          border: "none",
                        }}
                      >
                        {header}
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {employeeData.length > 0 ? (
                    employeeData.map((employee, rowIndex) => (
                      <TableRow
                        key={employee.id}
                        ref={
                          rowIndex === employeeData.length - 1
                            ? lastEmployeeElementRef
                            : null
                        }
                        sx={{
                          backgroundColor:
                            rowIndex % 2 === 0 ? "#f9fafb" : "#f3f4f6",
                          transition: "background-color 0.5s ease",
                        }}
                      >
                      <TableCell
                        sx={{ p: 1, whiteSpace: "nowrap", textAlign: "center", border: "none" }}
                      >
                        <Box
                          display="flex"
                          alignItems="center"
                          justifyContent="between"
                          gap={1}
                        >
                          <Avatar src={employee.image} alt={employee?.name} />
                          <Typography variant="body2" fontWeight="bold">
                            {employee?.name}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell
                            sx={{ p: 1, whiteSpace: "nowrap", textAlign: "center", border: "none" }}
                          >
                            {employee?.unique_id}
                          </TableCell>
                          <TableCell
                            sx={{ p: 1, whiteSpace: "nowrap", textAlign: "center", border: "none" }}
                          >
                            {employee?.department}
                          </TableCell>
                          <TableCell
                            sx={{ p: 1, whiteSpace: "nowrap", textAlign: "center", border: "none" }}
                          >
                            {employee?.designation}
                          </TableCell>
                      {activeTab === 0 && (
                        <>
                        <TableCell
                            sx={{ p: 1, whiteSpace: "nowrap", textAlign: "center", border: "none" }}
                          >
                            {employee?.leave_type}
                         </TableCell>
                         <TableCell
                            sx={{ p: 1, whiteSpace: "nowrap", textAlign: "center", border: "none" }}
                          >
                            {employee?.start_date}
                          </TableCell>
                         <TableCell
                            sx={{ p: 1, whiteSpace: "nowrap", textAlign: "center", border: "none" }}
                          >
                            {employee?.end_date}
                          </TableCell>
                          <TableCell
                            sx={{ p: 1, textAlign: "center", border: "none" }}
                          >
                            <Checkbox
                              checked={employee?.line_manager_status}
                              disabled
                            />
                          </TableCell>
                          <TableCell
                            sx={{ p: 1, textAlign: "center", border: "none" }}
                          >
                            <Checkbox
                              checked={employee?.dept_manager_status}
                              disabled
                            />
                          </TableCell>
                          <TableCell
                            sx={{ p: 1, textAlign: "center", border: "none" }}
                          >
                            <Checkbox checked={employee?.hr_status} disabled />
                          </TableCell>
                          <TableCell
                            sx={{ p: 1, whiteSpace: "nowrap", textAlign: "center", border: "none" }}
                          >
                           {employee?.status}
                          </TableCell>
                         <TableCell
                            sx={{ p: 1, textAlign: "center", border: "none" }}
                          >
                            {employee?.phone}
                          </TableCell>
                         <TableCell
                            sx={{ p: 1, textAlign: "center", border: "none" }}
                          >
                            {employee?.email}
                          </TableCell>
                         <TableCell
                            sx={{ p: 1, whiteSpace: "nowrap", textAlign: "center", border: "none" }}
                          >
                            {employee?.work_location}
                          </TableCell>
                        </>
                      )}

                      {activeTab === 1 && (
                        <>
                          <TableCell
                            sx={{ p: 1, whiteSpace: "nowrap", textAlign: "center", border: "none" }}
                          >
                            {employee?.request_type}
                          </TableCell>
                          <TableCell
                            sx={{ p: 1, whiteSpace: "nowrap", textAlign: "center", border: "none" }}
                          >
                            {employee?.requested_date}
                          </TableCell>
                          <TableCell
                            sx={{ p: 1, whiteSpace: "nowrap", textAlign: "center", border: "none" }}
                          >
                            {employee?.original_from_date}
                          </TableCell>
                          <TableCell
                            sx={{ p: 1, whiteSpace: "nowrap", textAlign: "center", border: "none" }}
                          >
                            {employee?.original_to_date}
                          </TableCell>
                          <TableCell
                            sx={{ p: 1, textAlign: "center", border: "none" }}
                          >
                            <Checkbox
                              checked={employee?.line_manager_status}
                              disabled
                            />
                          </TableCell>
                          <TableCell
                            sx={{ p: 1, textAlign: "center", border: "none" }}
                          >
                            <Checkbox
                              checked={employee?.dept_manager_status}
                              disabled
                            />
                          </TableCell>
                          <TableCell
                            sx={{ p: 1, textAlign: "center", border: "none" }}
                          >
                            <Checkbox checked={employee?.hr_status} disabled />
                          </TableCell>
                          <TableCell
                            sx={{ p: 1, textAlign: "center", border: "none" }}
                          >
                           {employee?.state}
                          </TableCell>
                        </>
                      )}

                      {activeTab === 2 && (
                        <>
                         <TableCell
                            sx={{ p: 1, whiteSpace: "nowrap", textAlign: "center", border: "none" }}
                          >
                            {employee?.job_duration}
                          </TableCell>
                         <TableCell
                            sx={{ p: 1, whiteSpace: "nowrap", textAlign: "center", border: "none" }}
                          >
                            {employee?.work_location}
                          </TableCell>
                          <TableCell
                            sx={{ p: 1, whiteSpace: "nowrap", textAlign: "center", border: "none" }}
                          >
                            {employee?.service_type}
                          </TableCell>
                          <TableCell
                            sx={{ p: 1, textAlign: "center", border: "none" }}
                          >
                            {employee?.application_datetime}
                          </TableCell>
                          <TableCell
                            sx={{ p: 1, textAlign: "center", border: "none" }}
                          >
                            <Checkbox
                              checked={employee?.dept_head_approval_status}
                              disabled
                            />
                          </TableCell>
                          <TableCell
                            sx={{ p: 1, textAlign: "center", border: "none" }}
                          >
                            <Checkbox
                              checked={employee?.hr_approval_status}
                              disabled
                            />
                          </TableCell>
                          <TableCell
                            sx={{ p: 1, textAlign: "center", border: "none" }}
                          >
                            <Checkbox
                              checked={employee?.hr_head_approval_status}
                              disabled
                            />
                          </TableCell>
                          <TableCell
                            sx={{ p: 1, textAlign: "center", border: "none" }}
                          >
                           {employee?.state}
                          </TableCell>
                        </>
                      )}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={currentHeaders.length}
                      sx={{ textAlign: "center", py: 3 }}
                    >
                      No records found
                    </TableCell>
                  </TableRow>
                )}

                {isLoadingMore && (
                  <TableRow>
                    <TableCell
                      colSpan={currentHeaders.length}
                      align="center"
                      sx={{ py: 2 }}
                    >
                      <CircularProgress size={24} />{" "}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
          )}
      </Box>
    </Modal>
  );
};

export default HistoryModal;


// import React, { useEffect, useState, useRef, useCallback } from "react";
// import {
//   Table,
//   TableBody,
//   TableCell,
//   TableContainer,
//   TableHead,
//   TableRow,
//   Tabs,
//   Tab,
//   Box,
//   CircularProgress,
//   TextField,
//   InputAdornment,
//   useMediaQuery,
//   IconButton,
//   Typography,
//   Modal,
//   Avatar,
//   Checkbox,
// } from "@mui/material";
// import SearchIcon from "@mui/icons-material/Search";
// import CloseIcon from "@mui/icons-material/Close";
// import { EmployeeTableData } from "@/types/ApprovalTypes";
// import { PendingApprovalService } from "@/services/WorkStationServices/ApprovalServices";
// import { ClearIcon } from "@mui/x-date-pickers";

// type approvalRequestType = "Leave" | "Attendance" | "Self Service";

// type ApprovalRequestTableProps = {
//   open: boolean;
//   onClose: () => void;
//   approvalRequestName: approvalRequestType;
// };

// const HistoryModal: React.FC<ApprovalRequestTableProps> = ({
//   open,
//   onClose,
//   approvalRequestName,
// }) => {
//   const isSmallScreen = useMediaQuery("(max-width: 600px)");
//   const categories = ["Leave", "Attendance", "Self Services"];
//   const initialTabIndex = categories.indexOf(
//     approvalRequestName === "Self Service"
//       ? "Self Services"
//       : approvalRequestName
//   );

//   const [activeTab, setActiveTab] = useState(initialTabIndex);
//   const [employeeData, setEmployeeData] = useState<EmployeeTableData[]>([]);
//   const [loading, setLoading] = useState(false);
//   const [error, setError] = useState("");
//   const [searchQuery, setSearchQuery] = useState("");
//   const [tempSearchQuery, setTempSearchQuery] = useState("");
//   const [lastId, setLastId] = useState<number | undefined>(undefined);
//   const [hasMore, setHasMore] = useState(true);

//   const observer = useRef<IntersectionObserver | null>(null);

//   // Function to fetch approval data
//   const fetchApprovalList = async () => {
//     if (loading) return; // Prevent duplicate calls
//     setLoading(true);
//     setError("");

//     try {
//       const response = await PendingApprovalService.getPendingApprovalList(
//         categories[activeTab] as "Leave" | "Attendance" | "Self Services",
//         "",
//         searchQuery,
//         lastId
//       );

//       console.log("API Response:", response);

//       if (response?.status === "SUCCESS") {
//         const newData = response?.data?.data || [];
//         setEmployeeData((prev) => [...prev, ...newData]);
//         setHasMore(response?.data?.data?.length > 0);
//       } else {
//         setError("Failed to fetch data");
//       }
//     } catch (e) {
//       console.error("API Error:", e);
//       setError("Something went wrong");
//     }

//     setLoading(false);
//   };

//   // Fetch data on initial load, tab switch, or search
//   useEffect(() => {
//     if (open) {
//       setEmployeeData([]); // Reset data on modal open
//       setLastId(undefined); // Reset pagination
//       fetchApprovalList();
//     }
//   }, [open, activeTab, searchQuery]);

//   // Infinite scrolling logic
//   const lastEmployeeElementRef = useCallback(
//     (node: HTMLTableRowElement) => {
//       if (loading) return;
//       if (observer.current) observer.current.disconnect();

//       observer.current = new IntersectionObserver((entries) => {
//         if (entries[0].isIntersecting && hasMore) {
//           const newLastId = employeeData[employeeData.length - 1]?.id;
//           console.log("Last ID before update:", lastId);
//           console.log("New Last ID:", newLastId);
//           setLastId(newLastId); // Set new last ID for pagination
//         }
//       });

//       if (node) observer.current.observe(node);
//     },
//     [loading, hasMore, employeeData, lastId] // Added lastId in dependency array
//   );

//   useEffect(() => {
//     if (lastId !== undefined) {
//       fetchApprovalList(); // Fetch data when lastId is set
//     }
//   }, [lastId]);

//   const tableHeaders = [
//     ["Name", "Leave Type", "LM", "HOD", "HR"], // Leave
//     ["Name", "Request Type", "Request Date", "LM", "HOD", "HR"], // Attendance
//     ["Name", "Request Detail", "Manager Approval", "HR"], // Self Service
//   ];

//   const currentHeaders = tableHeaders[activeTab];

//   const handleSearch = () => {
//     setSearchQuery(tempSearchQuery);
//   };

//   const handleClearSearch = () => {
//     setTempSearchQuery("");
//     setSearchQuery("");
//   };
