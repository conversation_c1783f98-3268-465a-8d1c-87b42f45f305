import { dateTimeFormatForExcel, getComanyLogoById } from "@/utils/helper";
import ExcelJ<PERSON> from "exceljs";

export async function AttendanceDetailsReport(
  data: any,
  reportName: any,
  startTime: any,
  endTime: any,
  company: any,
  companyId: any
): Promise<void> {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet("Attendance");

  const fetchLogoAndGenerateExcel = async (): Promise<string> => {
    if (companyId) {
      const data = await getComanyLogoById(companyId);
      return data;
    } else {
      
      return "data:image/jpeg;base64,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";
    }
  };

  worksheet.mergeCells(`A1:CT1`);
  const logoRow = worksheet.getRow(1);
  let companyImage = await fetchLogoAndGenerateExcel();

  let companyLogo = workbook.addImage({
    base64: companyImage,
    extension: "jpeg",
  });

  worksheet.addImage(companyLogo, {
    tl: { col: 0, row: 0 },
    ext: { width: 200, height: 100 },
  });
  logoRow.height = 90;

  worksheet.mergeCells(`A2:CT3`);
  const headerRow = worksheet.getRow(2);
  headerRow.height = 25;
  headerRow.getCell(1).value = reportName + " of " + company;
  headerRow.getCell(1).font = { size: 11, bold: true };
  headerRow.getCell(1).alignment = { horizontal: "center", vertical: "middle" };

  // Merge cells AI2 to AO2
  const today = new Date();

  worksheet.mergeCells("A4:CT4");
  const printDateHeaderRow = worksheet.getRow(4);
  printDateHeaderRow.height = 20;
  printDateHeaderRow.getCell(1).value = `Print Date: ${dateTimeFormatForExcel(
    today
  )}`;
  printDateHeaderRow.font = { bold: true }; // Making the font bold
  printDateHeaderRow.getCell(1).alignment = {
    horizontal: "center",
    vertical: "middle",
  }; // Making the font bold

  const secondHeaders = data[0]?.attendance?.attendance?.map(
    (item: any, index: string) => {
      return item?.date;
    }
  );

  worksheet.mergeCells("A5:CT5");
  const nullRow = worksheet.getRow(4);

  const dateHeaders = [...secondHeaders];

  // Initialize starting column index
  let startCol = 3; // Column C

  // Iterate over the date headers
  dateHeaders.forEach((date) => {
    // Calculate ending column index based on the number of columns for each date
    const endCol = startCol + 3;

    // Merge cells for the current date header
    worksheet.mergeCells(6, startCol, 6, endCol);

    // Set the value of the merged cell to the date
    worksheet.getCell(6, startCol).value = date;

    // Increment the starting column index for the next date header
    startCol = endCol + 1;
  });

  // Assuming dateHeaders is an array containing date strings

  // Create an array to store the final result
  const resultArray = ["Id", "Name"];

  // Add 'start', 'end', and 'hour' for each date in dateHeaders
  dateHeaders.forEach((date) => {
    resultArray.push("Start", "End", "Hour", "OT");
  });

  resultArray.push("Total OT Hours");
  resultArray.push("Total Working Hours");
  resultArray.push("Actual Working Hours");
  resultArray.push("Total Minimum Hours");
  resultArray.push("Normal OT");
  resultArray.push("LWP");
  resultArray.push("Issue Date");
  resultArray.push("Cut Days");

  // Define the starting column index
  let currentCol = 1; // Column A

  // Iterate over the resultArray and put values in the worksheet
  resultArray.forEach((value) => {
    worksheet.getCell(7, currentCol).value = value; // Put the value in the 7th row and current column
    currentCol++; // Move to the next column
  });

  const dataObject: any[] = [];

  data.forEach((item: any) => {
    const rowObject: any[] = [];
    rowObject.push(item.unique_id, item.employee_name);
    let total_hours = "";
    let total_over_time = "";
    let total_actual_work_hour = "";
    let total_minimum_hours = "";
    let normal_ot = "";
    let lwp = 0.0;
    let issue_date = 0;
    let cut_days = 0.0;
    Object.values(item.attendance.attendance).forEach((record: any) => {
      let check_in_time: number = parseFloat(
        record.check_in_time?.toString().replace(":", ".")
      );
      let check_out_time: number = parseFloat(
        record.check_out_time?.toString().replace(":", ".")
      );
      let half_day_status = record.half_day_status;
      let late_status = record.late_status;
      let early_exit_status = record.exit_status;
      let attendance_request_status = record.attendance_request;
      let status = record.status;
      let leave_status = record.leave_status;
      total_hours = item?.final_work_hours;
      total_over_time = item?.final_over_time;
      total_actual_work_hour = item?.total_actual_worked_hours;
      total_minimum_hours = item?.total_minimum_hours;
      normal_ot = item?.normal_ot_hours;
      lwp = item?.absent;
      issue_date = item?.late + item?.early;
      cut_days = item?.cut_days + lwp;

      if (status === "Absence") {
        rowObject.push("A Deeppink", "A Deeppink", "");
      } else if (status === "Public Off Day" || status === "Public Off day" || status === "Public off day") {
        if (check_in_time || check_out_time) {
          rowObject.push(
            isNaN(check_in_time) ? "" : `${record.check_in_time}`,
            isNaN(check_out_time) ? "" : `${record.check_out_time}`,
            record.worked_hour === 0 || ""
              ? ""
              : `${parseFloat(record.worked_hour)}`
          );
        } else {
          rowObject.push("DO", "DO", "");
        }
      } else if (status === "Weekend") {
        if (check_in_time || check_out_time) {
          rowObject.push(
            isNaN(check_in_time) ? "" : `${record.check_in_time}`,
            isNaN(check_out_time) ? "" : `${record.check_out_time}`,
            record.worked_hour === 0 ? "" : `${parseFloat(record.worked_hour)}`
          );
        } else {
          rowObject.push("W", "W", "");
        }
      } else if (status === "Casual Leave(CL)") {
        if (check_in_time || check_out_time) {
          rowObject.push(
            isNaN(check_in_time) ? "" : `${record.check_in_time} Violet`,
            isNaN(check_out_time) ? "" : `${record.check_out_time} Violet`,
            record.worked_hour === 0 ? "" : `${parseFloat(record.worked_hour)}`
          );
        } else {
          rowObject.push("CL Violet", "CL Violet", "");
        }
      } else if (status === "Sick Leave (SL)") {
        if (check_in_time || check_out_time) {
          rowObject.push(
            isNaN(check_in_time) ? "" : `${record.check_in_time} Violet`,
            isNaN(check_out_time) ? "" : `${record.check_out_time} Violet`,
            record.worked_hour === 0 ? "" : `${parseFloat(record.worked_hour)}`
          );
        } else {
          rowObject.push("SL Violet", "SL Violet", "");
        }
      } else if (status === "Earned Leave") {
        if (check_in_time || check_out_time) {
          rowObject.push(
            isNaN(check_in_time) ? "" : `${record.check_in_time} Violet`,
            isNaN(check_out_time) ? "" : `${record.check_out_time} Violet`,
            record.worked_hour === 0 ? "" : `${parseFloat(record.worked_hour)}`
          );
        } else {
          rowObject.push("EL Violet", "EL Violet", "");
        }
      } else if (status === "Compensatory") {
        if (check_in_time || check_out_time) {
          rowObject.push(
            isNaN(check_in_time) ? "" : `${record.check_in_time} Violet`,
            isNaN(check_out_time) ? "" : `${record.check_out_time} Violet`,
            record.worked_hour === 0 ? "" : `${parseFloat(record.worked_hour)}`
          );
        } else {
          rowObject.push("C/off Violet", "C/off Violet", "");
        }
      } else if (status === "Miscarriage") {
        rowObject.push("Mis Violet", "Mis Violet", "");
      } else if (status === "Paternity") {
        rowObject.push("PT Violet", "PT Violet", "");
      } else if (status === "Maternity") {
        rowObject.push("MT Violet", "MT Violet", "");
      } else if (status === "LWP") {
        rowObject.push("LWP Violet", "LWP Violet", "");
      } else {
        if (half_day_status) {
          rowObject.push(
            isNaN(check_in_time) ? "Blue" : `${record.check_in_time} Green`,
            isNaN(check_out_time) ? "Blue" : `${record.check_out_time} Green`,
            record.worked_hour === 0
              ? "0.00"
              : `${parseFloat(record.worked_hour)} Green`
          );
        } else if (late_status) {
          rowObject.push(
            isNaN(check_in_time) ? "Blue" : `${record.check_in_time} Red`,
            isNaN(check_out_time) ? "Blue" : `${record.check_out_time}`,
            record.worked_hour === 0
              ? "0.00"
              : parseFloat(record.worked_hour) < 9.0
              ? `${parseFloat(record.worked_hour)} Yellow`
              : `${parseFloat(record.worked_hour)}`
          );
        } else if (early_exit_status) {
          rowObject.push(
            isNaN(check_in_time) ? "Blue" : `${record.check_in_time}`,
            isNaN(check_out_time) ? "Blue" : `${record.check_out_time} Red`,
            record.worked_hour === 0
              ? "0.00"
              : parseFloat(record.worked_hour) < 9.0
              ? `${parseFloat(record.worked_hour)} Yellow`
              : `${parseFloat(record.worked_hour)}`
          );
        } else if (!check_in_time && check_out_time) {
          rowObject.push(
            isNaN(check_in_time) ? "Blue" : `${record.check_in_time} Blue`,
            isNaN(check_out_time) ? "Blue" : `${record.check_out_time}`,
            record.worked_hour === 0
              ? "0.00"
              : parseFloat(record.worked_hour) < 9.0
              ? `${parseFloat(record.worked_hour)} Yellow`
              : `${parseFloat(record.worked_hour)}`
          );
        } else if (check_in_time && !check_out_time) {
          rowObject.push(
            isNaN(check_in_time) ? "Blue" : `${record.check_in_time}`,
            isNaN(check_out_time) ? "Blue" : `${record.check_out_time} Blue`,
            record.worked_hour === 0
              ? "0.00"
              : parseFloat(record.worked_hour) < 9.0
              ? `${parseFloat(record.worked_hour)} Yellow`
              : `${parseFloat(record.worked_hour)}`
          );
        } else {
          rowObject.push(
            isNaN(check_in_time) ? "Blue" : record.check_in_time,
            isNaN(check_out_time) ? "Blue" : record.check_out_time,
            record.worked_hour === 0
              ? "0.00"
              : parseFloat(record.worked_hour) < 9.0
              ? `${parseFloat(record.worked_hour)} Yellow`
              : `${parseFloat(record.worked_hour)}`
          );
        }
      }
      rowObject.push(record?.ot_time || "");
    });
    rowObject.push(total_over_time);
    rowObject.push(total_hours);
    rowObject.push(total_actual_work_hour);
    rowObject.push(total_minimum_hours);
    rowObject.push(normal_ot);
    rowObject.push(lwp);
    rowObject.push(issue_date);
    rowObject.push(cut_days);

    dataObject.push(rowObject);
  });

  // Define the starting row index
  let currentRow = 8;

  // Define the strip fill colors
  const evenRowFill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFDDDDDD" }, // Adjust the color as needed
  };

  const oddRowFill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFFFFFFF" }, // Adjust the color as needed
  };

  // Iterate over each row in dataObject
  dataObject.forEach((row, index) => {
    // Define the starting column index
    let currentCol = 1;

    if (currentRow % 2 === 0) {
      worksheet.getRow(currentRow).fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "FFD9E2F3" },
      };
      worksheet.getRow(currentRow).border = {
        top: { style: "thin", color: { argb: "FFB1B1B1" } },
        left: { style: "thin", color: { argb: "FFB1B1B1" } },
        bottom: { style: "thin", color: { argb: "FFB1B1B1" } },
        right: { style: "thin", color: { argb: "FFB1B1B1" } },
      };
    }

    // Define the fill color for the current row
    const fill = index % 2 === 0 ? evenRowFill : oddRowFill;

    // Iterate over each value in the row
    row.forEach((value: any) => {
      // Set the value in the worksheet
      if (value.toString().includes(" Yellow")) {
        worksheet.getCell(currentRow, currentCol).value = value
          .toString()
          .replace(" Yellow", "");

        worksheet.getCell(currentRow, currentCol).fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "FFFFF200" },
        };

        worksheet.getCell(currentRow, currentCol).border = {
          top: { style: "thin", color: { argb: "FFB1B1B1" } },
          left: { style: "thin", color: { argb: "FFB1B1B1" } },
          bottom: { style: "thin", color: { argb: "FFB1B1B1" } },
          right: { style: "thin", color: { argb: "FFB1B1B1" } },
        };
      } else if (value.toString().includes("Deeppink")) {
        worksheet.getCell(currentRow, currentCol).value = value
          .toString()
          .replace(" Deeppink", "");

        worksheet.getCell(currentRow, currentCol).fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "FFD98880" },
        };

        worksheet.getCell(currentRow, currentCol).border = {
          top: { style: "thin", color: { argb: "FFB1B1B1" } },
          left: { style: "thin", color: { argb: "FFB1B1B1" } },
          bottom: { style: "thin", color: { argb: "FFB1B1B1" } },
          right: { style: "thin", color: { argb: "FFB1B1B1" } },
        };
      } else if (value.toString().includes("Violet")) {
        worksheet.getCell(currentRow, currentCol).value = value
          .toString()
          .replace(" Violet", "");

        worksheet.getCell(currentRow, currentCol).fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "FFA569BD" },
        };

        worksheet.getCell(currentRow, currentCol).border = {
          top: { style: "thin", color: { argb: "FFB1B1B1" } },
          left: { style: "thin", color: { argb: "FFB1B1B1" } },
          bottom: { style: "thin", color: { argb: "FFB1B1B1" } },
          right: { style: "thin", color: { argb: "FFB1B1B1" } },
        };
      } else if (value.toString().includes("Green")) {
        worksheet.getCell(currentRow, currentCol).value = value
          .toString()
          .replace(" Green", "");

        worksheet.getCell(currentRow, currentCol).fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "FF98FB98" },
        };

        worksheet.getCell(currentRow, currentCol).border = {
          top: { style: "thin", color: { argb: "FFB1B1B1" } },
          left: { style: "thin", color: { argb: "FFB1B1B1" } },
          bottom: { style: "thin", color: { argb: "FFB1B1B1" } },
          right: { style: "thin", color: { argb: "FFB1B1B1" } },
        };
      } else if (value.toString().includes("Red")) {
        worksheet.getCell(currentRow, currentCol).value = value
          .toString()
          .replace(" Red", "");

        worksheet.getCell(currentRow, currentCol).fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "FFFF0000" },
        };

        worksheet.getCell(currentRow, currentCol).border = {
          top: { style: "thin", color: { argb: "FFB1B1B1" } },
          left: { style: "thin", color: { argb: "FFB1B1B1" } },
          bottom: { style: "thin", color: { argb: "FFB1B1B1" } },
          right: { style: "thin", color: { argb: "FFB1B1B1" } },
        };
      } else if (value.toString().includes("Blue")) {
        worksheet.getCell(currentRow, currentCol).value = "";

        worksheet.getCell(currentRow, currentCol).fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "FF1E90FF" },
        };

        worksheet.getCell(currentRow, currentCol).border = {
          top: { style: "thin", color: { argb: "FFB1B1B1" } },
          left: { style: "thin", color: { argb: "FFB1B1B1" } },
          bottom: { style: "thin", color: { argb: "FFB1B1B1" } },
          right: { style: "thin", color: { argb: "FFB1B1B1" } },
        };
      } else {
        worksheet.getCell(currentRow, currentCol).value = value;
      }

      // Move to the next column
      currentCol++;
    });

    // Move to the next row
    currentRow++;
  });

  try {
    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer();

    // Trigger file download
    const blob = new Blob([buffer], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${reportName ? reportName : "Report"}.xlsx`;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
  } catch (e) {
    console.log(e);
  }
}
