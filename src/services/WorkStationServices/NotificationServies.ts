import useS<PERSON> from "swr";
import { APIForWorkspace } from "../apiService";
import { AnnouncementsList, BirthdayList, EventListType } from "@/types/notificationTypes";

const fetcher = async <T>(url: string): Promise<{ data: T }> => {
  const response = await APIForWorkspace.get<{ data: T }>(url);
  return response.data;
};
export const getBirthdayList = () => {
  return useSWR<{ data: BirthdayList[] }>("dashboard/birthdays", fetcher);
};
export const getEventList = () => {
  return useSWR<{ data: EventListType[] }>("dashboard/get-events", fetcher);
};
export const getAnnouncementsList = () => {
  return useSWR<{ data: AnnouncementsList[] }>(
    "dashboard/announcements",
    fetcher
  );
};

export const getEmployeeDetails = {
  async employeeData(empId: any) { 
    try {
      const { data } = await APIForWorkspace.get(`profile/users/${empId}` );

      return {
        status: "SUCCESS",
        data: data,
      };
    } catch (e: any) {
      if (e.response) {
        const responseJson = JSON.parse(e.response.request.responseText);
        const errorDetail = responseJson.detail;
        // errorToast(errorDetail);
      } else {
        console.error("Error:", e.message);
      }
      return {
        status: "FAILED",
        message: "Something wrong!!",
        error: e,
      };
    }
  },
};
