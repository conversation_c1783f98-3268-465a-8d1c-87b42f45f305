import Button, { ButtonProps } from "@mui/material/Button";
import { SvgIconProps } from "@mui/material/SvgIcon";
import React from "react";

interface CommonButtonProps extends ButtonProps {
  variant: "contained" | "outlined" | "text";
  icon?: React.ReactElement<SvgIconProps>;
}

const CommonButton: React.FC<CommonButtonProps> = ({
  variant,
  icon,
  children,
  ...rest
}) => {
  return (
    <Button variant={variant} {...rest}>
      {icon &&
        React.cloneElement(icon, {
          fontSize: "small",
          style: { marginRight: "8px" },
        })}
      {children}
    </Button>
  );
};

export default CommonButton;
