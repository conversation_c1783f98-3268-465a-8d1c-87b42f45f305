// api.ts

export async function fetchAttendanceReportData(
    companyName: number
): Promise<Employee[]> {
  const baseUrl: string = process.env.NEXT_PUBLIC_API_URL_FOR_REPORT || '';
  const url = `${baseUrl}/api/attendance/status/report/`;
  const body = {
    company_id: companyName,
    active: true,
    start_date: '2024-04-01',
    end_date: '2024-04-30',
  };

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json', // Specify JSON content type
      },
      body: JSON.stringify(body), // Convert body to JSON string
    });

    if (!response.ok) {
      throw new Error(
          `Failed to fetch employee data: ${response.status} ${response.statusText}`
      );
    }

    const data: Employee[] = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching employee data:', error);
    throw new Error('Failed to fetch employee data');
  }
}

export interface Employee {
  unique_id: string;
  employee_name: string;
  designation: string;
  attendance: {
    [date: string]: string | AttendanceRecord[];
  };
  present: string;
  absent: string;
  leave: string;
  off_day: string;
  holiday: string;
  late: string;
  early: string;
  total_e_time: string;
  night_shift: string;
}

export interface AttendanceRecord {
  date: string;
  check_in_time: string | null;
  check_out_time: string | null;
  worked_hour: number;
  status: string;
}
