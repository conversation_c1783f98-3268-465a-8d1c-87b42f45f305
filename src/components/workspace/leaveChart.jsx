import React from 'react'
import { LineChart } from "@mui/x-charts/LineChart";
import { getLeaveGraphData } from '@/services/WorkStationServices/AttendanceServices';
const LeaveChart = () => {
  const {
      data: leaveGraphResponse,
      error: graphDataError,
      isLoading: graphDataLoading,
    } = getLeaveGraphData();
    const leaveData = leaveGraphResponse?.data || [];
    const chartData = leaveData.map((item) => ({
        x: item.month,
        y: item.leaveCount,
      }));
  return (
    <LineChart
      xAxis={[{ scaleType: "point", data: leaveData.map((item) => item.month) }]}
      yAxis={[
        {
          min: 0,
          max: Math.max(...leaveData.map((item) => item.leaveCount || 0), 1),
        },
      ]}
      series={[{ data: leaveData.map((item) => item.leaveCount), label: "Leaves",color: "#f97316" }]}
      height={420}
    />
  )
}

export default LeaveChart
