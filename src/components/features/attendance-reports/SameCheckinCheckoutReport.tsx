import { dateTimeFormatForExcel, getComanyLogoById } from "@/utils/helper";
import ExcelJ<PERSON> from "exceljs";

export async function SameCheckinCheckoutReport(
  data: any,
  reportName: any,
  startTime: any,
  endTime: any,
  company: any,
  companyId: any
) {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet("Attendance");

  const fetchLogoAndGenerateExcel = async (): Promise<string> => {
    if (companyId) {
      const data = await getComanyLogoById(companyId);
      return data;
    } else {
      console.log("No Comapny Id");
      return "data:image/jpeg;base64,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";
    }
  };
  worksheet.mergeCells(`A1:H1`);
  const logoRow = worksheet.getRow(1);
  let companyImage = await fetchLogoAndGenerateExcel();

  let companyLogo = workbook.addImage({
    base64: companyImage,
    extension: "jpeg",
  });

  worksheet.addImage(companyLogo, {
    tl: { col: 0, row: 0 },
    ext: { width: 200, height: 100 },
  });
  logoRow.height = 90;

  worksheet.mergeCells(`A2:H3`);
  const headerRow = worksheet.getRow(2);
  headerRow.height = 25;
  headerRow.getCell(1).value = reportName + " of " + company;
  headerRow.getCell(1).font = { size: 11, bold: true };
  headerRow.getCell(1).alignment = { horizontal: "center", vertical: "middle" };

  // Merge cells AI2 to AO2
  const today = new Date();

  // Add "From Date" header row
  worksheet.mergeCells(`A4:H4`);
  const fromDateHeaderRow = worksheet.getRow(4);
  fromDateHeaderRow.height = 20;
  fromDateHeaderRow.getCell(1).value = `From Date: ${startTime}`;
  fromDateHeaderRow.getCell(1).alignment = {
    horizontal: "center",
    vertical: "middle",
  };
  fromDateHeaderRow.font = { bold: true }; // Making the font bold

  // Add "To Date" header row
  worksheet.mergeCells(`A5:H5`);
  const toDateHeaderRow = worksheet.getRow(5);
  toDateHeaderRow.height = 20;
  toDateHeaderRow.getCell(1).value = `To Date: ${endTime}`;
  toDateHeaderRow.getCell(1).alignment = {
    horizontal: "center",
    vertical: "middle",
  };
  toDateHeaderRow.font = { bold: true }; // Making the font bold

  // Add "Print Date" header row
  worksheet.mergeCells("A6:H6");
  const printDateHeaderRow = worksheet.getRow(6);
  printDateHeaderRow.height = 20;
  printDateHeaderRow.getCell(1).value = `Print Date: ${dateTimeFormatForExcel(
    today
  )}`;
  printDateHeaderRow.font = { bold: true }; // Making the font bold
  printDateHeaderRow.getCell(1).alignment = {
    horizontal: "center",
    vertical: "middle",
  }; // Making the font bold

  worksheet.mergeCells("A7:H7");
  const DepartmentHeaderRow = worksheet.getRow(7);
  DepartmentHeaderRow.height = 20;

  const headers = [
    "Employee Name",
    "Employee ID",
    "Designation",
    "Location",
    "Total Days",
    "Date",
    "Check In",
    "Check Out",
  ];

  const allHeaders = [...headers];

  // Add data to the Excel sheet
  const row = worksheet.addRow(allHeaders);

  // Rotate cells after the 3rd element by 90 degrees
  for (let i = 4; i <= allHeaders.length; i++) {
    const cell = row.getCell(i);
    cell.alignment = {
      vertical: "middle",
      horizontal: "center",
    };
    cell.font = { bold: true };
  }

  let rowNo = 9;
  let colorPop: any = [];

  function range(start: number, end: number) {
    return Array.from({ length: end - start + 1 }, (_, i) => start + i);
  }

  let count = 0;
  data.attendances.map((value: any, index: any) => {
    let rowLength = value.attendance.length;

    worksheet.mergeCells(`A${rowNo}:A${rowNo + (rowLength - 1)}`);
    let employeeNameRow = worksheet.getRow(rowNo);
    employeeNameRow.getCell(1).value = value.employee_name;
    employeeNameRow.getCell(1).alignment = {
      horizontal: "center",
      vertical: "middle",
    };

    worksheet.mergeCells(`B${rowNo}:B${rowNo + (rowLength - 1)}`);
    let designationRow = worksheet.getRow(rowNo);
    designationRow.getCell(2).value = value.employee_id;
    designationRow.getCell(2).alignment = {
      horizontal: "center",
      vertical: "middle",
    };

    worksheet.mergeCells(`C${rowNo}:C${rowNo + (rowLength - 1)}`);
    let locationRow = worksheet.getRow(rowNo);
    locationRow.getCell(3).value = value["job_name"];
    locationRow.getCell(3).alignment = {
      horizontal: "center",
      vertical: "middle",
    };

    worksheet.mergeCells(`D${rowNo}:D${rowNo + (rowLength - 1)}`);
    let totalDaysRow = worksheet.getRow(rowNo);
    totalDaysRow.getCell(4).value = value.location_name;
    totalDaysRow.getCell(4).alignment = {
      horizontal: "center",
      vertical: "middle",
    };

    worksheet.mergeCells(`E${rowNo}:E${rowNo + (rowLength - 1)}`);
    let dateRow = worksheet.getRow(rowNo);
    dateRow.getCell(5).value = value.attendance.length;
    dateRow.getCell(5).alignment = { horizontal: "center", vertical: "middle" };

    if (index % 2 === 0) {
      worksheet.getRow(rowNo).fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: {
          argb: "98FB98",
        },
      };
      colorPop.push(range(rowNo, rowNo + (rowLength - 1)));
    }

    rowNo += rowLength;



    for (let i = 0; i < value.attendance.length; i++) {
      worksheet.getCell(count+9, 6).value = value.attendance[i].date;
      worksheet.getCell(count+9, 7).value = value.attendance[i].check_in;
      worksheet.getCell(count+9, 8).value = value.attendance[i].check_out;
      count +=1;
    }
  });

  // data.attendances.map((value: any, valueIndex: any) => {
  //
  // });

  colorPop.map((value: any, index: any) => {
    value.map((item: any) => {
      worksheet.getRow(item).fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: {
          argb: "FFD9E2F3",
        },
      };
      worksheet.getRow(item).border = {
        top: { style: "thin", color: { argb: "FFB1B1B1" } }, // Border color is red
        left: { style: "thin", color: { argb: "FFB1B1B1" } },
        bottom: { style: "thin", color: { argb: "FFB1B1B1" } },
        right: { style: "thin", color: { argb: "FFB1B1B1" } },
      };
    });
  });

  worksheet.columns.forEach((column, index) => {
    if (index >= 0 && index <= 5) {
      // Columns D to AS
      column.width = 20;
    }
  });

  try {
    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer();

    // Trigger file download
    const blob = new Blob([buffer], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${reportName ? reportName : "Report"}.xlsx`;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
  } catch (e) {
    console.log(e);
  }
}
