import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>utt<PERSON>,
  <PERSON>dal,
  <PERSON>Field,
  useMediaQuery,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import React, { useState } from "react";
import { useTheme } from "@mui/material/styles";
import { Tabs, Tab, Box } from "@mui/material";
import {
  RiCheckboxBlankCircleLine,
  RiCheckboxCircleFill,
} from "react-icons/ri";
import { getLeaveApplicationHistory } from "@/services/WorkStationServices/ApprovalServices";
import { format } from "date-fns";
interface EmployeeDetailsModalProps {
  selectedEmployee: any | null;
  setSelectedEmployee: any;
  activeTab: number;
  openRejectModal: boolean;
  setSelectedEmployeeId: any;
  setSelectedTabName: (tab: string) => void;
  setOpenRejectModal: (open: boolean) => void;
  handleApprove: any;
}

const EmployeeDetailsModal: React.FC<EmployeeDetailsModalProps> = ({
  setSelectedEmployee,
  selectedEmployee,
  activeTab,
  openRejectModal,
  setSelectedEmployeeId,
  setSelectedTabName,
  setOpenRejectModal,
  handleApprove,
}) => {
  const tabNames: Record<number, string> = {
    0: "Leave",
    1: "Attendance",
    2: "Self Service",
  };

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const [activeTabName, setActiveTabName] = useState(0);
  const [leaveHistoryData, setLeaveHistoryData] = useState<any>([]);
  const handleChange = async (
    event: React.SyntheticEvent,
    newValue: number
  ) => {
    if (newValue === 1) {
      const data = await getLeaveApplicationHistory(selectedEmployee?.id);
      setLeaveHistoryData(data || []);
    }
    setActiveTabName(newValue);
  };


  return (
    <Modal
      className="employee_details_modal"
      open={!!selectedEmployee}
      onClose={() => {
        setSelectedEmployee(null), setActiveTabName(0);
      }}
    >
      <div className="flex justify-center items-center min-h-screen">
        <div className="bg-white w-[75%] md:min-w-[500px] max-w-[900px] p-6 rounded-xl shadow-lg md:max-h-[600px]  overflow-y-scroll">
          {selectedEmployee && (
            <>
              <div className="flex justify-between items-center pb-6">
                <h2 className="text-xl font-semibold text-blue-800">
                  {selectedEmployee.leave_type
                    ? selectedEmployee.leave_type
                    : selectedEmployee.request_type
                    ? selectedEmployee.request_type
                    : selectedEmployee.service_type}
                </h2>
                <IconButton
                  onClick={() => {
                    setSelectedEmployee(null), setActiveTabName(0);
                  }}
                  size="small"
                  sx={{
                    color: "red",
                    "&:hover": { bgcolor: "red", color: "white" },
                  }}
                >
                  <CloseIcon />
                </IconButton>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
                <TextField
                  fullWidth
                  label="Name"
                  //   variant="standard"
                  value={selectedEmployee.name ? selectedEmployee.name : "N/A"}
                  InputProps={{
                    readOnly: true,
                    sx: {
                      "& .MuiOutlinedInput-notchedOutline": {
                        border: "none",
                      },
                      "& .css-1t8l2tu-MuiInputBase-input-MuiOutlinedInput-input ":
                        {
                          padding: "14px 0px 14px 14px",
                        },
                    },
                  }}
                />
                <TextField
                  fullWidth
                  //   variant="standard"
                  label="ID"
                  value={
                    selectedEmployee.unique_id
                      ? selectedEmployee.unique_id
                      : "N/A"
                  }
                  InputProps={{
                    readOnly: true,
                    sx: {
                      "& .MuiOutlinedInput-notchedOutline": {
                        border: "none",
                      },
                      "& .css-1t8l2tu-MuiInputBase-input-MuiOutlinedInput-input ":
                        {
                          padding: "14px 0px 14px 14px",
                        },
                    },
                  }}
                />
                <TextField
                  fullWidth
                  //   variant="standard"
                  label="Department"
                  value={
                    selectedEmployee.department
                      ? selectedEmployee.department
                      : "N/A"
                  }
                  InputProps={{
                    readOnly: true,
                    sx: {
                      "& .MuiOutlinedInput-notchedOutline": {
                        border: "none",
                      },
                      "& .css-1t8l2tu-MuiInputBase-input-MuiOutlinedInput-input ":
                        {
                          padding: "14px 0px 14px 14px",
                        },
                    },
                  }}
                />
                <TextField
                  fullWidth
                  //   variant="standard"
                  label="Designation"
                  value={
                    selectedEmployee.designation
                      ? selectedEmployee.designation
                      : "N/A"
                  }
                  InputProps={{
                    readOnly: true,
                    sx: {
                      "& .MuiOutlinedInput-notchedOutline": {
                        border: "none",
                      },
                      "& .css-1t8l2tu-MuiInputBase-input-MuiOutlinedInput-input ":
                        {
                          padding: "14px 0px 14px 14px",
                        },
                    },
                  }}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
                <TextField
                  fullWidth
                  label="Status"
                  value={selectedEmployee?.status ||selectedEmployee?.state}
                  InputProps={{
                    readOnly: true,
                    sx: {
                      "& .MuiOutlinedInput-notchedOutline": {
                        border: "none",
                      },
                      "& .css-1t8l2tu-MuiInputBase-input-MuiOutlinedInput-input ":
                        {
                          padding: "14px 0px 14px 14px",
                        },
                    },
                  }}
                />

                {activeTab === 0 && (
                  <>
                    <TextField
                      fullWidth
                      label="Start Date"
                      value={
                        selectedEmployee.start_date
                          ? selectedEmployee.start_date
                          : "N/A"
                      }
                      InputProps={{
                        readOnly: true,
                        sx: {
                          "& .MuiOutlinedInput-notchedOutline": {
                            border: "none",
                          },
                          "& .css-1t8l2tu-MuiInputBase-input-MuiOutlinedInput-input ":
                            {
                              padding: "14px 0px 14px 14px",
                            },
                        },
                      }}
                    />
                    <TextField
                      fullWidth
                      label="End Date"
                      value={
                        selectedEmployee.end_date
                          ? selectedEmployee.end_date
                          : "N/A"
                      }
                      InputProps={{
                        readOnly: true,
                        sx: {
                          "& .MuiOutlinedInput-notchedOutline": {
                            border: "none",
                          },
                          "& .css-1t8l2tu-MuiInputBase-input-MuiOutlinedInput-input ":
                            {
                              padding: "14px 0px 14px 14px",
                            },
                        },
                      }}
                    />
                    <TextField
                      fullWidth
                      label="Duration"
                      value={
                        selectedEmployee.duration
                          ? selectedEmployee.duration
                          : "N/A"
                      }
                      InputProps={{
                        readOnly: true,
                        sx: {
                          "& .MuiOutlinedInput-notchedOutline": {
                            border: "none",
                          },
                          "& .css-1t8l2tu-MuiInputBase-input-MuiOutlinedInput-input ":
                            {
                              padding: "14px 0px 14px 14px",
                            },
                        },
                      }}
                    />
                  </>
                )}

                {activeTab === 1 && (
                  <>
                    <TextField
                      fullWidth
                      // variant="standard"
                      label="Application Date"
                      value={
                        selectedEmployee.requested_date
                          ? selectedEmployee.requested_date
                          : "N/A"
                      }
                      InputProps={{
                        readOnly: true,
                        sx: {
                          "& .MuiOutlinedInput-notchedOutline": {
                            border: "none",
                          },
                        },
                      }}
                    />
                    <TextField
                      fullWidth
                      label="Actual Punch In Time"
                      value={
                        selectedEmployee.original_from_date
                          ? selectedEmployee.original_from_date
                          : "N/A"
                      }
                      InputProps={{
                        readOnly: true,
                        sx: {
                          "& .MuiOutlinedInput-notchedOutline": {
                            border: "none",
                          },
                        },
                      }}
                    />
                    <TextField
                      fullWidth
                      label="Actual Punch Out Time"
                      value={
                        selectedEmployee.original_to_date
                          ? selectedEmployee.original_to_date
                          : "N/A"
                      }
                      InputProps={{
                        readOnly: true,
                        sx: {
                          "& .MuiOutlinedInput-notchedOutline": {
                            border: "none",
                          },
                        },
                      }}
                    />
                    {
                      selectedEmployee?.adjustment_reason &&
                      <TextField
                      fullWidth
                      label="Adjustment Reason"
                      value={
                        selectedEmployee?.adjustment_reason
                          ? selectedEmployee?.adjustment_reason
                          : "N/A"
                      }
                      InputProps={{
                        readOnly: true,
                        sx: {
                          "& .MuiOutlinedInput-notchedOutline": {
                            border: "none",
                          },
                        },
                      }}
                      multiline
                      className="col-span-full"
                    />
                    }
                    
                    {
                      selectedEmployee?.reason &&
                      <TextField
                      fullWidth
                      label="Reason"
                      value={
                        selectedEmployee?.reason
                          ? selectedEmployee?.reason
                          : "N/A"
                      }
                      InputProps={{
                        readOnly: true,
                        sx: {
                          "& .MuiOutlinedInput-notchedOutline": {
                            border: "none",
                          },
                        },
                      }}
                      multiline
                      minRows={2}
                      className="col-span-full"
                    />
                    }
                    
                  </>
                )}

                {activeTab === 2 && (
                  <>
                    <TextField
                      fullWidth
                      label="Application Date and Time"
                      value={
                        selectedEmployee.application_datetime
                          ? selectedEmployee.application_datetime
                          : "N/A"
                      }
                      InputProps={{
                        readOnly: true,
                        sx: {
                          "& .MuiOutlinedInput-notchedOutline": {
                            border: "none",
                          },
                        },
                      }}
                    />
                    <TextField
                      fullWidth
                      label="Job Duration"
                      value={
                        selectedEmployee.job_duration
                          ? selectedEmployee.job_duration
                          : "N/A"
                      }
                      InputProps={{
                        readOnly: true,
                        sx: {
                          "& .MuiOutlinedInput-notchedOutline": {
                            border: "none",
                          },
                        },
                      }}
                    />
                  </>
                )}
              </div>

              {activeTab === 0 && (
                <div>
                  {/* <h3 className="text-xl font-semibold py-3 text-blue-800">
                    Documents
                  </h3> */}
                  {selectedEmployee?.attachment?.length > 0 ? (
                    <ul className="list-none mt-2 space-y-2">
                      {selectedEmployee.attachment.map(
                        (doc: any, index: number) => (
                          <li
                            key={index}
                            className="flex items-center justify-between border border-gray-300 rounded-md p-2 bg-gray-50"
                          >
                            <span>{doc.attachment_name}</span>
                            <a
                              href={doc.attachment_url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="bg-blue-600 text-white px-3 py-1 rounded-md text-sm"
                            >
                              Download
                            </a>
                          </li>
                        )
                      )}
                    </ul>
                  ) : (
                    <p className="text-sm text-gray-500">No document given.</p>
                  )}
                </div>
              )}

              <div className="grid w-full">
                <Tabs
                  value={activeTabName}
                  onChange={handleChange}
                  variant="scrollable"
                  scrollButtons="auto"
                  sx={{
                    borderBottom: "2px solid #ddd",
                    "& .MuiTabs-flexContainer": {
                      justifyContent: "flex-start",
                    },
                    "& .MuiTab-root": {
                      textTransform: "none",
                      fontWeight: 600,
                      fontSize: "1rem",
                      color: "#333",
                      transition: "0.3s",
                      whiteSpace: "nowrap",
                      "&:hover": {
                        background: "linear-gradient(45deg, #FFA726, #FF7043)",
                        color: "white",
                      },
                      "&.Mui-selected": { color: "#FF5722" },
                    },
                    "& .MuiTabs-indicator": { backgroundColor: "#FF5722" },
                  }}
                >
                  <Tab label="Approve Status" />
                  {activeTab === 0 && <Tab label="Leave History" />}
                </Tabs>

                <Box>
                  {activeTabName === 0 && (
                    <div className="overflow-x-auto">
                      <table
                        className="min-w-full text-sm text-left border-collapse border-none"
                        style={{ border: "none" }}
                      >
                        <tbody>
                          {selectedEmployee.line_manager && (
                            <tr className="even:bg-gray-50">
                              <td className="p-2 font-medium border-none">
                                {selectedEmployee.line_manager}
                              </td>
                              <td className="p-2 border-none flex items-center justify-center">
                                {selectedEmployee.line_manager_status ? (
                                  <RiCheckboxCircleFill className="text-green-600 text-xl" />
                                ) : (
                                  <RiCheckboxBlankCircleLine className=" text-xl" />
                                )}
                              </td>
                            </tr>
                          )}
                          {
                            (selectedEmployee.dept_manager && selectedEmployee.line_manager !== selectedEmployee.dept_manager) && (
                            <tr className="even:bg-gray-50">
                              <td className="p-2 font-medium border-none">
                                {selectedEmployee.dept_manager}
                              </td>
                              <td className="p-2 border-none flex items-center justify-center">
                                {selectedEmployee.dept_manager_status ? (
                                  <RiCheckboxCircleFill className="text-green-600 text-xl" />
                                ) : (
                                  <RiCheckboxBlankCircleLine className=" text-xl" />
                                )}
                              </td>
                            </tr>
                          )}
                          {
                            (selectedEmployee.hr_manager &&  selectedEmployee.line_manager !== selectedEmployee.hr_manager) && (
                            <tr className="even:bg-gray-50">
                              <td className="p-2 font-medium border-none">
                                {selectedEmployee.hr_manager}
                              </td>
                              <td className="p-2 border-none flex items-center justify-center">
                                {selectedEmployee.hr_status ? (
                                  <RiCheckboxCircleFill className="text-green-600 text-xl" />
                                ) : (
                                  <RiCheckboxBlankCircleLine className=" text-xl" />
                                )}
                              </td>
                            </tr>
                          )}

                          {/* <tr className="even:bg-gray-50">
                            <td className="p-2 font-medium border-none text-green-800">
                              Status
                            </td>
                            <td className="p-2 border-none"></td>
                            <td className="p-2 border-none">
                              <span
                                className={`font-medium ${
                                  selectedEmployee.status ||
                                  selectedEmployee.state !== "Approve"
                                    ? "text-yellow-500"
                                    : "text-green-600"
                                }`}
                              >
                                {selectedEmployee.status ||
                                  selectedEmployee.state}
                              </span>
                            </td>
                          </tr> */}
                        </tbody>
                      </table>
                    </div>     
                  )}

                  {activeTabName === 1 && (
                    <div className="overflow-x-auto max-h-40">
                      <table className="min-w-full text-sm text-left border-collapse border-none">
                        <thead className="bg-gray-100">
                          <tr>
                            <th className="p-2 border-none">Leave Type</th>
                            <th className="p-2 border-none">Start Date</th>
                            <th className="p-2 border-none">End Date</th>
                            <th className="p-2 border-none">Duration</th>
                            <th className="p-2 border-none">Reason</th>
                          </tr>
                        </thead>
                        <tbody>
                          {leaveHistoryData.map((leave: any) => (
                            <tr key={leave.id} className="even:bg-gray-50">
                              <td className="p-2 border-none">
                                {leave.leave_type}
                              </td>
                              <td className="p-2 border-none">
                                {format(
                                  new Date(leave.start_date),
                                  "yyyy-MM-dd"
                                )}
                              </td>
                              <td className="p-2 border-none">
                                {format(new Date(leave.end_date), "yyyy-MM-dd")}
                              </td>
                              <td className="p-2 border-none">
                                {leave.duration}
                              </td>
                              <td className="p-2 border-none">
                                {leave.reason}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </Box>
              </div>

              {!openRejectModal && selectedEmployee.show_button && (
                <div className="mt-10 flex justify-center gap-4">
                  <Button
                    variant="contained"
                    color="success"
                    onClick={() => {
                      handleApprove(selectedEmployee.id, tabNames[activeTab]);
                      setSelectedEmployee(null);
                    }}
                  >
                    Approve
                  </Button>
                  <Button
                    variant="contained"
                    color="error"
                    onClick={() => {
                      setSelectedEmployeeId(selectedEmployee.id);
                      setSelectedTabName(tabNames[activeTab]);
                      setOpenRejectModal(true);
                    }}
                  >
                    Reject
                  </Button>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default EmployeeDetailsModal;
