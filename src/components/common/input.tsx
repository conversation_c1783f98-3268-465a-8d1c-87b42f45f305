import React from "react";
import { Input } from "antd";
import { SearchOutlined } from "@ant-design/icons";

interface SearchInputProps {
    label: string;
    placeholder: string;
    onSearch: (value: string) => void;
    value: any;
    setValue: React.Dispatch<React.SetStateAction<string>>;
}

const SearchInput: React.FC<SearchInputProps> = ({
    label,
    placeholder,
    onSearch,
    value,
    setValue,
}) => {
    const handleSearch = () => {
        onSearch(value);
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setValue(e.target.value);
    };
    return (
        <div className="mb-1 flex w-full flex-col">
            <label className="mb-2">{label}</label>
            <div className="relative">
                <Input
                    value={value}
                    onChange={handleChange}
                    placeholder={placeholder}
                    size="large"
                    onPressEnter={(e) =>
                        onSearch((e.target as HTMLInputElement).value)
                    }
                    className="w-full rounded-lg border border-gray-300 px-3 py-1 pr-2"
                    suffix={
                        <SearchOutlined
                            className="text-2xl cursor-pointer px-2 text-gray-500"
                            onClick={handleSearch}
                        />
                    }
                />
            </div>
        </div>
    );
};

export default SearchInput;
