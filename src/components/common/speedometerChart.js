import React from 'react';
import { Typography } from '@mui/material';
import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';

const SpeedometerChart = ({ percentage }) => {
  const [rotateValue, setRotateValue] = useState(0);

  useEffect(() => {
    // Map percentage to rotation degree (0 to 180)
    const rotation = (percentage / 100) * 180;
    setRotateValue(rotation);
  }, [percentage]);

  return (
    <div className="relative w-80 h-40 mx-auto">
      {/* Background Half Circle */}
      <div className="w-full h-full rounded-t-full bg-gray-300 overflow-hidden relative">
        <div
          className="absolute top-0 left-0 w-full h-full rounded-t-full"
          style={{ backgroundColor: '#00bcd4', clipPath: `inset(${100 - percentage}% 0 0 0)` }}
        />
      </div>

      {/* Indicator Needle */}
      <motion.div
        className="absolute top-1/2 left-1/2 w-1 h-32 bg-gray-700 origin-bottom"
        animate={{ rotate: rotateValue }}
        transition={{ type: 'spring', stiffness: 100 }}
      />

      {/* Labels */}
      <div className="absolute top-full w-full flex justify-between mt-2">
        <Typography variant="body2">0%</Typography>
        <Typography variant="body2">100%</Typography>
      </div>

      {/* Percentage Text */}
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center">
        {/* <Typography variant="h6">Progress</Typography> */}
        <Typography variant="h5">{percentage}%</Typography>
      </div>
    </div>
  );
};

export default SpeedometerChart;
