import { API_ROUTE } from "@/constants/api-routes";
import { RadiusInfo } from "@/interface/schedule";
import { getRequest, patchRequest } from "@/services/apiRequestHandlers";
import { Button, Form, InputNumber, Modal, Select } from "antd";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import showNotification from "../common/notification";
import TextArea from "antd/es/input/TextArea";

interface Props {
  isModalVisible: boolean;
  handleCancel: () => void;
}
const SettingsModal = ({ isModalVisible, handleCancel }: Props) => {
  const [form] = Form.useForm();
  const router = useRouter();
  const [radiusInfo, setRadiusInfo] = useState<RadiusInfo>();

  useEffect(() => {
    getRadiusInfo();
  }, []);

  useEffect(() => {
    form.setFieldsValue(radiusInfo);
  }, [radiusInfo]);

  const getRadiusInfo = async () => {
    try {
      const response:any = await getRequest(`${API_ROUTE.GET_CONFIG}`);
      if (response) setRadiusInfo(response);
    } catch (error) {
      console.error("Error fetching radius info:", error);
    }
  };
  const handleSubmit = async () => {
    form
      .validateFields()
      .then(async (values) => {
        const resp = await patchRequest(`${API_ROUTE.UPDATE_CONFIG}`, values);
        if (resp) {
          showNotification({
            type: "success",
            message: "Successfully Updated the Radius!",
          });
          handleCancel();
        }
      })
      .catch((errorInfo) => {
        console.error("Validation Failed:", errorInfo);
        showNotification({
          type: "error",
          message: "Update Unsuccessful!",
        });
      });
  };
  return (
    <Modal
      open={isModalVisible}
      onCancel={handleCancel}
      centered
      okText="Update"
      onOk={handleSubmit}
    >
      <div>
        <h3 className="mb-4  text-black text-lg font-semibold">Default Radius Settings</h3>
        <Form
          form={form}
          layout="vertical"
          className="w-full"
        >
          <Form.Item name="measurement" label="Measured By">
            <Select
              placeholder="Salary Distance Type"
              options={[
                {
                  value: "Meter",
                  label: "Meter",
                },
                {
                  value: "Km",
                  label: "Kilometer",
                },
              ]}
            />
          </Form.Item>
          <Form.Item
            label="Distance"
            name="default_radius"
            rules={[{ required: true, message: "Please enter Radius!" }]}
            className="w-full"
          >
            <InputNumber
              style={{ width: "100%" }}
              className="w-full"
              min={1}
              placeholder="Type Here..."
            />
          </Form.Item>
          <Form.Item
            label="Remarks"
            name="remarks"
            rules={[
              {
                required: true,
                message: "Please enter the reason behind this!",
              },
            ]}
            className="w-full"
          >
            <TextArea placeholder="Type Here..." />
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
};

export default SettingsModal;
