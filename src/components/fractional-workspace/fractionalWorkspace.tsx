import React, { useState, useEffect } from "react";
import { Box, Typography } from "@mui/material";
import { getAttendanceSummaryForFractionalWorkspace } from "@/services/WorkStationServices/AttendanceServices";
import { AttendanceSummaryItem } from "@/types/attendanceTypes";
import { CircularProgress } from "@mui/material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import isAfter from "date-fns/isAfter";
import { format } from "date-fns";
import AttendaceListTable from "./AttendaceListTable";
import { Button } from "antd";
const FractionalWorkspace = () => {
  const [selectedItem, setSelectedItem] = useState({
    name: "Team Members",
    key: "total",
  });
  const [selectedDate, setSelectedDate] = useState<Date | null>(new Date());
  const [employeeId, setEmployeeId] = useState<string | undefined>(undefined);
  const formattedDate = selectedDate ? format(selectedDate, "yyyy-MM-dd") : "";

  useEffect(() => {
    const storedEmployeeId = localStorage.getItem("employeeId");
    if (storedEmployeeId) setEmployeeId(storedEmployeeId);
  }, []);

  const {
    data: attendanceResponse,
    error: attendanceError,
    isLoading: attendanceLoading,
  } = getAttendanceSummaryForFractionalWorkspace(formattedDate, employeeId);
  const attendanceSummaryList: AttendanceSummaryItem[] =
    attendanceResponse?.data || [];

  const handleItemClick = (item: any) => {
    setSelectedItem(item);
  };

  const disableNextDay = (date: Date) => {
    return isAfter(date, new Date()); // disallow future dates
  };

  return (
    <div className="md:ml-6 py-4 pr-0 md_lg:pl-12 lg:pl-[50px]">
      {/* Today's Attendance Summary */}
      <div className="flex-1 p-3 bg-white shadow-md">
        <div className="flex justify-between items-center gap-4 pb-2">
                    <Typography className="text-sm xl:text-lg !font-bold pb-2 text-sky-800">
              Attendance Summary
            </Typography>
          // <div className="flex md:gap-4">

          //   <Button className="hidden md:block font-bold">
          //     Member Wise Summary
          //   </Button>
          // </div>

          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <DatePicker
              label="Pick a date"
              value={selectedDate}
              onChange={(newValue) => {
                if (newValue) setSelectedDate(newValue);
              }}
              shouldDisableDate={disableNextDay}
              slotProps={{
                textField: {
                  size: "small",
                  className: "bg-white",
                },
              }}
            />
          </LocalizationProvider>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 xl:grid-cols-7 gap-3">
          {attendanceSummaryList?.map((item) => (
            <div
              key={item.name}
              className="relative text-start shadow-lg flex items-center p-2 md:p-2 xl:p-3 transition-transform duration-300 ease-in-out transform hover:scale-105"
              onClick={() => handleItemClick(item)}
            >
              <div className="absolute top-0 left-0 w-1 h-full bg-green-600" />

              {/* Content Wrapper */}
              <div className="flex flex-row items-center justify-start w-full">
                <div className="relative w-16 h-16 flex items-center justify-center">
                  <Box position="relative" display="inline-flex">
                    <CircularProgress
                      variant="determinate"
                      value={100}
                      sx={{ color: "#E0E0E0" }}
                      size={64}
                      thickness={3}
                    />
                    <CircularProgress
                      variant="determinate"
                      value={item?.percent}
                      sx={{
                        color: getProgressColor(item.name),
                        position: "absolute",
                        left: 0,
                      }}
                      size={64}
                      thickness={3}
                    />
                    <Box
                      position="absolute"
                      top={0}
                      left={0}
                      bottom={0}
                      right={0}
                      display="flex"
                      alignItems="center"
                      justifyContent="center"
                    >
                      <div
                        className={`absolute flex items-center justify-center w-full h-full ${getColor(
                          item.name
                        )}`}
                      >
                        <span className="text-sm font-bold">
                          {item?.percent}%
                        </span>
                      </div>
                    </Box>
                  </Box>
                </div>

                {/* Text Content */}
                <div className="flex flex-col pl-4">
                  <div className="text-xs sm:text-sm md:text-sm lg:text-md xl:text-md font-semibold text-sky-800">
                    {item?.name}
                  </div>
                  <div className=" font-bold text-lg text-sky-800">
                    {item?.count}
                  </div>
                </div>
              </div>
            </div>
          ))}
          // <div className="flex justify-center relative md:hidden">
          //   <Button className="sm:absolute sm:right-0 sm:bottom-0 font-bold">
          //     Member Wise Summary
          //   </Button>
          // </div>
        </div>
      </div>
      <AttendaceListTable
        selectedItem={selectedItem}
        title={selectedItem}
        selectedDate={formattedDate}
        setSelectedItem={setSelectedItem}
      />
    </div>
  );
};

const getColor = (name: string) => {
  switch (name) {
    case "My Teams":
      return "text-blue-600";
    case "Absent":
      return "text-red-600";
    case "Present":
      return "text-green-600";
    case "Late":
      return "text-yellow-500";
    case "Early Exit":
      return "text-teal-500";
    case "On Leave":
      return "text-lime-400";
    default:
      return "text-gray-600";
  }
};

const getProgressColor = (name: string) => {
  switch (name) {
    case "My Teams":
      return "#2563eb";
    case "Absent":
      return "#dc2626";
    case "Present":
      return "#16a34a";
    case "Late":
      return "#eab308";
    case "Early Exit":
      return "#14b8a6";
    case "On Leave":
      return "#65a30d";
    default:
      return "#6b7280";
  }
};

export default FractionalWorkspace;
