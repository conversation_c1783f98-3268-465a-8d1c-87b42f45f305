import { <PERSON>complete, TextField } from "@mui/material";
import React, { useEffect, useState } from "react";
import { RxAvatar } from "react-icons/rx";
import { getRequest } from "@/services/apiRequestHandlers";
import { API_ROUTE } from "@/constants/api-routes";
import showNotification from "../common/notification";
import { User } from "@/interface/user";

interface Props {
  companyId: number | undefined;
  selectedUser: any;
  setSelectedUser: React.Dispatch<React.SetStateAction<User[]>>;
}
const EmployeeDropDown = ({ companyId ,selectedUser,setSelectedUser}: Props) => {
  const [selectedOption, setSelectedOption] = useState<{
    image: string | undefined;
    name: string;
    id: string;
    label: string;
  } | null>(null);

  const [options, setOptions] = useState<
    {
      image: string | undefined;
      name: string;
      id: string;
      label: string;
    }[]
  >([]);
  async function getEmployees(query: string) {
    if (companyId) {
      try {
        const response: any = await getRequest<{ data: any[] }>(
          `${API_ROUTE.EMPLOYEE_DROPDOWN_WITH_IMAGE}?company_id=${companyId}&manager_filter=true&query=${query}`
        );

        if (response) {
          const formattedOptions = response?.map((employee: any) => ({
            id: employee.employee_id,
            label: employee.name,
            name: employee.name,
            image: employee.image,
          }));
          setOptions(formattedOptions);
        } else {
          setOptions([]);
        }
      } catch (error) {
        setOptions([]);
        console.error("Error fetching employees:", error);
      }
    }
  }

  useEffect(() => {
    getEmployees("");
  }, [companyId]);

  const handleSelect = async (value: any) => {
    try {
      if (!value) {
        showNotification({
          type: "warning",
          message: "No Employee Id is provided",
        });
        return;
      }
      const userExists = selectedUser.some((user:User) => user.unique_id === value);

      if (userExists) {
        showNotification({
          type: "warning",
          message: "User already exists in the selected list!",
        });
        setSelectedOption(null);
        return;
      }
      const response: any = await getRequest(
        `${API_ROUTE.GET_EMPLOYEE}/${value}`
      );

      // Append the new response as an object to the array
      setSelectedUser((prevSelectedUser) => [...prevSelectedUser, response]);

      // Clear the dropdown after successful selection
      setSelectedOption(null);

      showNotification({
        type: "success",
        message: "User details fetched successfully!",
      });
    } catch (error: any) {
      if (error.response?.status === 409) {
        showNotification({
          message: error?.response?.data?.message,
          type: "error",
        });
      } else {
        showNotification({
          type: "error",
          message: error?.response?.data?.message || "An error occurred.",
        });
      }
    }
  };

  return (
    <div className="w-full mb-4 mt-2">
      <div className="w-full flex flex-col gap-1">
        <p className=" !font-semibold text-md">Select Employees</p>
        <Autocomplete
          options={options}
          size="small"
          sx={{
            width: "100%",
          }}
          getOptionLabel={(option) => option?.label || ""}
          value={selectedOption}
          onChange={(_, newValue) => {
            if (newValue) {
              setSelectedOption(newValue);
              handleSelect(newValue.id);
            }
          }}
          renderOption={(props, option) => (
            <li
              {...props}
              className="flex items-center gap-3 relative group p-3"
            >
              <div className="relative w-10 h-10">
                {option.image ? (
                  <img
                    src={option.image}
                    alt={option.name}
                    className="w-10 h-10 rounded-full object-cover transition-transform duration-300 transform group-hover:scale-150 z-50"
                  />
                ) : (
                  <RxAvatar />
                )}
              </div>
              <span>{option?.name}</span>
            </li>
          )}
          renderInput={(params) => <TextField {...params} variant="outlined" />}
          className="mb-4 w-64 lg:w-[350px]"
        />
      </div>
      <div className="w-full h-[1px] bg-gray-300" />
    </div>
  );
};

export default EmployeeDropDown;
