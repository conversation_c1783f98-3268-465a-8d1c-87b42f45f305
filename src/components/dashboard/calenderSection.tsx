import React, { useEffect, useState } from "react";
import dayjs from "dayjs";
import { FaEyeSlash, FaHandPointLeft, FaHandPointRight } from "react-icons/fa";
import { DayWiseDetails } from "@/interface/dashboard-models";
import LeavePolicy from "./leavePolicy";
import { getRequest } from "@/services/apiRequestHandlers";
import { API_ROUTE } from "@/constants/api-routes";
import { useEmployeeContext } from "@/store/EmployeeContext";
import LoaderForComponent from "../common/LoaderForComponent";
import { LuTimerReset } from "react-icons/lu";
import ReactCardFlip from "react-card-flip";
import TimeCard from "./timeCard";
import { Button, IconButton, MenuItem, Select } from "@mui/material";
import { capitalizeWords } from "@/utils/helper";

const CalenderSection: React.FC = () => {
  const currentYear = dayjs().year();
  const [currentMonth, setCurrentMonth] = useState(dayjs().month());
  const [numOfMonths, setNumOfMonths] = useState<number>(2);
  const [dayWiseDetails, setDayWiseDetails] = useState<DayWiseDetails[]>([]);
  const { employeeId } = useEmployeeContext();
  const [loading, setLoading] = useState<boolean>(false);
  const [flippedMonth, setFlippedMonth] = useState<string>("");
  const [showOnlyTimeCard, setShowOnlyTimeCard] = useState(false);

  const getEmployeeAttendanceDetails = async (
    year: number,
    from: number,
    to: number,
    employeeId: string
  ) => {
    setLoading(true);
    const userId = localStorage.getItem("userEmployeeId");
    try {
      let response: any;
      if (employeeId == userId) {
        response = await getRequest<{ data: DayWiseDetails[] }>(
          `${API_ROUTE.DASHBOARD_MY_WORKSPACE_DATA}?year=${year}&from_month=${from}&to_month=${to}`
        );
      } else {
        response = await getRequest<{ data: DayWiseDetails[] }>(
          `${API_ROUTE.DASHBOARD_MY_WORKSPACE_DATA}?year=${year}&from_month=${from}&to_month=${to}&employee_id=${employeeId}`
        );
      }

      if (response && response.data) {
        setDayWiseDetails(response.data);
      }
    } catch (error) {
      console.error("Error fetching calendar data:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Try to show as many previous months as possible
    let startMonth = currentMonth - (numOfMonths - 1);

    // If we can't go back enough (would go below 0), adjust to start from 0
    if (startMonth < 0) {
      startMonth = 0;
    }

    const endMonth = Math.min(startMonth + numOfMonths - 1, 11);

    getEmployeeAttendanceDetails(
      currentYear,
      startMonth + 1,
      endMonth + 1,
      employeeId
    );
  }, [currentMonth, numOfMonths, employeeId]);

  const getMonthPairs = (monthIndex: number, count: number) => {
    let months = [];

    // Try to show as many previous months as possible
    // Calculate how many months we need to go back to show the desired count
    let startIndex = monthIndex - (count - 1);

    // If we can't go back enough (would go below 0), adjust to start from 0
    // and include more future months
    if (startIndex < 0) {
      startIndex = 0;
    }

    // Ensure we don't go beyond December (index 11)
    for (let i = 0; i < count; i++) {
      const index = startIndex + i;
      if (index > 11) break; // Safety check
      months.push(dayjs().month(index).format("MMMM"));
    }
    return months;
  };

  const [visibleMonths, setVisibleMonths] = useState<string[]>(() => {
    // Initialize with current month and prioritize showing previous months
    return getMonthPairs(currentMonth, numOfMonths);
  });

  useEffect(() => {
    // Update visible months when currentMonth or numOfMonths changes
    // This will prioritize showing previous months
    setVisibleMonths(getMonthPairs(currentMonth, numOfMonths));
  }, [currentMonth, numOfMonths]);

  const handlePrev = () => {
    const newCurrentMonth = currentMonth - numOfMonths;
    if (newCurrentMonth >= 0) {
      setCurrentMonth(newCurrentMonth);
    }
  };

  const handleNext = () => {
    // Move forward by numOfMonths
    const newCurrentMonth = currentMonth + numOfMonths;

    // Make sure we don't go beyond December (index 11)
    if (newCurrentMonth <= 11) {
      setCurrentMonth(newCurrentMonth);
    } else {
      // If going forward would exceed December, set to the maximum value
      // that would still show December
      setCurrentMonth(11);
    }
  };

  const monthNames: string[] = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];
  const getMonthIndex = (monthName: string) => monthNames.indexOf(monthName);
  const toggleFlip = (month: string) => {
    if (showOnlyTimeCard) return;
    setFlippedMonth((prev) => (prev === month ? "" : month));
  };
  const handleTimeCardButtion = () => {
    const newState = !showOnlyTimeCard;
    setShowOnlyTimeCard(newState);
    if (newState) {
      setFlippedMonth("ALL");
    } else {
      setFlippedMonth("");
    }
  };
  return (
    <div className="flex flex-col md_lg:flex-row gap-4 w-full ">
      <div className="px-1 text-center w-full md_lg:basis-[80%]">
        <div className="w-full relative flex flex-col md:flex-row justify-between items-center gap-6 mb-4 ">
          <div className="">
            <Button variant="contained" onClick={handleTimeCardButtion}>
              {showOnlyTimeCard ? (
                <FaEyeSlash size={25} />
              ) : (
                <span className="flex col">
                  <LuTimerReset size={25} /> Card
                </span>
              )}
            </Button>
          </div>
          <div className="flex justify-center items-center">
            <IconButton
              onClick={handlePrev}
              disabled={currentMonth - numOfMonths < 0}
              className="text-blue-600 hover:text-blue-800 transform hover:scale-110 transition duration-200 ease-in-out"
            >
              <FaHandPointLeft size={25} />
            </IconButton>
            <h3 className="text-md font-bold">
              {`${visibleMonths[0]} - ${
                visibleMonths[visibleMonths.length - 1]
              }`}{" "}
              {currentYear}
            </h3>
            <IconButton
              onClick={handleNext}
              disabled={currentMonth >= 11}
              className="text-blue-600 hover:text-blue-800 transform hover:scale-110 transition duration-200 ease-in-out"
            >
              <FaHandPointRight size={25} />
            </IconButton>
          </div>
          <div className="">
            <Select
              size="small"
              value={numOfMonths}
              onChange={(e) => {
                setNumOfMonths(Number(e.target.value));
                setCurrentMonth(dayjs().month());
              }}
              className="absolute right-0 text-sm"
              // sx={{ border: '1px solid #D1D5DB', px: 1, py: 0.5, minWidth: 120 }}
              MenuProps={{
                disableScrollLock: true,
                PaperProps: {
                  style: {
                    maxHeight: 300,
                  },
                },
              }}
            >
              <MenuItem value={2}>Two Months</MenuItem>
              <MenuItem value={3}>Three Months</MenuItem>
              <MenuItem value={4}>Four Months</MenuItem>
              <MenuItem value={5}>Five Months</MenuItem>
              <MenuItem value={6}>Six Months</MenuItem>
              <MenuItem value={7}>Seven Months</MenuItem>
              <MenuItem value={8}>Eight Months</MenuItem>
              <MenuItem value={9}>Nine Months</MenuItem>
              <MenuItem value={10}>Ten Months</MenuItem>
              <MenuItem value={11}>Eleven Months</MenuItem>
              <MenuItem value={12}>Current Year</MenuItem>
            </Select>
          </div>
        </div>
        {loading ? (
          <LoaderForComponent />
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-6 lg:mt-0">
            {visibleMonths?.map((month) => {
              const monthIndex = getMonthIndex(month);
              return showOnlyTimeCard ? (
                <div
                  key={`only-timecard-${month}`}
                  className="p-2 bg-white shadow flex flex-col min-h-[465px]"
                >
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-lg font-semibold">{month}</h3>
                  </div>
                  <div className="flex-1 overflow-y-auto">
                    <TimeCard
                      flipMonthIndex={monthIndex}
                      currentYear={currentYear}
                      type="timeCard"
                    />
                  </div>
                </div>
              ) : (
                <ReactCardFlip
                  isFlipped={flippedMonth === month || flippedMonth === "ALL"}
                  flipDirection="horizontal"
                  key={month}
                >
                  <div className="p-2 shadow flex flex-col h-[480px] bg-white">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-lg font-semibold">{month}</h3>
                      <IconButton
                        onClick={() => toggleFlip(month)}
                        className="text-blue-600 hover:text-blue-800 transform hover:scale-150 transition duration-200 ease-in-out"
                      >
                        <LuTimerReset size={25} />
                      </IconButton>
                    </div>

                    <div className="grid grid-cols-7 text-center font-bold ">
                      {["SUN", "MON", "TUE", "WED", "THU", "FRI", "SAT"].map(
                        (day) => (
                          <div
                            key={day}
                            className="font-bold text-blue-900 lg:text-md text-md opacity-70 pb-4"
                          >
                            {day}
                          </div>
                        )
                      )}
                      {Array.from({
                        length: dayjs(`${currentYear}-${month}-01`).day(),
                      }).map((_, i) => (
                        <div key={`empty-${i}`} className="border-none"></div>
                      ))}
                      {Array.from(
                        {
                          length: dayjs(
                            `${currentYear}-${month}-01`
                          ).daysInMonth(),
                        },
                        (_, i) => {
                          const day = i + 1;
                          const monthIndex = getMonthIndex(month);
                          const dateString = `${currentYear}-${String(
                            monthIndex + 1
                          ).padStart(2, "0")}-${String(day).padStart(2, "0")}`;
                          const currentDate = dayjs(dateString);
                          const dayOfWeek = currentDate.day();
                          const isWeekend = dayOfWeek === 5 || dayOfWeek === 6;

                          const dayDetail = dayWiseDetails.find(
                            (day) => day.date === dateString
                          );

                          const bgColor = dayDetail
                            ? getStatusBgColor(
                                dayDetail.half_day_leave_status
                                  ? capitalizeWords(dayDetail.leave_name)
                                  : capitalizeWords(dayDetail.status)
                              )
                            : isWeekend
                            ? "#ffffff"
                            : "#ffffff";
                          const textColor = dayDetail
                            ? getStatusTextColor(
                                capitalizeWords(dayDetail.status)
                              )
                            : isWeekend
                            ? "#ef4444"
                            : "#000000";

                          const backgroundStyle =
                            dayDetail?.half_day_leave_status
                              ? dayDetail.half_day_leave_period === "am"
                                ? `linear-gradient(to right, ${bgColor} 50%, white 50%)` // Left colored
                                : `linear-gradient(to right, white 50%, ${bgColor} 50%)` // Right colored
                              : bgColor;

                          return (
                            <div className=" flex flex-col items-center justify-center py-2">
                              <div
                                key={day}
                                className="w-[35px] h-[35px] rounded-full flex items-center justify-center font-semibold text-sm"
                                style={{
                                  background: backgroundStyle,
                                  color: dayDetail?.half_day_leave_status
                                    ? "#991b1b"
                                    : textColor,
                                }}
                              >
                                <div className="relative group inline-block">
                                  <span className="font-semibold text-md cursor-default">
                                    {day}
                                  </span>
                                  <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 w-max max-w-xs px-2 py-1 text-sm text-blue-800 bg-white rounded opacity-0 group-hover:opacity-100 transition-opacity z-10">
                                    {!dayDetail?.half_day_leave_status
                                      ? capitalizeWords(dayDetail?.status!)
                                      : `${
                                          capitalizeWords(dayDetail?.status!) ||
                                          ""
                                        }/${
                                          capitalizeWords(
                                            dayDetail?.leave_name!
                                          ) || ""
                                        }`}
                                  </div>
                                </div>
                              </div>

                              {dayDetail && (
                                <div className="flex space-x-[4px] items-center justify-center pb-2 pt-[2px]">
                                  {dayDetail.late_status === "Yes" && (
                                    <span className="w-[8px] h-[8px] bg-[#e50000] rounded-full"></span>
                                  )}
                                  {dayDetail.early_status === "Yes" && (
                                    <span className="w-[8px] h-[8px] bg-[#004cff] rounded-full"></span>
                                  )}
                                  {dayDetail.wfh_status && (
                                    <span className="w-[8px] h-[8px] bg-[#008121] rounded-full"></span>
                                  )}
                                  {dayDetail.adjustment_status && (
                                    <span className="w-[8px] h-[8px] bg-[#A020F0] rounded-full"></span>
                                  )}
                                </div>
                              )}
                            </div>
                          );
                        }
                      )}
                    </div>
                  </div>

                  <div className="p-4 bg-white shadow flex flex-col" key="back">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-lg font-semibold">{month}</h3>
                      <IconButton
                        onClick={() => toggleFlip(month)}
                        className="text-blue-600 hover:text-blue-800 transform hover:scale-150 transition duration-200 ease-in-out"
                      >
                        <FaEyeSlash size={22} />
                      </IconButton>
                    </div>
                    <div className="flex-1 overflow-y-auto">
                      <TimeCard
                        flipMonthIndex={monthIndex}
                        currentYear={currentYear}
                        type="monthly"
                      />
                    </div>
                  </div>
                </ReactCardFlip>
              );
            })}
          </div>
        )}
      </div>
      <div className="w-full md_lg:basis-[20%] justify-end">
        <LeavePolicy
          type={numOfMonths}
          fromMonth={
            visibleMonths.length > 0 ? getMonthIndex(visibleMonths[0]) + 1 : 1
          }
          toMonth={
            visibleMonths.length > 0
              ? getMonthIndex(visibleMonths[visibleMonths.length - 1]) + 1
              : 12
          }
        />
      </div>
    </div>
  );
};

export default CalenderSection;

const getStatusBgColor = (status: string): string => {
  switch (status) {
    case "Present":
    case "Weekend":
      return "#ffffff";
    case "Absence":
      return "#f94144";
    case "Public Off Day":
      return "#87abca";
    case "Earned Leave":
      return "#90be6d";
    case "Sick Leave (SL)":
      return "#43aa8b";
    case "Compensatory":
      return "#8E1616";
    case "Casual Leave(CL)":
      return "#f8961e";
    case "Maternity":
    case "Paternity":
      return "#f472b6";
    default:
      return "#ffffff"; // default white
  }
};

const getStatusTextColor = (status: string): string => {
  switch (status) {
    case "Present":
      return "#063c7a";
    case "Weekend":
      return "#ef4444"; // red-500
    default:
      return "#ffffff"; // most cases white text
  }
};
