import { Bar<PERSON><PERSON> } from "@mui/x-charts/BarChart";
import React, { useEffect, useState } from "react";
import { axisClasses } from "@mui/x-charts/ChartsAxis";

const chartSetting = {
  yAxis: [
    {
      label: "Marks",
    },
  ],
  height: 550,
  sx: {
    [`.${axisClasses.left} .${axisClasses.label}`]: {
      transform: "translate(-10px, 0)",
    },
  },
};

const KpiGraph = ({ graphData }) => {
  const [yearData, setYearData] = useState([]);

  const transformData = (data) => {
    return data.map((item) => {
      const { year, quarters } = item;
      const q1 = quarters[0]?.mark || 0;
      const q2 = quarters[1]?.mark || 0;
      const q3 = quarters[2]?.mark || 0;
      const q4 = quarters[3]?.mark || 0;

      return {
        year: year,
        q1: q1,
        q2: q2,
        q3: q3,
        q4: q4,
      };
    });
  };

  useEffect(() => {
    const data = transformData(graphData);
    setYearData(data);
  }, [graphData]);

  const valueFormatter = (value) => `${value}`;

  return (
    <div className="pl-8 bg-white shadow-md w-full h-full">
      <BarChart
        dataset={yearData}
        borderRadius={10}
        xAxis={[
          {
            scaleType: "band",
            dataKey: "year",
            categoryGapRatio: 0.5, // 👈 still here as requested
          },
        ]}
        yAxis={[
          {
            min: 0,
            max: Math.max(...yearData.map((item) => item.q1 || item.q3 || item.q3 ||item.q4 || 0), 1),
          },
        ]}
        series={[
          {
            dataKey: "q1",
            label: "1st Quarter",
            valueFormatter,
            color: "#90e0ef",
          },
          {
            dataKey: "q2",
            label: "2nd Quarter",
            valueFormatter,
            color: "#00b4d8",
          },
          {
            dataKey: "q3",
            label: "3rd Quarter",
            valueFormatter,
            color: "#0077b6",
          },
          {
            dataKey: "q4",
            label: "4th Quarter",
            valueFormatter,
            color: "#03045e",
          },
        ]}
        {...chartSetting}
      />
    </div>
  );
};

export default KpiGraph;
