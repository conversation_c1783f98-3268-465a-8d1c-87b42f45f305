import { API_ROUTE } from "@/constants/api-routes";
import { DatePicker, Form, Input, Radio, Select } from "antd";
import React, { useEffect, useState } from "react";
import dayjs from "dayjs";
import { User } from "@/interface/user";
import { RadiusInfo } from "@/interface/schedule";
import { getRequest } from "@/services/apiRequestHandlers";

interface Props {
  selectedUser: User[];
  form: any;
  schedulingType: string;
  radiusInfo: RadiusInfo | undefined;
  handleSchedulingTypeChange: (e: any) => void;
  setSingleDate: React.Dispatch<React.SetStateAction<any>>;
  setDateRange: React.Dispatch<React.SetStateAction<any>>;
  setIsDateSelected: React.Dispatch<React.SetStateAction<boolean>>;
}
const ScheduleCreateForm = ({
  selectedUser,
  form,
  schedulingType,
  handleSchedulingTypeChange,
  setSingleDate,
  setDateRange,
  setIsDateSelected,
  radiusInfo,
}: Props) => {
  const { RangePicker } = DatePicker;
  const [shiftList, setShiftList] = useState<any[]>([]);
  const [companyId, setCompanyId] = useState<string>("");
  const [reasonList,setReasonList] = useState<{
    id: number;
    text: string;   
  }[]>([]);

  useEffect(() => {
    if (radiusInfo) {
      form.setFieldsValue({
        radius: `${radiusInfo.default_radius} ${radiusInfo.measurement}`,
      });
    }
  }, [radiusInfo, form]);

  useEffect(() => {
    getShiftList();
    getReasonsList();
  }, [companyId]);

  useEffect(() => {
    const company = localStorage.getItem("userCompanyId");
    setCompanyId(company || "");
  }, []);

  const getShiftList = async () => {
    if (companyId) {
      try {
        const response: any = await getRequest(
          `${API_ROUTE.GET_SHIFT_LIST}?company_id=${companyId}&query=`
        );
        setShiftList(response);
      } catch (error) {
        console.error("Error fetching shift list:", error);
      }
    }
  };

  const getReasonsList = async () => {
    try {
        const response: any = await getRequest(
          `${API_ROUTE.GET_REASONS_LIST}`
        );
        setReasonList(response);
      } catch (error) {
        console.error("Error fetching shift list:", error);
      }
  };
  return (
    <div className="rounded-lg bg-white">
      <Form
        layout="vertical"
        form={form}
        initialValues={{
          schedulingType: "Single",
          shift: selectedUser[0]?.shift_id,
        }}
      >
        <Form.Item
          label="Reason"
          name="reason"
          rules={[
            {
              required: true,
              message: "Please enter the reason !",
            },
          ]}
        >
          <Select placeholder="Select Reason">
            {reasonList?.map((reason) => (
              <Select.Option key={reason.id} value={reason.id}>
                {reason.text}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        {/* Reason Description */}
        <Form.Item
          label="Description"
          name="description"
          rules={[
            {
              required: true,
              message: "Please describe why its for !",
            },
          ]}
        >
          <Input.TextArea placeholder="Meeting with a stakeholder about payroll system." />
        </Form.Item>

        <div className="flex w-full flex-col md:flex-row md:gap-4">
          {/* Scheduling Type */}
          <Form.Item
            label="Scheduling Type"
            name="schedulingType"
            rules={[
              {
                required: true,
                message: "You must choose one option !",
              },
            ]}
            className="w-1/2"
          >
            <Radio.Group
              onChange={handleSchedulingTypeChange}
              value={schedulingType}
            >
              <Radio value="Single">Single</Radio>
              <Radio value="Repetitive">Repetitive</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item
            label="Radius"
            name="radius"
            rules={[
              {
                required: true,
                message: "Please enter Radius !",
              },
            ]}
            className="w-1/2"
          >
            <Input disabled />
          </Form.Item>
        </div>
        <div className="flex w-full flex-col gap-4 md:flex-row">
          <div className="w-1/2">
            {schedulingType === "Single" ? (
              <Form.Item
                label="Select Date"
                name="singleDate"
                rules={[
                  {
                    required: true,
                    message: "Please choose a date !",
                  },
                ]}
              >
                <DatePicker
                  disabledDate={(current) =>
                    current && current.isBefore(dayjs().startOf("day"))
                  }
                  onChange={(val: any) => {
                    setSingleDate(val);
                    setIsDateSelected(true);
                  }}
                  style={{ width: "100%" }}
                />
              </Form.Item>
            ) : (
              <>
                <Form.Item
                  label="Start Date Range"
                  name="dateRange"
                  rules={[
                    {
                      required: true,
                      message: "Please select the date range !",
                    },
                  ]}
                >
                  <RangePicker
                    onChange={(val: any) => {
                      setDateRange(val);
                      setIsDateSelected(true);
                    }}
                    disabledDate={(current) =>
                      current && current.isBefore(dayjs().startOf("day"))
                    }
                  />
                </Form.Item>
              </>
            )}
          </div>
          <div className="w-1/2">
            <Form.Item
              label="Shift"
              name="shift"
              rules={[
                {
                  required: true,
                  message: "Please choose one shift !",
                },
              ]}
            >
              <Select placeholder="Select Shift">
                {shiftList?.map((shift) => (
                  <Select.Option key={shift.id} value={shift.id}>
                    {shift.name}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </div>
        </div>
      </Form>
    </div>
  );
};

export default ScheduleCreateForm;
