import { Button, Table, Typography } from "antd";
import React from "react";
import { DeleteOutlined, EyeOutlined } from "@ant-design/icons";
import { User } from "@/interface/user";
const { Text } = Typography;

interface Props {
    selectedUser: User[];
    setSelectedUser: React.Dispatch<React.SetStateAction<User[]>>;
    setShowScheduleModal: React.Dispatch<
        React.SetStateAction<{
            visible: boolean;
            record?: any;
        }>
    >;
    isDateSelected: boolean;
}
const EmployeeTable = ({
    selectedUser,
    setSelectedUser,
    setShowScheduleModal,
    isDateSelected,
}: Props) => {
    const handleDelete = (id: string) => {
        setSelectedUser((prev) => prev.filter((user) => user.unique_id !== id));
    };
    return (
        <div className="w-full lg:w-1/2">
            <p className="font-semibold !text-md pb-2">Added Employees</p>
            <Table
                columns={[
                    {
                        title: "Emp. Id",
                        dataIndex: "unique_id",
                        key: "unique_id",
                    },
                    {
                        title: "Name",
                        dataIndex: "name",
                        key: "name",
                    },
                    {
                        title: "Dept.",
                        dataIndex: "department_name",
                        key: "department_name",
                    },
                    {
                        title: "View schedule",
                        key: "view",
                        render: (_, record) => (
                            <div className="flex">
                                <Button
                                    icon={<EyeOutlined />}
                                    type="primary"
                                    onClick={() => {
                                        setShowScheduleModal({
                                            visible: true,
                                            record: record,
                                        });
                                    }}
                                    disabled={!isDateSelected}
                                >
                                    View
                                </Button>
                            </div>
                        ),
                    },
                    {
                        title: "Action",
                        key: "action",
                        render: (_, record) => (
                            <div className="flex">
                                <Button
                                    icon={<DeleteOutlined />}
                                    danger
                                    onClick={() =>
                                        handleDelete(record.unique_id)
                                    }
                                >
                                    Delete
                                </Button>
                            </div>
                        ),
                    },
                ]}
                dataSource={selectedUser}
            />
        </div>
    );
};

export default EmployeeTable;
