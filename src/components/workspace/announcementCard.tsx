"use client";
import React from "react";
import { Card, CardContent, Typography, Badge } from "@mui/material";
import { formatDistanceToNow, parseISO, isToday } from "date-fns";

interface Announcement {
  id: number;
  title: string;
  description: string;
  requested_date: string; 
  start_date: string;
  end_date: string;
}

interface AnnouncementCardProps {
  announcements: Announcement[];
}

const AnnouncementCard: React.FC<AnnouncementCardProps> = ({
  announcements,
}) => {
  return (
    <Card className="w-full shadow-lg !rounded-none">
      <CardContent>
        <Typography variant="h6" className="font-semibold">
          📢 Announcements
        </Typography>
        <div className="mt-3 p-1 h-[420px] overflow-y-auto">
          {announcements?.length > 0 ? (
            announcements?.map((announcement, index) => {
              if (!announcement?.start_date) return null; // Prevents error if undefined

              const parsedDate = parseISO(announcement.start_date);
              const timeAgo = isToday(parsedDate)
                ? "New"
                : formatDistanceToNow(parsedDate, { addSuffix: true });

              return (
                <div
                  key={announcement.id}
                  className={`flex justify-between items-center rounded-md p-3 mb-2 ${
                    index % 2 === 0 ? "bg-gray-100" : "bg-gray-200"
                  }`}
                >
                  <div>
                    <Typography className="text-gray-700 font-medium">
                      {announcement?.title}
                    </Typography>
                    {announcement.description && (
                      <Typography className="text-gray-500 text-sm">
                        {announcement?.description?.split("\n").map((line, i) => (
                          <p key={i}>{line}</p>
                        ))}
                      </Typography>
                    )}
                  </div>
                  <div className="mr-3 ml-3">
                    <Badge
                      badgeContent={timeAgo}
                      color={isToday(parsedDate) ? "success" : "default"}
                    />
                  </div>
                </div>
              );
            })
          ) : (
            <Typography className="text-orange-400 text-center mt-2">
              No Announcements Available Right Now
            </Typography>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default AnnouncementCard;
