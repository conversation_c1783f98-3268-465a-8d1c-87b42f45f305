import React from "react";
import { Bar<PERSON><PERSON> } from "@mui/x-charts/BarChart";
import { axisClasses } from "@mui/x-charts/ChartsAxis";
import { getAttendanceGraph } from "@/services/WorkStationServices/AttendanceServices";
const chartSetting = {
  yAxis: [
    {
      label: "Count",
    },
  ],
  height: 420,
  sx: {
    [`.${axisClasses.left} .${axisClasses.label}`]: {
      transform: "translate(-10px, 0)",
    },
  },
};

export default function AttendanceBarChart() {
  const {
    data: attendanceGraphResponse,
    error: attendanceGraphError,
    isLoading: attendanceGraphLoading,
  } = getAttendanceGraph();
  const attendanceData = attendanceGraphResponse?.data || [];
  
  const valueFormatter = (value) => `${value}`;

  return (
    <div className="w-full pt-2 pl-4">
      <BarChart
        dataset={attendanceData}
        xAxis={[{ scaleType: "band", dataKey: "date" }]}
         yAxis={[
          {
            min: 0,
            max: Math.max(...attendanceData.map((item) => item.present || item.absent || 0 ), 1),
          },
        ]}
        series={[
          {
            dataKey: "present",
            label: "Present",
            valueFormatter,
            color: "#3b82f6",
          },
          {
            dataKey: "absent",
            label: "Absent",
            valueFormatter,
            color: "#f44336",
          },
          {
            dataKey: "lateEntry",
            label: "Late Entry",
            valueFormatter,
            color: "#ff9800",
          },
          {
            dataKey: "earlyExit",
            label: "Early Exit",
            valueFormatter,
            color: "#c026d3",
          },
        ]}
        {...chartSetting}
      />
    </div>
  );
}
