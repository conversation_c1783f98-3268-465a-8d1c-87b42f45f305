import { Url } from "next/dist/shared/lib/router/router";

export interface ApprovalRequestSummary {
    name: string;
    count: number;
  }
export interface AttendanceApprovalRequestList {
  //  id: number;
  //  unique_id: string;
  //  name: string;
  //  designation: string;
  //  department: string;
  //  image: string;
  //  request_type: string;
  //  requested_date: string;
  //  line_manager_status:boolean;
  //  dept_manager_status:boolean;
  //  hr_status:boolean;
  id: number;
  unique_id: string;
  name: string;
  designation: string;
  department: string;
  image: string;
  leave_type: string;
  start_date:Date;
  end_date:Date;
  line_manager_status:boolean;
  dept_manager_status:boolean;
  hr_status:boolean;
  request_type: string;
 requested_date: string;
 job_duration: string;
 work_location: string;
 state: string;
 application_datetime: string;
 service_type: string;
 dept_head_approval_status:boolean;
 hr_approval_status:boolean;
 hr_head_approval_status:boolean;
 requestDetail:string;
 managerApproved:boolean
  }

export interface LeaveApprovalRequestList {
  //  id: number;
  //  unique_id: string;
  //  name: string;
  //  designation: string;
  //  department: string;
  //  image: string;
  //  leave_type: string;
  //  start_date:Date;
  //  end_date:Date;
  //  line_manager_status:boolean;
  //  dept_manager_status:boolean;
  //  hr_status:boolean;
  id: number;
  unique_id: string;
  name: string;
  designation: string;
  department: string;
  image: string;
  leave_type: string;
  start_date:Date;
  end_date:Date;
  line_manager_status:boolean;
  dept_manager_status:boolean;
  hr_status:boolean;
  request_type: string;
 requested_date: string;
 job_duration: string;
 work_location: string;
 state: string;
 application_datetime: string;
 service_type: string;
 dept_head_approval_status:boolean;
 hr_approval_status:boolean;
 hr_head_approval_status:boolean;
 requestDetail:string;
 managerApproved:boolean
  }
export interface SelfServiceApprovalRequestList {
  //  id: number;
  //  employee_name: string;
  //  name: string;
  //  designation: string;
  //  department: string;
  //  job_duration: string;
  //  work_location: string;
  //  image:string;
  //  state: string;
  //  application_datetime: string;
  //  service_type: string;
  //  dept_head_approval_status:boolean;
  //  hr_approval_status:boolean;
  //  hr_head_approval_status:boolean;
  id: number;
  unique_id: string;
  name: string;
  designation: string;
  department: string;
  image: string;
  leave_type: string;
  start_date:Date;
  end_date:Date;
  line_manager_status:boolean;
  dept_manager_status:boolean;
  hr_status:boolean;
  request_type: string;
 requested_date: string;
 job_duration: string;
 work_location: string;
 state: string;
 application_datetime: string;
 service_type: string;
 dept_head_approval_status:boolean;
 hr_approval_status:boolean;
 hr_head_approval_status:boolean;
 requestDetail:string;
 managerApproved:boolean
  }

  export interface approvalRequestType {
    leave:string;
    attendance:string;
    selfService:string
  } ;

  export interface EmployeeTableData {
    id: number;
    unique_id: string;
    name: string;
    designation: string;
    department: string;
    image: string;
    leave_type: string;
    start_date:string;
    end_date:string;
    line_manager_status:boolean;
    dept_manager_status:boolean;
    hr_status:boolean;
    request_type: string;
   requested_date: string;
   job_duration: string;
   work_location: string;
   state: string;
   application_datetime: string;
   service_type: string;
   dept_head_approval_status:boolean;
   hr_approval_status:boolean;
   hr_head_approval_status:boolean;
   requestDetail:string;
   managerApproved:boolean;
   duration:string;
   status:string
   adjustment_reason:string
   original_from_date:string
   original_to_date:string
   attachment:[title:string,Url:string]
   phone:string
   email:string
   show_button:boolean
   last_id:number
   }