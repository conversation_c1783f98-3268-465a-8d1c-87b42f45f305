import React, { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON> } from "@mui/x-charts/BarChart";
import { axisClasses } from "@mui/x-charts/ChartsAxis";
import { useEmployeeContext } from "@/store/EmployeeContext";
import { getRequest } from "@/services/apiRequestHandlers";
import { API_ROUTE } from "@/constants/api-routes";
import { Typography } from "@mui/material";

const chartSetting = {
  yAxis: [
    {
      label: "Count",
    },
  ],
  height: 485,
  sx: {
    [`.${axisClasses.left} .${axisClasses.label}`]: {
      transform: "translate(-10px, 0)",
    },
  },
};

const AdjustmentAnalysisGraph = () => {
  const [attendanceData, setAttendanceData] = useState([]);
  const { employeeId } = useEmployeeContext();

  const getEmployeeAdjustmentDetails = async (employeeId) => {
    const userId = localStorage.getItem("userEmployeeId");
    try {
      let response;
      if(employeeId==userId){
        response = await getRequest(
          `${API_ROUTE.DASHBOARD_ADJUSTMENTS_GRAPH}`
        );
      } else {
        response = await getRequest(
          `${API_ROUTE.DASHBOARD_ADJUSTMENTS_GRAPH}?employee_id=${employeeId}`
        );
      }

      if (response && response.data) {
        setAttendanceData(response.data);
      }
    } catch (error) {
      console.error("Error fetching adjustment data:", error);
    }
  };

  useEffect(() => {
    getEmployeeAdjustmentDetails(employeeId);
  }, [employeeId]);

  const valueFormatter = (value) => `${value}`;

  return (
    <div className="pl-8 bg-white shadow-md w-full h-full">
      <Typography className="text-lg !font-bold text-center pt-4">
        Last 6 Months(Approved)
      </Typography>
      <BarChart
        dataset={attendanceData}
        borderRadius={10}
        xAxis={[
          {
            scaleType: "band",
            dataKey: "month",
            categoryGapRatio: 0.5,
          },
        ]}
        yAxis={[
          {
            min: 0,
            max: Math.max(...attendanceData?.map((item) => item.date_adjustment_count || item.wfh_count || 0), 1),
          },
        ]}
        series={[
          {
            dataKey: "wfh_count",
            label: "WFH Requests",
            valueFormatter,
            color: "#008121",
          },
          {
            dataKey: "date_adjustment_count",
            label: "Adjustment Requests",
            valueFormatter,
            color: "#A020F0",
          },
        ]}
        {...chartSetting}
      />
    </div>
  );
};

export default AdjustmentAnalysisGraph;
