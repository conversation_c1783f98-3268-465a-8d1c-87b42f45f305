import {dateTimeFormatForExcel, getComanyLogoById} from "@/utils/helper";
// @ts-ignore
import ExcelJS from "exceljs";

export async function EmployeeSeparationDetailsReport(
    data: any,
    reportName: any,
    company: any,
    departmentName: any,
    companyId: any
): Promise<void> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Attendance");

    const fetchLogoAndGenerateExcel = async (): Promise<string> => {
        if (companyId) {
            const data = await getComanyLogoById(companyId);
            return data;
        } else {
            return "data:image/jpeg;base64,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";
        }
    };

    worksheet.mergeCells(`A1:L1`);
    const logoRow = worksheet.getRow(1);
    let companyImage = await fetchLogoAndGenerateExcel();

    let companyLogo = workbook.addImage({
        base64: companyImage,
        extension: "jpeg",
    });

    worksheet.addImage(companyLogo, {
        tl: {col: 0, row: 0},
        ext: {width: 200, height: 100},
    });
    logoRow.height = 90;

    worksheet.mergeCells(`A2:L3`);
    const headerRow = worksheet.getRow(2);
    headerRow.height = 25;
    headerRow.getCell(1).value = reportName + " of " + company;
    headerRow.getCell(1).font = {size: 11, bold: true};
    headerRow.getCell(1).alignment = {horizontal: "center", vertical: "middle"};
    // date
    const today = new Date();

    worksheet.mergeCells(`A4:L4`);
    const printDate = worksheet.getRow(4);
    printDate.getCell(1).value = `Print Date: ${dateTimeFormatForExcel(today)}`;
    printDate.getCell(1).font = {size: 11};
    printDate.getCell(1).alignment = {horizontal: "center", vertical: "middle"};

    worksheet.mergeCells(`A5:L5`);
    const department = worksheet.getRow(5);
    department.getCell(1).value = departmentName
        ? `Department of ${departmentName}`
        : "";
    department.getCell(1).font = {size: 11};
    department.getCell(1).alignment = {
        horizontal: "center",
        vertical: "middle",
    };

    const alphabet = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I'];
    const page1HeaderString = ['SL', 'ID', 'Name', 'Designation', 'Department', 'Date of Joining', 'Joining Designation', 'Separation Date', 'Work Location']; // These will be added; 'Joining Salary', 'Current Salary'

    for (let i = 0; i < alphabet.length; i++) {
        // Merge the cells from row 7 to 8 in the respective column
        worksheet.mergeCells(`${alphabet[i]}7:${alphabet[i]}8`);

        // Get the cell in row 7, column i+1 (convert alphabet to index)
        let cell = worksheet.getCell(`${alphabet[i]}7`);

        // Set the cell value
        cell.value = page1HeaderString[i];

        // Set the font size
        cell.font = {size: 11};

        // Set the alignment
        cell.alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        cell.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: {
                argb: "FFC6E0B4",
            },
        };

        worksheet.getColumn(alphabet[i]).width = page1HeaderString[i].length + 2;
    }

    const page2LowerHeaderString = ['Joining Salary', 'Current Salary','Salary Increment','Effective Date', ]
    worksheet.mergeCells(`J7:M7`);
    const salaryHistoryDetails = worksheet.getCell(`J7`);
    salaryHistoryDetails.value = 'Salary History'
    salaryHistoryDetails.font = {size: 11, bold:true};
    salaryHistoryDetails.alignment = {
        horizontal: "center",
        vertical: "middle",
    };
    salaryHistoryDetails.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: {
            argb: "FFC6E0B4",
        },
    }

    const educationalHeaderString = ['Exam', 'Major', 'Year', 'Result', 'Institution']

    worksheet.mergeCells(`N7:R7`);
    const educationalDetails = worksheet.getCell(`N7`);
    educationalDetails.value = 'Educational Details'
    educationalDetails.font = {size: 11,bold:true};
    educationalDetails.alignment = {
        horizontal: "center",
        vertical: "middle",
    };
    educationalDetails.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: {
            argb: "FFC6E0B4",
        },
    }
    // worksheet.getColumn('J7').width = "Educational Details".length + 2;

    const page2LowerHeaderIndex = [ 'J', 'K', 'L', 'M']

    for (let i = 0; i < page2LowerHeaderString.length; i++) {
        worksheet.mergeCells(`${page2LowerHeaderIndex[i]}8:${page2LowerHeaderIndex[i]}8`);
        // Get the cell in row 7, column i+1 (convert alphabet to index)
        let cell = worksheet.getCell(`${page2LowerHeaderIndex[i]}8`);

        // Set the cell value
        cell.value = page2LowerHeaderString[i];

        // Set the font size
        cell.font = {size: 11};

        // Set the alignment
        cell.alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        cell.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: {
                argb: "FFC6E0B4",
            },
        };
    }

    const educationalHeaderIndex = ['N','O','P', 'Q', 'R']

    for (let i = 0; i < educationalHeaderString.length; i++) {
        worksheet.mergeCells(`${educationalHeaderIndex[i]}8:${educationalHeaderIndex[i]}8`);
        // Get the cell in row 7, column i+1 (convert alphabet to index)
        let cell = worksheet.getCell(`${educationalHeaderIndex[i]}8`);

        // Set the cell value
        cell.value = educationalHeaderString[i];

        // Set the font size
        cell.font = {size: 11};

        // Set the alignment
        cell.alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        cell.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: {
                argb: "FFC6E0B4",
            },
        };
    }
    console.log("Everything is alright till now!");

    const experienceHeaderString = ['Employer', 'Position', 'From', 'To', 'Duration']

    worksheet.mergeCells(`S7:W7`);
    const experienceDetails = worksheet.getCell(`S7`);
    experienceDetails.value = 'Experience Details'
    experienceDetails.font = {size: 11 ,bold:true};
    experienceDetails.alignment = {
        horizontal: "center",
        vertical: "middle",
    };
    experienceDetails.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: {
            argb: "FFC6E0B4",
        },
    }

    const experienceHeaderIndex = ['S','T','U', 'V', 'W']

    for (let i = 0; i < experienceHeaderString.length; i++) {
        worksheet.mergeCells(`${experienceHeaderIndex[i]}8:${experienceHeaderIndex[i]}8`);
        // Get the cell in row 7, column i+1 (convert alphabet to index)
        let cell = worksheet.getCell(`${experienceHeaderIndex[i]}8`);

        // Set the cell value
        cell.value = experienceHeaderString[i];

        // Set the font size
        cell.font = {size: 11};

        // Set the alignment
        cell.alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        cell.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: {
                argb: "FFC6E0B4",
            },
        };
    }

    const workDurationIndex = ['X', 'Y'];
    const workDurationString = ['Previous Airlines/Travel Agency Experience','USBA working Duration']

    for (let i = 0; i < workDurationIndex.length; i++) {
        // Merge the cells from row 7 to 8 in the respective column
        // const mergeCells = `${workDurationIndex[i]}7:${workDurationIndex[i]}8`;
        worksheet.mergeCells(`${workDurationIndex[i]}7:${workDurationIndex[i]}8`);

        // Get the cell in row 7, column i+1 (convert alphabet to index)
        let cell = worksheet.getCell(`${workDurationIndex[i]}7`);

        // Set the cell value
        cell.value = workDurationString[i];

        // Set the font size
        cell.font = {size: 11};

        // Set the alignment
        cell.alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        cell.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: {
                argb: "FFC6E0B4",
            },
        };

        worksheet.getColumn(workDurationIndex[i]).width = workDurationString[i].length + 2;
    }
    // worksheet.mergeCells(`V7:V8`);
    // const previousAgencyExperience = worksheet.getCell(`V7`);
    // previousAgencyExperience.value = 'Previous Airlines/Travel Agency Experience'
    // previousAgencyExperience.font = {size: 11};
    // previousAgencyExperience.alignment = {
    //     horizontal: "center",
    //     vertical: "middle",
    // };
    // previousAgencyExperience.fill = {
    //     type: "pattern",
    //     pattern: "solid",
    //     fgColor: {
    //         argb: "FFC6E0B4",
    //     },
    // }
    // // worksheet.getColumn('R7').width = "Previous Airline/Travel Agency Experience".length + 2;

    // worksheet.mergeCells(`W7:W8`);
    // const workingDuration = worksheet.getCell(`W7`);
    // workingDuration.value = 'USBA working Duration'
    // workingDuration.font = {size: 11};
    // workingDuration.alignment = {
    //     horizontal: "center",
    //     vertical: "middle",
    // };
    // workingDuration.fill = {
    //     type: "pattern",
    //     pattern: "solid",
    //     fgColor: {
    //         argb: "FFC6E0B4",
    //     },
    // }

    console.log("All okay till W7");

    // worksheet.mergeCells(`Z7:AG7`);
    // const page3PersonalDetails = worksheet.getCell(`Z7`);
    // page3PersonalDetails.value = "Personal Details";
    // page3PersonalDetails.font = {size: 11, bold: true};
    // page3PersonalDetails.alignment = {horizontal: "center", vertical: "middle"};
    // page3PersonalDetails.fill = {
    //     type: "pattern",
    //     pattern: "solid",
    //     fgColor: {
    //         argb: "FFC6E0B4",
    //     },
    // }
    //
    // const page3LowerHeaderString = ['NID', 'Present Address', 'Permanent Address', 'Date of Birth', 'Marital Status', 'Fathers Name', 'Mothers Name', 'Contact']
    // const page3LowerHeaderIndex = ['Z', 'AA', 'AB', 'AC','AD','AE', 'AF', 'AG']
    //
    // for (let i = 0; i < page3LowerHeaderString.length; i++) {
    //     worksheet.mergeCells(`${page3LowerHeaderIndex[i]}8:${page3LowerHeaderIndex[i]}8`);
    //     // Get the cell in row 7, column i+1 (convert alphabet to index)
    //     let cell = worksheet.getCell(`${page3LowerHeaderIndex[i]}8`);
    //
    //     // Set the cell value
    //     cell.value = page3LowerHeaderString[i];
    //
    //     // Set the font size
    //     cell.font = {size: 11};
    //
    //     // Set the alignment
    //     cell.alignment = {
    //         horizontal: "center",
    //         vertical: "middle",
    //     };
    //
    //     cell.fill = {
    //         type: "pattern",
    //         pattern: "solid",
    //         fgColor: {
    //             argb: "FFC6E0B4",
    //         },
    //     };
    // }
    //
    console.log("All okay till AE7");

    worksheet.mergeCells(`Z7:AE7`);
    const professionalCertification = worksheet.getCell(`Z7`);
    professionalCertification.value = "Professional Certification";
    professionalCertification.font = {size: 11, bold: true};
    professionalCertification.alignment = {horizontal: "center", vertical: "middle"};
    professionalCertification.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: {
            argb: "FFC6E0B4",
        },
    }

    const professionalCertificationString = ['Certification Name','Institute Name','Result', 'Start Date', 'End Date' ,'Duration'];
    const professionalCertificationIndex = ['Z', 'AA','AB','AC','AD', 'AE']

    for (let i = 0; i < professionalCertificationString.length; i++) {
        worksheet.mergeCells(`${professionalCertificationIndex[i]}8:${professionalCertificationIndex[i]}8`);
        // Get the cell in row 7, column i+1 (convert alphabet to index)
        let cell = worksheet.getCell(`${professionalCertificationIndex[i]}8`);

        // Set the cell value
        cell.value = professionalCertificationString[i];

        // Set the font size
        cell.font = {size: 11};

        // Set the alignment
        cell.alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        cell.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: {
                argb: "FFC6E0B4",
            },
        };
    }

    worksheet.columns.forEach((item: any) => {
        item.border = {
            top: {style: "thin", color: {argb: "FFB1B1B1"}}, // Border color is red
            left: {style: "thin", color: {argb: "FFB1B1B1"}},
            bottom: {style: "thin", color: {argb: "FFB1B1B1"}},
            right: {style: "thin", color: {argb: "FFB1B1B1"}},
        }
    })

    let rowNo = 9;
    let colorPop: any = [];

    function range(start: number, end: number) {
        return Array.from({length: end - start + 1}, (_, i) => start + i);
    }

    let rowLengthForMerge: any[] = []

    data?.map((value: any, index: any) => {
        let educationInfoLength = value?.educational_info?.length;
        let jobHistoryLength = value?.job_history?.length;
        let salaryHistoryLength = value?.salary_history?.length;
        let rowLength = Math.max(educationInfoLength, jobHistoryLength, salaryHistoryLength);
        rowLengthForMerge.push(rowLength)

        worksheet.mergeCells(`A${rowNo}:A${rowNo + (rowLength - 1)}`);
        let slRow = worksheet.getRow(rowNo);
        slRow.getCell(1).value = index + 1;
        slRow.getCell(1).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        worksheet.mergeCells(`B${rowNo}:B${rowNo + (rowLength - 1)}`);
        let employeeIdRow = worksheet.getRow(rowNo);
        employeeIdRow.getCell(2).value = value?.work_information.employee_id;
        employeeIdRow.getCell(2).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        worksheet.mergeCells(`C${rowNo}:C${rowNo + (rowLength - 1)}`);
        let employeeNameRow = worksheet.getRow(rowNo);
        employeeNameRow.getCell(3).value = value?.work_information.name;
        employeeNameRow.getCell(3).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        worksheet.mergeCells(`D${rowNo}:D${rowNo + (rowLength - 1)}`);
        let employeeDesignationRow = worksheet.getRow(rowNo);
        employeeDesignationRow.getCell(4).value = value?.work_information.job_title;
        employeeDesignationRow.getCell(4).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        worksheet.mergeCells(`E${rowNo}:E${rowNo + (rowLength - 1)}`);
        let employeeDepartmentRow = worksheet.getRow(rowNo);
        employeeDepartmentRow.getCell(5).value = value?.work_information.department;
        employeeDepartmentRow.getCell(5).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        worksheet.mergeCells(`F${rowNo}:F${rowNo + (rowLength - 1)}`);
        let employeeDojRow = worksheet.getRow(rowNo);
        employeeDojRow.getCell(6).value = value?.work_information?.joining_date;
        employeeDojRow.getCell(6).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        worksheet.mergeCells(`G${rowNo}:G${rowNo + (rowLength - 1)}`);
        let employeeJoiningDesignationRow = worksheet.getRow(rowNo);
        employeeJoiningDesignationRow.getCell(7).value = value?.work_information?.joining_designation;
        employeeJoiningDesignationRow.getCell(7).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        worksheet.mergeCells(`H${rowNo}:H${rowNo + (rowLength - 1)}`);
        let separateDateRow = worksheet.getRow(rowNo);
        separateDateRow.getCell(8).value = value?.separation_details?.separation_date;
        separateDateRow.getCell(8).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        worksheet.mergeCells(`I${rowNo}:I${rowNo + (rowLength - 1)}`);
        let workLocationRow = worksheet.getRow(rowNo);
        workLocationRow.getCell(9).value = value?.work_information?.work_location;
        workLocationRow.getCell(9).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        worksheet.mergeCells(`J${rowNo}:J${rowNo + (rowLength - 1)}`);
        let employeeJoiningSalaryRow = worksheet.getRow(rowNo);
        employeeJoiningSalaryRow.getCell(10).value = value?.work_information?.joining_salary;
        employeeJoiningSalaryRow.getCell(10).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        worksheet.mergeCells(`K${rowNo}:K${rowNo + (rowLength - 1)}`);
        let employeeCurrentSalaryRow = worksheet.getRow(rowNo);
        employeeCurrentSalaryRow.getCell(11).value = value?.work_information?.running_salary;
        employeeCurrentSalaryRow.getCell(11).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        // worksheet.mergeCells(`K${rowNo}:K${rowNo + (rowLength - 1)}`);
        // let employeeIncrementSalaryRow = worksheet.getRow(rowNo);
        // employeeIncrementSalaryRow.getCell(11).value = value?.salary_history?.increment_salary;
        // employeeIncrementSalaryRow.getCell(11).alignment = {
        //     horizontal: "center",
        //     vertical: "middle",
        // };

        rowNo += rowLength;
    })

    let salaryIncrementNo = 9;
    data?.map((value: any) => {
        let colNo = 12;
        let educationInfoLength = value?.educational_info?.length;
        let jobHistoryLength = value?.job_history?.length;
        let salaryHistoryLength = value?.salary_history?.length;
        let rowLength = Math.max(educationInfoLength, jobHistoryLength, salaryHistoryLength);
        for (let i = 0; i < rowLength; i++) {
            if (value?.salary_history[i]) {
                worksheet.getCell(salaryIncrementNo, colNo).value = value?.salary_history[i]?.increment_salary;
                salaryIncrementNo += 1;
            }
            else {
                salaryIncrementNo += 1;
            }
        }
    });

    let incrementEffectiveNo = 9;
    data?.map((value: any) => {
        let colNo = 13;
        let educationInfoLength = value?.educational_info?.length;
        let jobHistoryLength = value?.job_history?.length;
        let salaryHistoryLength = value?.salary_history?.length;
        let rowLength = Math.max(educationInfoLength, jobHistoryLength, salaryHistoryLength);

        for (let i = 0; i < rowLength; i++) {
            if (value?.salary_history[i]) {
                worksheet.getCell(incrementEffectiveNo, colNo).value = value?.salary_history[i]?.updated_date;
                incrementEffectiveNo += 1;
            }
            else {
                incrementEffectiveNo += 1;
            }
        }
    });

    let educationNo = 9;
    data?.map((value: any, index: any) => {
        let colNo = 14;
        console.log(`${value?.work_information?.name} = ${value?.educational_info?.length}`)

        let educationInfoLength = value?.educational_info?.length;
        let jobHistoryLength = value?.job_history?.length;
        let salaryHistoryLength = value?.salary_history?.length;
        let rowLength = Math.max(educationInfoLength, jobHistoryLength, salaryHistoryLength);

        for (let i = 0; i < rowLength; i++) {
            console.log("Subject: ", value?.educational_info[i]?.subject)
            if (value?.educational_info[i]) {
                worksheet.getCell(educationNo, colNo).value = value?.educational_info[i]?.exam_title ? value?.educational_info[i]?.exam_title : "";
                worksheet.getCell(educationNo, colNo+1).value = value?.educational_info[i]?.subject ? value?.educational_info[i]?.subject : ""; // Major
                worksheet.getCell(educationNo, colNo+2).value = value?.educational_info[i]?.year ? value?.educational_info[i]?.year : "";
                worksheet.getCell(educationNo, colNo+3).value = value?.educational_info[i]?.result ? value?.educational_info[i]?.result : "";
                worksheet.getCell(educationNo, colNo+4).value = value?.educational_info[i]?.institute ? value?.educational_info[i]?.institute : "";
                educationNo += 1;
            }
            else {
                educationNo += 1;
            }
        }
    });

    let jobHistoryNo: any;
    let rs = 9;
    let rs_increment = 0;
    data?.map((value: any) => {
        let colNo = 19;
        jobHistoryNo = rs;
        value?.job_history?.map((item: any, index: any) => {
            worksheet.getCell(jobHistoryNo, colNo).value = item?.organization;
            worksheet.getCell(jobHistoryNo, colNo+1).value = item?.position;
            worksheet.getCell(jobHistoryNo, colNo+2).value = item?.start_date;
            worksheet.getCell(jobHistoryNo, colNo+3).value = item?.end_date;
            worksheet.getCell(jobHistoryNo, colNo+4).value = item?.job_duration;
            // worksheet.getCell(jobHistoryNo, colNo+3).value = item?.industry_type === "airlines" ? item?.job_duration : "";
            // worksheet.getCell(jobHistoryNo, colNo+3).value = item?.industry_type === "airlines" ? "Airlines" : "";
            // worksheet.getCell(jobHistoryNo, colNo+5).value = value?.work_information?.airlines_duration; // work_duration
            jobHistoryNo += 1;
        })
        rs += rowLengthForMerge[rs_increment];
        rs_increment += 1;
    });

    let workDurationNo = 9;

    data?.map((value: any) => {
        let educationInfoLength = value?.educational_info?.length || 0;
        let jobHistoryLength = value?.job_history?.length || 0;
        let salaryHistoryLength = value?.salary_history?.length || 0;
        let rowLength = Math.max(educationInfoLength, jobHistoryLength, salaryHistoryLength);

        worksheet.mergeCells(`X${workDurationNo}:X${workDurationNo + (rowLength - 1)}`);
        let airlinesRow = worksheet.getRow(workDurationNo);
        airlinesRow.getCell(24).value = value?.work_information?.airlines_duration;
        airlinesRow.getCell(24).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        // let usbaDurationRow = worksheet.getRow(workDurationNo);
        // usbaDurationRow.getCell(23).value = value?.work_information?.working_duration; // Column W
        // usbaDurationRow.getCell(23).alignment = { horizontal: "center", vertical: "middle" };

        worksheet.mergeCells(`Y${workDurationNo}:Y${workDurationNo + (rowLength - 1)}`);
        let usbaDurationRow = worksheet.getRow(workDurationNo);
        usbaDurationRow.getCell(25).value = value?.work_information?.working_duration;
        usbaDurationRow.getCell(25).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        workDurationNo += rowLength;
    });

    let professionalCertificationNo = 9;
    data?.map((value: any, index: any) => {
        if (value.professional_certificate){
            let educationInfoLength = value?.educational_info?.length;
            let jobHistoryLength = value?.job_history?.length;
            let salaryHistoryLength = value?.salary_history?.length;
            const certificationsLength = value?.professional_certificate?.length;
            let rowLength = Math.max(educationInfoLength, jobHistoryLength, salaryHistoryLength, certificationsLength);

            const certifications = value?.professional_certificate;
            certifications.forEach((cert:any, certIndex:any) => {
                const rowOffset = certIndex + professionalCertificationNo;
                worksheet.mergeCells(`Z${rowOffset}:Z${rowOffset}`);
                worksheet.getCell(`Z${rowOffset}`).value = cert?.certificate_name;
                worksheet.getCell(`Z${rowOffset}`).alignment = { horizontal: "center", vertical: "middle" };

                worksheet.mergeCells(`AA${rowOffset}:AA${rowOffset}`);
                worksheet.getCell(`AA${rowOffset}`).value = cert?.certification_institute_name;
                worksheet.getCell(`AA${rowOffset}`).alignment = { horizontal: "center", vertical: "middle" };

                worksheet.mergeCells(`AB${rowOffset}:AB${rowOffset}`);
                worksheet.getCell(`AB${rowOffset}`).value = cert?.certification_result;
                worksheet.getCell(`AB${rowOffset}`).alignment = { horizontal: "center", vertical: "middle" };

                worksheet.mergeCells(`AC${rowOffset}:AC${rowOffset}`);
                worksheet.getCell(`AC${rowOffset}`).value = cert?.certification_start_date;
                worksheet.getCell(`AC${rowOffset}`).alignment = { horizontal: "center", vertical: "middle" };

                worksheet.mergeCells(`AD${rowOffset}:AD${rowOffset}`);
                worksheet.getCell(`AD${rowOffset}`).value = cert?.certification_end_date;
                worksheet.getCell(`AD${rowOffset}`).alignment = { horizontal: "center", vertical: "middle" };

                worksheet.mergeCells(`AE${rowOffset}:AE${rowOffset}`);
                worksheet.getCell(`AE${rowOffset}`).value = cert?.certification_duration;
                worksheet.getCell(`AE${rowOffset}`).alignment = { horizontal: "center", vertical: "middle" };
            });

            professionalCertificationNo += rowLength;
        }
    });

    const separationTypeIndex = ['AF', 'AG'];
    const separationTypeString = ['Separation Type','Separation Reason']

    for (let i = 0; i < separationTypeIndex.length; i++) {
        // Merge the cells from row 7 to 8 in the respective column
        // const mergeCells = `${workDurationIndex[i]}7:${workDurationIndex[i]}8`;
        worksheet.mergeCells(`${separationTypeIndex[i]}7:${separationTypeIndex[i]}8`);

        // Get the cell in row 7, column i+1 (convert alphabet to index)
        let cell = worksheet.getCell(`${separationTypeIndex[i]}7`);

        // Set the cell value
        cell.value = separationTypeString[i];

        // Set the font size
        cell.font = {size: 11};

        // Set the alignment
        cell.alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        cell.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: {
                argb: "FFC6E0B4",
            },
        };

        worksheet.getColumn(separationTypeIndex[i]).width = separationTypeString[i].length + 2;
    }

    let separationTypeNo = 9;

    data?.map((value: any) => {
        let educationInfoLength = value?.educational_info?.length || 0;
        let jobHistoryLength = value?.job_history?.length || 0;
        let salaryHistoryLength = value?.salary_history?.length || 0;
        let rowLength = Math.max(educationInfoLength, jobHistoryLength, salaryHistoryLength);

        worksheet.mergeCells(`AF${separationTypeNo}:AF${separationTypeNo + (rowLength - 1)}`);
        let separationTypeRow = worksheet.getRow(separationTypeNo);
        separationTypeRow.getCell(32).value = value?.separation_details?.name;
        separationTypeRow.getCell(32).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        worksheet.mergeCells(`AG${separationTypeNo}:AG${separationTypeNo + (rowLength - 1)}`);
        let separationReasonRow = worksheet.getRow(separationTypeNo);
        separationReasonRow.getCell(33).value = value?.separation_details?.separation_reason;
        separationReasonRow.getCell(33).alignment = {
            horizontal: "center",
            vertical: "middle",
        };

        separationTypeNo += rowLength;
    });

    worksheet.columns.forEach((column, index) => {
        if (index >= 1 && index <= 40) {
            column.width = 25;
        }
    });

    try {
        // Generate buffer
        const buffer = await workbook.xlsx.writeBuffer();

        // Trigger file download
        const blob = new Blob([buffer], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `${reportName ? reportName : "Report"}.xlsx`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
    } catch (e) {
        console.log(e);
    }
}
