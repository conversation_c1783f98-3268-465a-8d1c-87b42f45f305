import { But<PERSON>, Form, InputN<PERSON>ber, Modal, Select } from "antd";
import React, { useEffect } from "react";
import { API_ROUTE } from "@/constants/api-routes";
import { getRequest, putRequest } from "@/services/apiRequestHandlers";
import showNotification from "../common/notification";

interface Props {
    ids: any;
    isModalOpen: boolean;
    handleCancel: () => void;
    fetchList: () => void;
    type: string | undefined;
}
const ChangeRadiusModal = ({
    ids,
    isModalOpen,
    handleCancel,
    fetchList,
    type,
}: Props) => {
    const [form] = Form.useForm();

    useEffect(() => {
        if (ids && type === "single") fetchRadius();
        else form.resetFields();
    }, [ids]);

    const fetchRadius = async () => {
        try {
            const resp: any = await getRequest(
                `${API_ROUTE.GET_SCHEDULE_RADIUS}/${ids}`
            );
            if (resp) {
                form.setFieldsValue(resp[0]);
            }
        } catch (err) {
            console.log(err);
        }
    };
    const handleOk = async (values: any) => {
        const payLoad = {
            schedules: type === "bulk" ? ids : undefined,
            radius: values.radius,
            measurement: values.measurement || "Meter",
        };
        try {
            const resp = await putRequest(
                type === "single"
                    ? `${API_ROUTE.UPDATE_SCHEDULE_RADIUS}/${ids}`
                    : `${API_ROUTE.BULK_UPDATE_SCHEDULE_RADIUS}`,
                payLoad
            );
            if (resp) {
                showNotification({
                    type: "success",
                    message: "Successfuly Updated the radius !",
                });
                if (type === "single") fetchRadius();
                fetchList();
                handleCancel();
            }
        } catch (err) {
            console.log(err);
            showNotification({
                type: "error",
                message: "Update Unsuccessful !",
            });
        }
    };
    return (
        <Modal
            centered
            title="Change Radius"
            open={isModalOpen}
            onCancel={handleCancel}
            onOk={handleOk}
            footer={[
                <Button key="cancel" type="dashed" onClick={handleCancel}>
                    Discard
                </Button>,
                <Button
                    key="ok"
                    type="primary"
                    onClick={() => {
                        form.validateFields().then((values) =>
                            handleOk(values)
                        );
                    }}
                >
                    Update
                </Button>,
            ]}
        >
            <Form form={form} layout="vertical" className="w-full">
                <Form.Item name="measurement" label="Measured By">
                    <Select
                        placeholder="Salary Distance Type"
                        defaultValue={"Meter"}
                        options={[
                            {
                                value: "Meter",
                                label: "Meter",
                            },
                            {
                                value: "Km",
                                label: "Kilometer",
                            },
                        ]}
                    />
                </Form.Item>
                <Form.Item
                    label="Distance"
                    name="radius"
                    rules={[
                        { required: true, message: "Please enter Radius !" },
                    ]}
                    className="w-full"
                >
                    <InputNumber
                        style={{ width: "100%" }}
                        className="w-full"
                        min={1}
                        placeholder="Type Here..."
                    />
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default ChangeRadiusModal;
