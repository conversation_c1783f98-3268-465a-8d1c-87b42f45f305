import React, { useEffect, useState, useRef } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Box,
  CircularProgress,
  Typography,
  useMediaQuery,
  Checkbox,
  TablePagination,
} from "@mui/material";
import { getRequest } from "@/services/apiRequestHandlers";
import { API_ROUTE } from "@/constants/api-routes";
import { AdjustmentHistory, LeaveHistory } from "@/interface/dashboard-models";
import {
  capitalizeFirstLetter,
  extractTimeHHMM,
  formatISODate,
} from "@/utils/helper";
import { useEmployeeContext } from "@/store/EmployeeContext";
import dayjs from "dayjs";

const AdjustmentAnalysisTable = () => {
  const [employeeData, setEmployeeData] = useState<AdjustmentHistory[]>([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState<number>(1);
  const [totalCount, setTotalCount] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [rowsPerPage, setRowsPerPage] = useState<number>(7);
  const { employeeId } = useEmployeeContext();
  const currentYear = dayjs().year();
  // Fetch employee data
  const getEmployeeAdjustmentDetails = async (
    id: string,
    year: number,
    page: number,
    pageSize: number
  ) => {
    if (id) {
      try {
        setLoading(true);
        const response = await getRequest<{data: AdjustmentHistory[], total: number}>(
          `${API_ROUTE.ATTENDANCE_ADJUSTMENT}?employee_id=${id}&year=${year}&page=${page}&page_size=${pageSize}`
        );

        if (response && response.data) {
          setEmployeeData(response.data);
          setTotalCount(response.total);
          setTotalPages(Math.ceil(response.total / rowsPerPage));
        }
      } catch (error) {
        console.error("Error fetching adjustment data:", error);
      } finally {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    if (employeeId)
      getEmployeeAdjustmentDetails(employeeId, currentYear, page, rowsPerPage);
  }, [page, rowsPerPage, employeeId, currentYear]);

  const tableHeaders = [
    [
      "Adjustment Type",
      "Date",
      "Original In Time",
      "Original Out Time",
      "Status",
    ],
  ];

  return (
    <Box
      sx={{
        width: "100%",
        overflowX: "auto",
        bgcolor: "#ffffff",
        color: "#063c7a",
        p: 2,
        boxShadow: 1,
        border: "none",
        outline: "none",
      }}
    >
      <Typography className="text-lg !font-bold text-center pb-5 flex justify-center gap-2">

        <span className="text-md text-orange-600">Yearly Adjustment Table</span>

      </Typography>
      {loading && (
        <Box display="flex" justifyContent="center" alignItems="center" py={3}>
          <CircularProgress />
        </Box>
      )}
      {!loading && (
        <TableContainer
          sx={{
            maxHeight: 400,
            overflowY: "auto",
            "&::-webkit-scrollbar": {
              width: "5px",
              height: "5px",
            },
            "&::-webkit-scrollbar-thumb": {
              backgroundColor: "#7ae075",
              borderRadius: "10px",
            },
            "&::-webkit-scrollbar-track": {
              backgroundColor: "#f3f4f6",
            },
          }}
        >
          <Table stickyHeader className="table-scroll-container">
            <TableHead>
              <TableRow sx={{ backgroundColor: "#f5f5f5" }}>
                {tableHeaders[0]?.map((header) => (
                  <TableCell
                    key={header}
                    sx={{
                      p: 1,
                      textAlign: "center",
                      fontWeight: "bold",
                      border: "none",
                      color: "#063c7a",
                      backgroundColor: "#f3f4f6",
                      whiteSpace: "nowrap",
                    }}
                  >
                    {header}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {employeeData.length > 0 ? (
                employeeData.map((info: AdjustmentHistory, rowIndex) => (
                  <TableRow
                    key={info?.id}
                    sx={{
                      "& > td": {
                        p: "11px",
                        borderBottom: "1px solid #e0e0e0",
                      },
                      transition: "background-color 0.5s ease",
                    }}
                  >
                    <TableCell
                      sx={{
                        p: 1,
                        textAlign: "start",
                        border: "none",
                        color: getStatusColor(info?.state),
                        whiteSpace: "nowrap",
                      }}
                    >
                      {info?.request_type}
                    </TableCell>
                    <TableCell
                      sx={{
                        p: 1,
                        textAlign: "center",
                        border: "none",
                        color: getStatusColor(info?.state),
                        whiteSpace: "nowrap",
                      }}
                    >
                      {formatISODate(info?.date) || ""}
                    </TableCell>
                    <TableCell
                      sx={{
                        p: 1,
                        textAlign: "center",
                        border: "none",
                        color: getStatusColor(info?.state),
                      }}
                    >
                      {extractTimeHHMM(info?.original_from_date) || ""}
                    </TableCell>
                    <TableCell
                      sx={{
                        p: 1,
                        textAlign: "center",
                        border: "none",
                        color: getStatusColor(info?.state),
                      }}
                    >
                      {extractTimeHHMM(info?.original_to_date) || ""}
                    </TableCell>
                    <TableCell
                      sx={{
                        p: 1,
                        textAlign: "center",
                        border: "none",
                        color:
                          info?.state === "approved"
                            ? "#3c8038"
                            : info?.state === "rejected"
                            ? "#f94144"
                            : "#063c7a",
                      }}
                      align="center"
                    >
                      <Box
                        display="flex"
                        justifyContent="center"
                        alignItems="center"
                        height="100%"
                      >
                        <span
                          className={`p-1 w-24 rounded-md`}

                          style={{
                            backgroundColor: getStatusColor(info?.state),
                            color: "#ffffff",
                            opacity: 0.65,
                          }}
                        >
                          {capitalizeFirstLetter(info?.state)}
                        </span>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={tableHeaders[0]?.length}
                    sx={{ textAlign: "center", py: 3 }}
                  >
                    No records found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      )}
      {/* Pagination */}
      <TablePagination
        rowsPerPageOptions={[5, 15, 30, 50]}
        component="div"
        count={totalCount}
        rowsPerPage={rowsPerPage}
        page={page - 1}
        onPageChange={(_event, newPage) => setPage(newPage + 1)}
        onRowsPerPageChange={(event) => {
          setRowsPerPage(parseInt(event.target.value, 10));
          setPage(1);
        }}
        nextIconButtonProps={{ disabled: page >= totalPages }}
      />
    </Box>
  );
};

export default AdjustmentAnalysisTable;

const getStatusColor = (status: string) => {
  switch (status) {
    case "approved":
      return "#3c8038";
    case "draft":
      return "#86cbd6";
    case "requested":
      return "#075985";
    case "reject":
      return "#f94144";
    default:
      return "";
  }
};
