import React, { useEffect, useState } from "react";
import { Typography } from "@mui/material";
import AnnouncementCard from "../workspace/announcementCard";
import BirthdayCard from "../workspace/birthDayCard";
import EventCard from "../workspace/eventCard";
import { AnnouncementsList, BirthdayList } from "@/types/notificationTypes";
import {
  getAnnouncementsList,
  getBirthdayList,
  getEmployeeDetails,
} from "@/services/WorkStationServices/NotificationServies";
import CalendarSection from "./calenderSection";
import LeaveSection from "./leaveSection";
import KpiSection from "./kpiSection";
import SearchComponent from "./searchComponent";
import { useEmployeeContext } from "@/store/EmployeeContext";
import LeaveAnalysisTable from "./leaveAnalysisTable";
import LeaveGraph from "./leaveGraph";
import AdjustmentAnalysisTable from "./adjustmentAnalysisTable";
import AdjustmentAnalysisGraph from "./adjustmentAnalysisGraph";

const DashboardComponent: React.FC = () => {
  const [showSearchOption, setShowSearchOption] = useState<boolean>(false);
  const [companyId,setCompanyId]= useState<any>();
  const { setEmployeeId } = useEmployeeContext();
  const {
    data: birthdayListResponse,
    error: birthdayListError,
    isLoading: birthdayListLoading,
  } = getBirthdayList();

  const {
    data: announcementsListResponse,
    error: announcementsListError,
    isLoading: announcementsListLoading,
  } = getAnnouncementsList();
  const birthdaysList: BirthdayList[] = birthdayListResponse?.data || [];
  const announcementsList: AnnouncementsList[] =
    announcementsListResponse?.data || [];

  useEffect(() => {
    const company = localStorage.getItem("companyId");
    const showSearch = localStorage.getItem("showSearch")
    setCompanyId(company)
    if (showSearch==="true") {
      setShowSearchOption(true);
    }
  }, []);

  useEffect(() => {
    const fetchEmployee = async () => { 
      const uId = localStorage.getItem("uId");
      if (!uId) return;
      const response = await getEmployeeDetails.employeeData(uId);

      if (response.status === "SUCCESS") {
        localStorage.setItem("companyId", response?.data?.employee_company_id);
        setCompanyId(response.data?.employee_company_id)

        const employeeId = localStorage.getItem("employeeId");
        if(employeeId != "0") setEmployeeId(employeeId);
        else {
          localStorage.setItem('employeeId', "0");
          localStorage.setItem('userEmployeeId', response?.data?.employee_id);
          setEmployeeId(response?.data?.employee_id);
        }
      }
    };

     fetchEmployee();
  }, []);

  return (
    <div className="w-full flex flex-col items-start text-[#063c7a] py-2 px-4 md_lg:pl-24 lg:pl-[95px] lg:pr-4 overflow-y-auto">
      {showSearchOption && <SearchComponent companyId={companyId} />}
      {/* Leave Summary */}
      <LeaveSection />
      <CalendarSection />

      {/* Leave Analysis Section */}
      <div className="w-full flex flex-col justify-center items-center mt-4">
        <div className="w-full  flex flex-col md:flex-row gap-4 mt-4">
          <LeaveAnalysisTable />
          <LeaveGraph />
          
        </div>
      </div>

      {/* Attendance/Adjustment Analysis Section */}
      <div className="w-full flex flex-col justify-center items-center mt-4 h-full">
        <div className="w-full  flex flex-col md:flex-row gap-4 mt-4 h-full">
          <AdjustmentAnalysisTable />
          <AdjustmentAnalysisGraph />
        </div>
      </div>

      <div className="w-full flex flex-col md:flex-row gap-5 justify-center items-center md:items-start mt-6">
        <div className="w-full pt-4 md:w-1/2 lg:w-1/2 xl:1/3  bg-white shadow-lg">
          <Typography className="text-sm text-center xl:text-lg !font-semibold  pl-5 pb-5 text-orange-600">
            Announcement Board
          </Typography>
          <AnnouncementCard announcements={announcementsList} />
        </div>
        <div className="w-full pt-4 md:w-1/2 lg:w-1/2 xl:w-1/3  bg-white shadow-lg">
          <Typography className="text-sm text-center xl:text-lg !font-semibold  pb-5 pl-5 text-green-600">
            Birthday Board
          </Typography>
          <BirthdayCard birthdays={birthdaysList} />
        </div>
        <div className="w-full pt-4 md:w-1/2 lg:w-1/2 xl:w-1/3  bg-white shadow-lg">
          <Typography className="text-sm text-center xl:text-lg !font-semibold  pb-5 pl-5 text-sky-800">
            Events List
          </Typography>
          <EventCard/>
        </div>
      </div>

      {
        companyId == 1 && <KpiSection />
      }
    </div>
  );
};

export default DashboardComponent;
