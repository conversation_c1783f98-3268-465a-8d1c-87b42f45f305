@tailwind base;
@tailwind components;
@tailwind utilities;

table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}
/* th,
td {
  border: .5px solid black;
  padding: 6px;
  text-align: left;
} */
th {
  background-color: #f2f2f2;
}

html, body {
  overflow-y: auto; /* Ensure scrolling is enabled */
}

/* Custom Slim Scrollbar */
::-webkit-scrollbar {
  width: 5px !important;
}

::-webkit-scrollbar-thumb {
  background-color: #7ae075 !important;
  border-radius: 5px !important;
}

::-webkit-scrollbar-track {
  background-color: #f3f4f6 !important; /* Tailwind gray-200 */
  border-radius: 5px !important;
}

.employee_details_modal {
 position: absolute  !important;
 top: -10px !important;
}
@media (min-width: 495px) {
  .employee_details_modal {
    top: 20px !important;
  }
}

@media (min-width: 1024px) {
  .employee_details_modal {
    top: -20px !important;
  }
}

@media (min-width: 1200px) {
  .employee_details_modal {
    top: -30px !important;
  }
}

.ant-menu-inline-collapsed {
  width: 79px !important;
}

/* Sidebar menu selected item color, don't change it's linked to theme */
.ant-menu-item-selected {
  color: #fff !important;
  background-color: #003c97 !important;
  border-radius: 0 !important;
}
.ant-menu-item:hover {
  color: #fff !important;
  border-radius: 0 !important;
  background-color: #003c97 !important;
}

.ant-menu-submenu-selected .ant-menu-submenu-title {
  border-radius: 0 !important;
}

.ant-menu-submenu-title:hover {
  border-radius: 0 !important;
  
}
.ant-form-item-label{
  font-weight: 500 !important;
}

/* And table responsive */

@media (max-width: 768px) {
  .ant-table {
      width: 100%;
      overflow-x: auto;
  }
  .ant-table-thead > tr > th,
  .ant-table-thead > tr > td,
  .ant-table-tbody > tr > th,
  .ant-table-tbody > tr > td {
      white-space: pre;
  }
  .ant-table-thead > tr > th > span,
  .ant-table-thead > tr > td > span,
  .ant-table-tbody > tr > th > span,
  .ant-table-tbody > tr > td > span {
      display: block;
  }
  .ant-table table {
      font-size: 12px;
  }
  .ant-tabs-tab-btn {
      font-size: 0.75rem;
  }
  .ant-tabs .ant-tabs-tab + .ant-tabs-tab {
      margin: 0 0 0 12px;
  }
  .ant-tabs .ant-tabs-tab {
      padding: 7px 0;
  }
  .ant-card .ant-card-body {
      padding: 12px;
  }
  .ant-menu-submenu-popup {
      visibility: hidden;
  }
}

.slate-background {
  background-color: #f3f5fb;
}

.white-background {
  background-color: #ffffff;
}

/* table cell hover color DON'T OVERRIDE IT */

.ant-table-tbody .ant-table-row > .ant-table-cell-row-hover {
  background: rgba(255, 255, 255, 0) !important;
}

.red-background {
  background-color: #d43a26 !important;
  border-color: #d43a26 !important;
}
.red-background:hover {
  background-color: #f75d5d !important;
  border-color: #f75d5d !important;
}
.red-background:disabled {
  background-color: #ffffff !important;
  color: #d1d5db !important;
  border-color: #d1d5db !important;
}
.gray-background {
  background-color: #687b98 !important;
  border-color: #687b98 !important;
}
.gray-background:hover {
  background-color: #7788a3 !important;
  border-color: #7788a3 !important;
}
.gray-background:disabled {
  background-color: #ffffff !important;
  color: #d1d5db !important;
  border-color: #d1d5db !important;
}

