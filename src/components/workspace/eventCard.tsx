"use client";
import React from "react";
import { Card, CardContent, Typography } from "@mui/material";
// import { format, parseISO, isToday, isFuture } from "date-fns";
import { getEventList } from "@/services/WorkStationServices/NotificationServies";
import { EventListType } from "@/types/notificationTypes";
import { format, parseISO, isToday, isFuture, parse } from "date-fns";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import EventIcon from "@mui/icons-material/Event";

const EventList = () => {
  const {
    data: eventListResponse,
    error: announcementsListError,
    isLoading: announcementsListLoading,
  } = getEventList();
  const eventList: EventListType[] = eventListResponse?.data || [];
  // Filter today's and upcoming events
  const todayEvents = eventList.filter((e) =>
    isToday(parse(e.date, "dd-MM-yyyy", new Date()))
  );

  const upcomingEvents = eventList.filter((e) =>
    isFuture(parse(e.date, "dd-MM-yyyy", new Date()))
  );
  return (
    <Card className="w-full shadow-lg  h-[500px] !rounded-none">
      <CardContent>
        <Typography variant="h6" className="font-semibold text-center">
          Ongoing & Upcoming Events
        </Typography>

        <div className="mt-3 p-1 h-[420px] overflow-y-auto">
          {/* Today's Events */}
          {todayEvents?.length > 0 && (
            <>
              <Typography
                variant="subtitle1"
                className="font-semibold text-indigo-600 mb-2"
              >
                🗓️ Today's Events
              </Typography>
              {todayEvents?.map((event, index) => (
                <div
                  key={event?.date}
                  className="flex flex-col bg-gradient-to-r from-indigo-100 to-indigo-200 p-4 rounded-md mb-4 shadow-md"
                >
                  <Typography className="text-gray-800 font-semibold text-lg text-center">
                    {event?.name}
                  </Typography>
                  <Typography className="text-gray-600 text-center mt-2">
                    {event?.description}
                  </Typography>

                  {/* Location */}
                  <div className="flex items-center mt-2 text-gray-600">
                    <LocationOnIcon className="mr-1" />
                    <Typography>Location: {event?.location}</Typography>
                  </div>

                  {/* Date */}
                  <div className="flex items-center mt-1 text-gray-600">
                    <EventIcon className="mr-1" />
                    <Typography>
                      Date:{" "}
                      {format(
                        parse(event.date, "dd-MM-yyyy", new Date()),
                        "dd MMM yyyy"
                      )}
                    </Typography>
                  </div>
                </div>
              ))}
            </>
          )}

          {/* Upcoming Events */}
          {upcomingEvents.length > 0 && (
            <>
              <Typography
                variant="subtitle1"
                className="font-semibold text-teal-600 mt-4 mb-2"
              >
                🔜 Upcoming Events
              </Typography>
              {upcomingEvents?.map((event) => (
                <div
                  key={event?.date}
                  className="flex flex-col bg-gradient-to-r from-teal-50 to-teal-100 p-4 rounded-md mb-4 shadow-sm"
                >
                  <Typography className="text-gray-800 font-semibold text-lg text-center">
                    {event?.name}
                  </Typography>
                  <Typography className="text-gray-600 text-start mt-2">
                    {event?.description}
                  </Typography>

                  {/* Location */}
                  <div className="flex items-center mt-2 text-gray-600">
                    <LocationOnIcon className="mr-1" />
                    <Typography>Location: {event?.location}</Typography>
                  </div>

                  {/* Date */}
                  <div className="flex items-center mt-1 text-gray-600">
                    <EventIcon className="mr-1" />
                    <Typography>
                      Date:{" "}
                      {format(
                        parse(event?.date, "dd-MM-yyyy", new Date()),
                        "dd MMM yyyy"
                      )}
                    </Typography>
                  </div>
                </div>
              ))}
            </>
          )}

          {/* No Events Available */}
          {todayEvents.length === 0 && upcomingEvents.length === 0 && (
            <Typography className="text-gray-500 text-center mt-4">
              No events available at the moment. Please check back later.
            </Typography>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default EventList;
