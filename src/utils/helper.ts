import { CompanyLogService } from "@/services/CompanyLogoService";
import dayjs from "dayjs";
import { toast } from "react-toastify";
import { SUCCESS } from "./common";
import advancedFormat from "dayjs/plugin/advancedFormat";

export const decryptUserId = (userId: string | undefined): number => {
  let alfa: string[] = ["a", "b"];
  let binary_value: string = "";
  let message: string = userId?.split("").reverse().join("") || "";

  for (let i = 0; i < message.length; i++) {
    if (message[i] === alfa[0]) {
      binary_value += "0";
    } else {
      binary_value += "1";
    }
  }

  let decimal: number = 0;

  // Iterate through each character of the binary string
  for (let i = binary_value.length - 1; i >= 0; i--) {
    // If the character is '1', add 2^position to the decimal value
    if (binary_value[i] === "1") {
      decimal += Math.pow(2, binary_value.length - 1 - i);
    }
  }

  return decimal;
};

export const dateTimeFormatForExcel = (date: any) =>
  !date ? null : dayjs(date).format("DD/MM/YYYY");

dayjs.extend(advancedFormat);
export const formatISODate = (date: any) =>
  !date ? null : dayjs(date).format("DD-MM-YYYY");

export function extractTimeHHMM(dateTimeString: string): string {
 if (dateTimeString) {
  const date = new Date(dateTimeString);
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  return `${hours}:${minutes}`;
 }
 return "";
}
async function getComanyLogoById(id: any) {
  const { data, status } = await CompanyLogService.getLogo(id);

  if (status === SUCCESS) {
    return data?.logo_url;
  } else {
    console.log("My Log data: ", data);
  }
}

// Exporting the async function
export { getComanyLogoById };

export const errorToast = (str: any) => {
  toast.error(str);
};

export const successToast = (str: any) => {
  toast.success(str);
};

export function capitalizeFirstLetter(str: string): string {
  if (!str) return str; // Handle empty string
  return str.charAt(0).toUpperCase() + str.slice(1);
}

export function capitalizeWords(input: string | undefined) {
  if (!input) return '';

  return input
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

export function convertIsoToCustomFormat(isoTime: string): string {
  if (!dayjs(isoTime).isValid()) {
      return "N/A";
  }

  return dayjs(isoTime).format("DD-MMM-YYYY HH:mm");
}
