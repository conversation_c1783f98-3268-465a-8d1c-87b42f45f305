import {
  Document,
  Font,
  Image,
  Page,
  StyleSheet,
  Text,
  View,
} from "@react-pdf/renderer";

const EmployeeCv = ({ data }: any) => {
  const workInfo = data[0]?.work_information;
  const privateInfo = data[0]?.private_information;
  const emergencyFirst = data[0]?.emergency_first_contact;
  const emergencySecond = data[0]?.emergency_second_contact;
  const refOne = data[0]?.reference_1;
  const refTwo = data[0]?.reference_2;
  const bankInfo = data[0]?.bank_information;
  const educationalInfo = data[0]?.educational_info;
  const jobHistory = data[0]?.job_history;
  const employeeNominee = data[0]?.employee_nominee;
  const employeeSkills = data[0]?.employee_skill;
  const certificates = data[0]?.professional_certificate;
  const trainingInfo = data[0]?.training_information;
  const spouseInfo = data[0]?.spouse_information;
  const employeeImg = "https://erp.us-bangla.com" + data[0]?.image_url;
  const styles = StyleSheet.create({
    page: {
      padding: 30,
    },
    headerContainer: { display: "flex", justifyContent: "space-between" },
    name: { display: "flex", flexDirection: "column" },
    table: {
      width: "auto",
      borderStyle: "solid",
      borderWidth: 1,
      borderRightWidth: 0,
      borderBottomWidth: 0,
      marginTop: "10px",
    },
    tableRow: {
      flexDirection: "row",
    },
    tableCol: {
      width: "100%",
      borderStyle: "solid",
      borderWidth: 1,
      borderLeftWidth: 0,
      borderTopWidth: 0,
      padding: 5,
    },
    tableCellHeader: {
      margin: "auto",
      fontSize: 12,
      fontWeight: "bold",
    },
    tableCell: {
      margin: "auto",
      fontSize: 10,
    },
  });

  Font.register({
    family: "Open Sans",
    src: `https://fonts.gstatic.com/s/opensans/v17/mem8YaGs126MiZpBA-UFVZ0e.ttf`,
  });

  Font.register({
    family: "Lato",
    src: `https://fonts.gstatic.com/s/lato/v16/S6uyw4BMUTPHjx4wWw.ttf`,
  });

  Font.register({
    family: "Lato Italic",
    src: `https://fonts.gstatic.com/s/lato/v16/S6u8w4BMUTPHjxsAXC-v.ttf`,
  });

  Font.register({
    family: "Lato Bold",
    src: `https://fonts.gstatic.com/s/lato/v16/S6u9w4BMUTPHh6UVSwiPHA.ttf`,
  });
  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* header */}
        <View
          style={{
            display: "flex",
            justifyContent: "space-between",
            flexDirection: "row",
          }}
        >
          <View>
            <Text
              style={{
                fontSize: "30px",
                fontFamily: "Lato Bold",
              }}
            >
              {workInfo?.name}
            </Text>
            <Text style={{ fontSize: "18px" }}>{workInfo?.job_title}</Text>
            <Text style={{ fontSize: "12px" }}>Phone: {workInfo?.phone} </Text>
            <Text style={{ fontSize: "12px" }}>Email: {workInfo?.email}</Text>
            <Text style={{ fontSize: "12px" }}>
              Employee Id: {workInfo?.employee_id}
            </Text>
          </View>
          <View
            style={{
              height: "90px",
              width: "90px",
            }}
          >
            <Image
              style={{
                borderRadius: "9999px",
              }}
              src={employeeImg}
            />
          </View>
        </View>
        {/* Work Info */}
        <View>
          <View
            style={{
              backgroundColor: "#4338ca",
              height: "20px",
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
              marginTop: "10px",
            }}
          >
            <Text
              style={{
                color: "#fff",
                fontSize: "12px",
              }}
            >
              WORK INFORMATION
            </Text>
          </View>
          <View
            style={{
              display: "flex",
              flexDirection: "row",
              marginTop: "5px",
            }}
          >
            <View style={{ width: "50%" }}>
              <Text style={{ fontSize: "12px" }}>
                Name: {workInfo?.name ? workInfo?.name : "N/A"}
              </Text>
              <Text style={{ fontSize: "12px" }}>
                Employee Id:{" "}
                {workInfo?.employee_id ? workInfo?.employee_id : "N/A"}
              </Text>
              <Text style={{ fontSize: "12px" }}>
                Company: {workInfo?.company ? workInfo?.company : "N/A"}
              </Text>
              <Text style={{ fontSize: "12px" }}>
                Department:{" "}
                {workInfo?.department ? workInfo?.department : "N/A"}
              </Text>
              <Text style={{ fontSize: "12px" }}>
                Division: {workInfo?.division ? workInfo?.division : "N/A"}
              </Text>
              <Text style={{ fontSize: "12px" }}>
                Phone: {workInfo?.phone ? workInfo?.phone : "N/A"}
              </Text>
            </View>
            <View style={{ width: "50%" }}>
              <Text style={{ fontSize: "12px" }}>
                Email: {workInfo?.email ? workInfo?.email : "N/A"}
              </Text>
              <Text style={{ fontSize: "12px" }}>
                Job Title: {workInfo?.job_title ? workInfo?.job_title : "N/A"}
              </Text>
              <Text style={{ fontSize: "12px" }}>
                Employee Type:{" "}
                {workInfo?.employee_type ? workInfo?.employee_type : "N/A"}
              </Text>
              <Text style={{ fontSize: "12px" }}>
                Joining Date:{" "}
                {workInfo?.joining_date ? workInfo?.joining_date : "N/A"}
              </Text>
              <Text style={{ fontSize: "12px" }}>
                Work Location:{" "}
                {workInfo?.work_location ? workInfo?.work_location : "N/A"}
              </Text>
            </View>
          </View>
        </View>
        {/* Private Info */}
        <View>
          <View
            style={{
              backgroundColor: "#4338ca",
              height: "20px",
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
              marginTop: "10px",
            }}
          >
            <Text
              style={{
                color: "#fff",
                fontSize: "12px",
              }}
            >
              PRIVATE INFORMATION
            </Text>
          </View>
          <View
            style={{
              display: "flex",
              flexDirection: "row",
              marginTop: "5px",
            }}
          >
            <View style={{ width: "50%" }}>
              <Text style={{ fontSize: "12px" }}>
                Nick Name:{" "}
                {privateInfo?.nick_name ? privateInfo?.nick_name : "N/A"}
              </Text>
              <Text style={{ fontSize: "12px" }}>
                Blood Group:{" "}
                {privateInfo?.blood_group ? privateInfo?.blood_group : "N/A"}
              </Text>
            </View>
            <View style={{ width: "50%" }}>
              <Text style={{ fontSize: "12px" }}>
                Father's Name:{" "}
                {privateInfo?.father_name ? privateInfo?.father_name : "N/A"}
              </Text>
              <Text style={{ fontSize: "12px" }}>
                Mother's Name:{" "}
                {privateInfo?.mother_name ? privateInfo?.mother_name : "N/A"}
              </Text>
            </View>
          </View>
        </View>

        <View
          style={{
            backgroundColor: "#000",
            height: "2px",
            marginTop: "5px",
            marginBottom: "5px",
          }}
        ></View>
        {/* address */}

        <View>
          <View
            style={{
              display: "flex",
              flexDirection: "row",
            }}
          >
            <View style={{ width: "50%" }}>
              <Text style={{ fontSize: "14px", fontFamily: "Lato Bold" }}>
                Present Address:
              </Text>
              <Text style={{ fontSize: "12px" }}>
                Country:{" "}
                {privateInfo?.present_country
                  ? privateInfo?.present_country
                  : "N/A"}
              </Text>
              <Text style={{ fontSize: "12px" }}>
                Division:{" "}
                {privateInfo?.present_division
                  ? privateInfo?.present_division
                  : "N/A"}
              </Text>
              <Text style={{ fontSize: "12px" }}>
                District:{" "}
                {privateInfo?.present_district
                  ? privateInfo?.present_district
                  : "N/A"}
              </Text>
              <Text style={{ fontSize: "12px" }}>
                Thana:{" "}
                {privateInfo?.present_upazila
                  ? privateInfo?.present_upazila
                  : "N/A"}
              </Text>
              <Text style={{ fontSize: "12px" }}>
                Address:{" "}
                {privateInfo?.present_village
                  ? privateInfo?.present_village
                  : "N/A"}
              </Text>
            </View>
            <View style={{ width: "50%" }}>
              <Text style={{ fontSize: "14px", fontFamily: "Lato Bold" }}>
                Permanent Address:
              </Text>
              <Text style={{ fontSize: "12px" }}>
                Country:{" "}
                {privateInfo?.permanent_country
                  ? privateInfo?.permanent_country
                  : "N/A"}
              </Text>
              <Text style={{ fontSize: "12px" }}>
                Division:{" "}
                {privateInfo?.permanent_division
                  ? privateInfo?.permanent_division
                  : "N/A"}
              </Text>
              <Text style={{ fontSize: "12px" }}>
                District:{" "}
                {privateInfo?.permanent_district
                  ? privateInfo?.permanent_district
                  : "N/A"}
              </Text>
              <Text style={{ fontSize: "12px" }}>
                Thana:{" "}
                {privateInfo?.permanent_upazila
                  ? privateInfo?.permanent_upazila
                  : "N/A"}
              </Text>
              <Text style={{ fontSize: "12px" }}>
                Address:{" "}
                {privateInfo?.permanent_village
                  ? privateInfo?.permanent_village
                  : "N/A"}
              </Text>
            </View>
          </View>
        </View>
        <View
          style={{
            backgroundColor: "#000",
            height: "2px",
            marginTop: "5px",
            marginBottom: "5px",
          }}
        ></View>
        {/* emergency contact */}
        <View
          style={{
            display: "flex",
            flexDirection: "row",
          }}
        >
          <View style={{ width: "50%" }}>
            <Text style={{ fontSize: "14px", fontFamily: "Lato Bold" }}>
              Emergency First Contact:
            </Text>
            <Text style={{ fontSize: "12px" }}>
              Name: {emergencyFirst?.name ? emergencyFirst?.name : "N/A"}
            </Text>
            <Text style={{ fontSize: "12px" }}>
              Emergency Phone:{" "}
              {emergencyFirst?.phone ? emergencyFirst?.phone : "N/A"}
            </Text>
            <Text style={{ fontSize: "12px" }}>
              Relationship:{" "}
              {emergencyFirst?.relation ? emergencyFirst?.relation : "N/A"}
            </Text>
            <Text style={{ fontSize: "12px" }}>
              Address:{" "}
              {emergencyFirst?.address ? emergencyFirst?.address : "N/A"}
            </Text>
          </View>
          <View style={{ width: "50%" }}>
            <Text style={{ fontSize: "14px", fontFamily: "Lato Bold" }}>
              Emergency Second Contact:
            </Text>
            <Text style={{ fontSize: "12px" }}>
              Name: {emergencySecond?.name ? emergencySecond?.name : "N/A"}
            </Text>
            <Text style={{ fontSize: "12px" }}>
              Emergency Phone:{" "}
              {emergencySecond?.phone ? emergencySecond?.phone : "N/A"}
            </Text>
            <Text style={{ fontSize: "12px" }}>
              Relationship:{" "}
              {emergencySecond?.relation ? emergencySecond?.relation : "N/A"}
            </Text>
            <Text style={{ fontSize: "12px" }}>
              Address:{" "}
              {emergencySecond?.address ? emergencySecond?.address : "N/A"}
            </Text>
          </View>
        </View>

        <View
          style={{
            backgroundColor: "#000",
            height: "2px",
            marginTop: "5px",
            marginBottom: "5px",
          }}
        ></View>
        {/* Reference */}
        <View
          style={{
            display: "flex",
            flexDirection: "row",
          }}
        >
          <View style={{ width: "50%" }}>
            <Text style={{ fontSize: "14px", fontFamily: "Lato Bold" }}>
              Reference 1:
            </Text>
            <Text style={{ fontSize: "12px" }}>
              Name: {refOne?.name ? refOne?.name : "N/A"}
            </Text>
            <Text style={{ fontSize: "12px" }}>
              Position: {refOne?.position ? refOne?.position : "N/A"}
            </Text>
            <Text style={{ fontSize: "12px" }}>
              Organization:{" "}
              {refOne?.organization ? refOne?.organization : "N/A"}
            </Text>
            <Text style={{ fontSize: "12px" }}>
              Address: {refOne?.address ? refOne?.address : "N/A"}
            </Text>
            <Text style={{ fontSize: "12px" }}>
              Contact: {refOne?.ref_contact ? refOne?.ref_contact : "N/A"}
            </Text>
            <Text style={{ fontSize: "12px" }}>
              Email: {refOne?.ref_email ? refOne?.ref_email : "N/A"}
            </Text>
            <Text style={{ fontSize: "12px" }}>
              Relationship:{" "}
              {refOne?.ref_relation ? refOne?.ref_relation : "N/A"}
            </Text>
          </View>
          <View style={{ width: "50%" }}>
            <Text style={{ fontSize: "14px", fontFamily: "Lato Bold" }}>
              Reference 2:
            </Text>
            <Text style={{ fontSize: "12px" }}>
              Name: {refTwo?.name ? refTwo?.name : "N/A"}
            </Text>
            <Text style={{ fontSize: "12px" }}>
              Position: {refTwo?.position ? refTwo?.position : "N/A"}
            </Text>
            <Text style={{ fontSize: "12px" }}>
              Organization:{" "}
              {refTwo?.organization ? refTwo?.organization : "N/A"}
            </Text>
            <Text style={{ fontSize: "12px" }}>
              Address: {refTwo?.address ? refTwo?.address : "N/A"}
            </Text>
            <Text style={{ fontSize: "12px" }}>
              Contact: {refTwo?.ref_contact ? refTwo?.ref_contact : "N/A"}
            </Text>
            <Text style={{ fontSize: "12px" }}>
              Email: {refTwo?.ref_email ? refTwo?.ref_email : "N/A"}
            </Text>
            <Text style={{ fontSize: "12px" }}>
              Relationship:{" "}
              {refTwo?.ref_relation ? refTwo?.ref_relation : "N/A"}
            </Text>
          </View>
        </View>
        <View
          style={{
            backgroundColor: "#000",
            height: "2px",
            marginTop: "5px",
            marginBottom: "5px",
          }}
        ></View>
        {/* Bank Info */}
        <View
          style={{
            display: "flex",
            flexDirection: "row",
          }}
        >
          <View style={{ width: "50%" }}>
            <Text style={{ fontSize: "14px", fontFamily: "Lato Bold" }}>
              Bank Information:
            </Text>
            <Text style={{ fontSize: "12px" }}>
              Bank Name: {bankInfo?.bank_name ? bankInfo?.bank_name : "N/A"}
            </Text>
            <Text style={{ fontSize: "12px" }}>
              Branch Name:{" "}
              {bankInfo?.branch_name ? bankInfo?.branch_name : "N/A"}
            </Text>
            <Text style={{ fontSize: "12px" }}>
              Account Name:{" "}
              {bankInfo?.account_name ? bankInfo?.account_name : "N/A"}
            </Text>
            <Text style={{ fontSize: "12px" }}>
              Account No: {bankInfo?.account_no ? bankInfo?.account_no : "N/A"}
            </Text>
            <Text style={{ fontSize: "12px" }}>
              Route Name: {bankInfo?.route_name ? bankInfo?.route_name : "N/A"}
            </Text>
          </View>
        </View>

        {/* Nominee */}
        <View style={{ marginBottom: "20px" }}>
          <View
            style={{
              backgroundColor: "#4338ca",
              height: "20px",
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
              marginTop: "5px",
            }}
          >
            <Text
              style={{
                color: "#fff",
                fontSize: "12px",
              }}
            >
              SPOUSE INFORMATION
            </Text>
          </View>
          <View style={styles.table}>
            {/* Table Header */}
            <View style={styles.tableRow}>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>SPOUSE NAME</Text>
              </View>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>PROFESSION</Text>
              </View>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>CONTACT</Text>
              </View>
            </View>
            {spouseInfo?.map((info: any, index: any) => (
              <View key={index} style={styles.tableRow}>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.spouse_name ? info?.spouse_name : "N/A"}
                  </Text>
                </View>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.spouse_profession ? info?.spouse_profession : "N/A"}
                  </Text>
                </View>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.spouse_contact ? info?.spouse_contact : "N/A"}
                  </Text>
                </View>
              </View>
            ))}
          </View>
        </View>
        {/* academic info */}
        <View>
          <View
            style={{
              backgroundColor: "#4338ca",
              height: "20px",
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
              marginTop: "5px",
            }}
          >
            <Text
              style={{
                color: "#fff",
                fontSize: "12px",
              }}
            >
              ACADEMIC QUALIFICATION
            </Text>
          </View>
          <View style={styles.table}>
            {/* Table Header */}
            <View style={styles.tableRow}>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>EXAM TITLE</Text>
              </View>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>INSTITUTE</Text>
              </View>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>MAJOR</Text>
              </View>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>RESULT</Text>
              </View>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>YEAR</Text>
              </View>
            </View>
            {educationalInfo?.map((info: any, index: any) => (
              <View key={index} style={styles.tableRow}>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.exam_title ? info?.exam_title : "N/A"}
                  </Text>
                </View>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.institute ? info?.institute : "N/A"}
                  </Text>
                </View>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.subject ? info?.subject : "N/A"}
                  </Text>
                </View>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.result ? info?.result : "N/A"}
                  </Text>
                </View>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.year ? info?.year : "N/A"}
                  </Text>
                </View>
              </View>
            ))}
          </View>
        </View>

        {/* Certificates */}

        <View>
          <View
            style={{
              backgroundColor: "#4338ca",
              height: "20px",
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
              marginTop: "5px",
            }}
          >
            <Text
              style={{
                color: "#fff",
                fontSize: "12px",
              }}
            >
              PROFESSIONAL CERTIFICATION
            </Text>
          </View>
          <View style={styles.table}>
            {/* Table Header */}
            <View style={styles.tableRow}>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>CERTIFICATION NAME</Text>
              </View>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>INSTITUTE</Text>
              </View>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>RESULT</Text>
              </View>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>REMARKS</Text>
              </View>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>START DATE</Text>
              </View>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>END DATE</Text>
              </View>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>DURATION</Text>
              </View>
            </View>
            {certificates?.map((info: any, index: any) => (
              <View key={index} style={styles.tableRow}>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.certificate_name ? info?.certificate_name : "N/A"}
                  </Text>
                </View>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.certification_institute_name
                      ? info?.certification_institute_name
                      : "N/A"}
                  </Text>
                </View>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.certification_result
                      ? info?.certification_result
                      : "N/A"}
                  </Text>
                </View>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.certification_remarks
                      ? info?.certification_remarks
                      : "N/A"}
                  </Text>
                </View>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.certification_start_date
                      ? info?.certification_start_date
                      : "N/A"}
                  </Text>
                </View>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.certification_end_date
                      ? info?.certification_end_date
                      : "N/A"}
                  </Text>
                </View>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.certification_duration
                      ? info?.certification_duration
                      : "N/A"}
                  </Text>
                </View>
              </View>
            ))}
          </View>
        </View>

        {/* Skills */}
        <View>
          <View
            style={{
              backgroundColor: "#4338ca",
              height: "20px",
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
              marginTop: "5px",
            }}
          >
            <Text
              style={{
                color: "#fff",
                fontSize: "12px",
              }}
            >
              SKILLS
            </Text>
          </View>
          <View style={styles.table}>
            {/* Table Header */}
            <View style={styles.tableRow}>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>SKILLS NAME</Text>
              </View>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>YEARS OF EXPERIENCE</Text>
              </View>
            </View>
            {employeeSkills?.map((info: any, index: any) => (
              <View key={index} style={styles.tableRow}>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.skill_name ? info?.skill_name : "N/A"}
                  </Text>
                </View>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.duration ? info?.duration : "N/A"}
                  </Text>
                </View>
              </View>
            ))}
          </View>
        </View>
        {/* Training info */}

        <View>
          <View
            style={{
              backgroundColor: "#4338ca",
              height: "20px",
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
              marginTop: "5px",
            }}
          >
            <Text
              style={{
                color: "#fff",
                fontSize: "12px",
              }}
            >
              TRAINING INFORMATION
            </Text>
          </View>
          <View style={styles.table}>
            {/* Table Header */}
            <View style={styles.tableRow}>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>TITLE</Text>
              </View>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>INSTITUTE</Text>
              </View>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>COUNTRY</Text>
              </View>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>MODE</Text>
              </View>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>START DATE</Text>
              </View>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>END DATE</Text>
              </View>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>DURATION</Text>
              </View>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>YEAR</Text>
              </View>
            </View>
            {trainingInfo?.map((info: any, index: any) => (
              <View key={index} style={styles.tableRow}>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.training_title ? info?.training_title : "N/A"}
                  </Text>
                </View>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.training_institute
                      ? info?.training_institute
                      : "N/A"}
                  </Text>
                </View>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.training_country ? info?.training_country : "N/A"}
                  </Text>
                </View>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.training_mode ? info?.training_mode : "N/A"}
                  </Text>
                </View>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.training_start_date
                      ? info?.training_start_date
                      : "N/A"}
                  </Text>
                </View>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.training_end_date ? info?.training_end_date : "N/A"}
                  </Text>
                </View>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.training_duration ? info?.training_duration : "N/A"}
                  </Text>
                </View>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.trining_year ? info?.trining_year : "N/A"}
                  </Text>
                </View>
              </View>
            ))}
          </View>
        </View>

        {/* Nominee */}
        <View>
          <View
            style={{
              backgroundColor: "#4338ca",
              height: "20px",
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
              marginTop: "5px",
            }}
          >
            <Text
              style={{
                color: "#fff",
                fontSize: "12px",
              }}
            >
              NOMINEE INFORMATION
            </Text>
          </View>
          <View style={styles.table}>
            {/* Table Header */}
            <View style={styles.tableRow}>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>NOMINEE NAME</Text>
              </View>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>RELATION</Text>
              </View>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>PERCENTAGE</Text>
              </View>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>PASS/NID</Text>
              </View>
            </View>
            {employeeNominee?.map((info: any, index: any) => (
              <View key={index} style={styles.tableRow}>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.person_name ? info?.person_name : "N/A"}
                  </Text>
                </View>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.relation ? info?.relation : "N/A"}
                  </Text>
                </View>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.percentage ? info?.percentage : "N/A"}
                  </Text>
                </View>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.nominee_nid ? info?.nominee_nid : "N/A"}
                  </Text>
                </View>
              </View>
            ))}
          </View>
        </View>
        {/* Job History */}

        <View>
          <View
            style={{
              backgroundColor: "#4338ca",
              height: "20px",
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
              marginTop: "5px",
            }}
          >
            <Text
              style={{
                color: "#fff",
                fontSize: "12px",
              }}
            >
              EMPLOYMENT HISTORY
            </Text>
          </View>
          <View style={styles.table}>
            {/* Table Header */}
            <View style={styles.tableRow}>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>ORGANIZATION</Text>
              </View>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>COUNTRY</Text>
              </View>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>POSITION</Text>
              </View>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>START DATE </Text>
              </View>
              <View style={styles.tableCol}>
                <Text style={styles.tableCellHeader}>END DATE</Text>
              </View>
            </View>
            {jobHistory?.map((info: any, index: any) => (
              <View key={index} style={styles.tableRow}>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.organization ? info?.organization : "N/A"}
                  </Text>
                </View>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.country_name ? info?.country_name : "N/A"}
                  </Text>
                </View>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.position ? info?.position : "N/A"}
                  </Text>
                </View>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.start_date ? info?.start_date : "N/A"}
                  </Text>
                </View>
                <View style={styles.tableCol}>
                  <Text style={{ fontSize: "12px" }}>
                    {info?.end_date ? info?.end_date : "N/A"}
                  </Text>
                </View>
              </View>
            ))}
          </View>
        </View>
      </Page>
    </Document>
  );
};

export default EmployeeCv;
