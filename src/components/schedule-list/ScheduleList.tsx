"use client";
import ChangeRadiusModal from "@/components/modals/changeRadiusModal";
import UserDetailsModal from "@/components/modals/userDetailsModal";
import { API_ROUTE } from "@/constants/api-routes";
import { PlusOutlined, SettingOutlined, StopOutlined } from "@ant-design/icons";
import {
  Button,
  Checkbox,
  DatePicker,
  PaginationProps,
  Popover,
  Space,
  Table,
  Tag,
} from "antd";
import Search from "antd/es/input/Search";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import DeclineModal from "../modals/decineModal";
import showNotification from "../common/notification";
import { ColumnsType } from "antd/es/table";
import { Schedule } from "@/interface/schedule";
import { getRequestWithParams } from "@/services/apiRequestHandlers";
import { capitalizeFirstLetter } from "@/utils/helper";
import SettingsModal from "../modals/settingsModal";
import { Tab, Tabs } from "@mui/material";
import dayjs from "dayjs";
import { SearchOffRounded, SearchRounded } from "@mui/icons-material";

const ScheduleList = () => {
  const { RangePicker } = DatePicker;
  const [scheduleList, setScheduleList] = useState<Schedule[]>([]);
  const [selectAllChecked, setSelectAllChecked] = useState<boolean>(false);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [searchKey, setSearchKey] = useState<string>("");
  const [dateRange, setDateRange] = useState<any>(null);
  const [openSettingsModal, setOpenSettingsModal] = useState<boolean>(false);
  const router = useRouter();
  const [openRadiusModal, setOpenRadiusModal] = useState<{
    visible: boolean;
    ids?: any;
    type?: "single" | "bulk";
  }>({ visible: false });
  const [openDeclineModal, setOpenDeclineModal] = useState<{
    visible: boolean;
    record?: any;
    type?: "single" | "bulk";
  }>({ visible: false });
  const [openUserModal, setOpenUserModal] = useState<{
    visible: boolean;
    details?: Schedule;
  }>({ visible: false });
  const [activeTab, setActiveTab] = useState("today_upcoming");
  useEffect(() => {
    fetchList(currentPage, pageSize, searchKey);
  }, [currentPage, pageSize ,dateRange, activeTab]);

  useEffect(()=>{
     setDateRange(null);
  },[activeTab])

  const fetchList = async (
    pageNumber?: number,
    pageSize?: number,
    searchKey?: string,
    state?: string
  ) => {
    try {
      const resp: any = await getRequestWithParams(
        `${API_ROUTE.SCHEDULE_LIST}`,
        {
          page: pageNumber,
          page_size: pageSize,
          search: searchKey,
          state: state || activeTab,
          request_from_web: true,
          user_id: localStorage.getItem("uId"),
          from_date: dateRange && dateRange[0]?.format("YYYY-MM-DD") ,
          to_date: dateRange && dateRange[1]?.format("YYYY-MM-DD")
        }
      );
      if (resp) {
        setScheduleList(resp?.items || []);
        setTotalItems(resp?.total || 0);
        setSelectedIds([]);
        setSelectAllChecked(false);
      }
    } catch (err) {
      console.log(err);
    } finally {
      setLoading(false);
    }
  };

  const handleSelectAllChange = (e: any) => {
    const isChecked = e.target.checked;
    setSelectAllChecked(isChecked);

    if (isChecked) {
      const allIds = scheduleList
        .filter(
          (item) => item.schedule_id !== null && item.schedule_id !== undefined
        )
        .map((item) => String(item.schedule_id)); // Map only valid IDs as strings
      setSelectedIds(allIds); // Select all IDs
    } else {
      setSelectedIds([]); // Deselect all
    }
  };

  const handleCheckboxChange = useCallback(
    (id: string, checked: boolean, event: any) => {
      event.stopPropagation();
      setSelectedIds((prevSelectedIds) => {
        let newSelectedIds = [...prevSelectedIds];
        if (checked) {
          if (!newSelectedIds.includes(id)) {
            newSelectedIds.push(id);
          }
        } else {
          newSelectedIds = newSelectedIds.filter(
            (selectedId) => selectedId !== id
          );
        }
        return newSelectedIds;
      });
    },
    []
  );

  const validateSelectedItems = () => {
    const declinedItem = scheduleList.find(
      (item) =>
        selectedIds.includes(String(item.schedule_id)) &&
        item.status === "Declined"
    );
    const checkedItem = scheduleList.find(
      (item) =>
        selectedIds.includes(String(item.schedule_id)) &&
        (item.check_in_time || item.check_out_time)
    );
    if (declinedItem) {
      showNotification({
        type: "error",
        message: "One or more selected items are already declined !",
      });
      return false;
    }
    if (checkedItem) {
      showNotification({
        type: "error",
        message:
          "One or more selected items are already checked in or checked out !",
      });
      return false;
    }
    return true;
  };

  const columns: ColumnsType<Schedule> = [
    {
      title: (
        <Space>
          <Checkbox
            checked={selectAllChecked}
            onChange={handleSelectAllChange}
          />
          Employee ID
        </Space>
      ),
      dataIndex: "employee_unique_id",
      render: (text: any, record: any) => (
        <Space key={record.schedule_id}>
          <Checkbox
            checked={selectedIds.includes(String(record.schedule_id))}
            onChange={(e) =>
              handleCheckboxChange(
                String(record.schedule_id),
                e.target.checked,
                e
              )
            }
            onClick={(e) => e.stopPropagation()}
          />
          {text}
        </Space>
      ),
    },
    {
      title: "Name",
      dataIndex: "employee_name",
      key: "employee_name",
    },
    {
      title: "Scheduling Type",
      dataIndex: "scheduling_type",
      key: "scheduling_type",
    },
    {
      title: "Date",
      dataIndex: "date",
      key: "date",
    },
    {
      title: "Check In",
      key: "check_in_time",
      dataIndex: "check_in_time",
      render: (_: any, record: any) =>
        record?.check_in_time ? record?.check_in_time : "-- --",
    },
    {
      title: "Check Out",
      key: "check_out_time",
      dataIndex: "check_out_time",
      render: (_: any, record: any) =>
        record?.check_out_time ? record?.check_out_time : "-- --",
    },
    {
      title: "Status",
      key: "status",
      dataIndex: "status",
      render: (text: string) => (
        <Tag color={text === "Approved" ? "success" : "error"}>
          {capitalizeFirstLetter(text)}
        </Tag>
      ),
    },
    {
      title: "Actions",
      key: "actions",
      render: (_: any, record: any) => (
        <Popover
          content={
            <div className="w-full">
              <div
                className="cursor-pointer p-2 text-left hover:bg-primary hover:text-white"
                onClick={(e: any) => {
                  setOpenUserModal({
                    visible: true,
                    details: record,
                  });
                  e.stopPropagation();
                }}
              >
                Detail
              </div>
              {record.status === "Declined" ||
              record.check_in_time ||
              record.check_out_time || activeTab === "previous" ? (
                <></>
              ) : (
                <>
                  <div
                    className="cursor-pointer p-2 text-left hover:bg-primary hover:text-white"
                    onClick={(e: any) => {
                      setOpenDeclineModal({
                        visible: true,
                        record: record?.schedule_id,
                        type: "single",
                      });

                      e.stopPropagation();
                    }}
                  >
                    Decline
                  </div>
                  <div
                    className="cursor-pointer p-2 text-left hover:bg-primary hover:text-white"
                    onClick={(e: any) => {
                      setOpenRadiusModal({
                        visible: true,
                        ids: record?.schedule_id,
                        type: "single",
                      });
                      e.stopPropagation();
                    }}
                  >
                    Change Radius
                  </div>
                </>
              )}
            </div>
          }
          trigger="click"
          placement="bottomRight"
          overlayStyle={{ maxWidth: "150px" }} // Ensuring the pop-up is constrained to a reasonable width
        >
          <Button
            onClick={(e: any) => {
              e.stopPropagation();
            }}
            style={{ width: "40px", textAlign: "center" }}
          >
            ...
          </Button>
        </Popover>
      ),
    },
  ];

  const handleTableChange: PaginationProps["onChange"] = (page, pageSize) => {
    setCurrentPage(page);
    setPageSize(pageSize);
  };

  const handleTabChange = (_event: React.SyntheticEvent, newValue: string) => {
    setActiveTab(newValue);
    // Optionally refetch list based on tab
    fetchList(1, 10, searchKey, newValue); // assuming backend handles tab-based filtering
    setCurrentPage(1);
  };

  return (
    <div className="md:pl-[80px] md:pr-2 pt-4">
      <div>
        <div className="flex flex-col md:flex-row items-center gap-2 justify-between mb-2">
          <div className="space-y-2">
            <h1 className="text-lg">Out Station Attendance List</h1>
            <div className="space-x-2">
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => router.push("/location-info")}
                className="w-28 bg-primary"
              >
                Create
              </Button>
              <Button
                type="primary"
                icon={<SettingOutlined />}
                onClick={() => setOpenSettingsModal(true)}
                className="w-28 bg-primary"
              >
                Settings
              </Button>
            </div>
          </div>
          <div className="flex flex-col gap-2">
            <Search
              placeholder="Search"
              allowClear
              onChange={(e: any) => {
                const value = e.target.value;
                setSearchKey(value);

                if (value === "") {
                  fetchList(1, 10, "");
                  setCurrentPage(1);
                }
              }}
              onSearch={(value: string) => {
                fetchList(1, 10, value);
                setCurrentPage(1);
              }}
              enterButton={<Button style={{ backgroundColor: "#023c97", borderColor: "#023c97", color: "white" }}>
                <SearchRounded/>
              </Button>}
            />
            <RangePicker
              value={dateRange}
              onChange={(val: any) => {
                setDateRange(val);
              }}
              disabledDate={(current:any) => {
                if (activeTab === "today_upcoming") {
                  // For Today/Upcoming tab: Disable dates before current day
                  return current && current.isBefore(dayjs().startOf("day"));
                } else if (activeTab === "previous") {
                  // For Previous tab: Disable dates after and including current day
                  return current && current.isAfter(dayjs().startOf("day").subtract(1, "day"));
                }
                return false;
              }}
            />
            <div className="md:hidden flex items-center justify-end gap-2 w-full">
              <Button
                onClick={() => {
                  if (validateSelectedItems()) {
                    setOpenRadiusModal({
                      visible: true,
                      ids: selectedIds,
                      type: "bulk",
                    });
                  }
                }}
                disabled={selectedIds.length === 0 || activeTab === "previous"}
                className="gray-background !text-white w-1/2"
              >
                Change Radius
              </Button>
              <Button
                icon={<StopOutlined />}
                onClick={() => {
                  if (validateSelectedItems()) {
                    setOpenDeclineModal({
                      visible: true,
                      record: selectedIds,
                      type: "bulk",
                    });
                  }
                }}
                className="red-background  !text-white w-1/2"
                disabled={selectedIds.length === 0 || activeTab === "previous"}
              >
                Decline
              </Button>
            </div>
          </div>
        </div>
        <div className="flex justify-between mt-2">
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            textColor="primary"
            indicatorColor="primary"
            sx={{
              borderBottom: "2px solid #ddd",
              "& .MuiTabs-flexContainer": { justifyContent: "flex-start" },
              "& .MuiTab-root": {
                textTransform: "none",
                fontWeight: 600,
                fontSize: "1rem",
                color: "#333",
                transition: "0.3s",
                whiteSpace: "nowrap",
                "&:hover": {
                  background: "linear-gradient(45deg, #023c97, #2563eb)",
                  color: "white",
                },
                "&.Mui-selected": { color: "#023c97" },
              },
              "& .MuiTabs-indicator": { backgroundColor: "#023c97" },
            }}
          >
            <Tab label="Today/Upcoming" value="today_upcoming" />
            <Tab label="Previous" value="previous" />
          </Tabs>
          <div className=" hidden md:flex items-center justify-end gap-2 w-1/2">
              <Button
                onClick={() => {
                  if (validateSelectedItems()) {
                    setOpenRadiusModal({
                      visible: true,
                      ids: selectedIds,
                      type: "bulk",
                    });
                  }
                }}
                disabled={selectedIds.length === 0 || activeTab === "previous"}
                className="gray-background !text-white w-[149px]"
              >
                Change Radius
              </Button>
              <Button
                icon={<StopOutlined />}
                onClick={() => {
                  if (validateSelectedItems()) {
                    setOpenDeclineModal({
                      visible: true,
                      record: selectedIds,
                      type: "bulk",
                    });
                  }
                }}
                className="red-background  !text-white w-[149px]"
                disabled={selectedIds.length === 0 || activeTab === "previous"}
              >
                Decline
              </Button>
            </div>
        </div>
        <Table
          loading={loading}
          bordered
          dataSource={scheduleList.map((item) => ({
            ...item,
            key: item.schedule_id,
          }))}
          columns={columns}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: totalItems,
            onChange: handleTableChange,
            showSizeChanger: true,
            pageSizeOptions: ["10", "20", "50", "100"],
          }}
          key={"schedule_id"}
          rowClassName={(_record: any, index) =>
            index % 2 === 0 ? "slate-background" : "white-background"
          }
          onRow={(record) => ({
            onClick: () =>
              setOpenUserModal({
                visible: true,
                details: record,
              }),
            style: { cursor: "pointer" },
          })}
          style={{ whiteSpace: "nowrap" }}
          scroll={{ x: "max-content" }}
        />
      </div>
      <ChangeRadiusModal
        fetchList={fetchList}
        ids={openRadiusModal.ids}
        isModalOpen={openRadiusModal.visible}
        handleCancel={() => setOpenRadiusModal({ visible: false })}
        type={openRadiusModal.type}
      />
      <UserDetailsModal
        isModalVisible={openUserModal.visible}
        handleCancel={() => setOpenUserModal({ visible: false })}
        details={openUserModal.details}
      />
      <DeclineModal
        fetchList={fetchList}
        record={openDeclineModal.record}
        isModalOpen={openDeclineModal.visible}
        handleCancel={() => setOpenDeclineModal({ visible: false })}
        type={openDeclineModal.type}
      />
      <SettingsModal
        isModalVisible={openSettingsModal}
        handleCancel={() => setOpenSettingsModal(false)}
      />
    </div>
  );
};

export default ScheduleList;
