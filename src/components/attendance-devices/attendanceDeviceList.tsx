"use client";
import { DeleteOutlined, EditOutlined, PlusOutlined } from "@ant-design/icons";
import { Button, PaginationProps, Table } from "antd";
import Search from "antd/es/input/Search";
import { useEffect, useState } from "react";
import AttendanceDeviceForm from "../modals/attendanceDeviceForm";
import ConfirmationModal from "../modals/confirmationModal";
import { ActiveBadge } from "../icons/ActiveBadge";
import { DeActiveBadge } from "../icons/DeactiveBadge";
import { Device } from "@/interface/device";
import { convertIsoToCustomFormat } from "@/utils/helper";
import { deleteRequest, getRequest } from "@/services/apiRequestHandlers";
import showNotification from "../common/notification";
import { API_ROUTE } from "@/constants/api-routes";

const AttendanceDeviceList = () => {
    const [deviceList, setDeviceList] = useState<Device[]>([]);
    const [loading, setLoading] = useState(true);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [totalItems, setTotalItems] = useState<number>(0);
    const [searchKey, setSearchKey] = useState<string>("");
    const [openForm, setOpenForm] = useState<{
        visible: boolean;
        record?: Device;
    }>({ visible: false });
    const [deleteModal, setDeleteModal] = useState<{
        visible: boolean;
        id?: number;
    }>({ visible: false });

    useEffect(() => {
        fetchList(currentPage, pageSize, searchKey);
    }, [currentPage, pageSize]);

    const fetchList = async (
        pageNumber?: number,
        pageSize?: number,
        searchKey?: string
    ) => {
        try {
            const resp: any = await getRequest(
                `${API_ROUTE.DEVICE_LIST}/?page=${pageNumber}&page_size=${pageSize}&search=${searchKey}`
            );
            if (resp) {
                setDeviceList(resp?.items || []);
                setTotalItems(resp?.total || 0);
            }
        } catch (err) {
            console.log(err);
        } finally {
            setLoading(false);
        }
    };

    const columns = [
        {
            title: "Serial No",
            dataIndex: "serial_no",
            key: "serial_no",
        },
        {
            title: "Device IP",
            dataIndex: "device_ip",
            key: "device_ip",
        },
        {
            title: "Device Port",
            dataIndex: "device_port",
            key: "device_port",
        },
        {
            title: "Device Location",
            dataIndex: "device_location",
            key: "device_location",
        },
        {
            title: "Last Activity",
            key: "last_activity",
            dataIndex: "last_activity",
            render: (_: any, record: any) =>
                record?.last_activity
                    ? convertIsoToCustomFormat(record?.last_activity)
                    : "N/A",
        },
        {
            title: "Company Name",
            dataIndex: "company_name",
            key: "company_name",
        },
        {
            title: "Status",
            key: "status",
            dataIndex: "status",
            render: (text: string) =>
                text === "Active" ? <ActiveBadge /> : <DeActiveBadge />,
        },
        {
            title: "Actions",
            key: "actions",
            render: (_: any, record: any) => (
                <div className="flex w-full flex-row gap-2">
                    <Button
                        onClick={(e: any) => {
                            setOpenForm({
                                visible: true,
                                record: record,
                            });
                            e.stopPropagation();
                        }}
                        icon={<EditOutlined />}
                    />
                    <Button
                        onClick={(e: any) => {
                            setDeleteModal({
                                visible: true,
                                id: record?.id,
                            });
                            e.stopPropagation();
                        }}
                        danger
                        icon={<DeleteOutlined />}
                    />
                </div>
            ),
        },
    ];

    const handleDeleteDevice = async () => {
        try {
            const resp = await deleteRequest(
                `${API_ROUTE.DELETE_DEVICE}/${deleteModal.id}`
            );
            if (resp) {
                showNotification({
                    type: "success",
                    message: "Successfuly deleted the device !",
                });
                fetchList();
                setDeleteModal({ visible: false });
            }
        } catch (err: any) {
            console.log(err);
            showNotification({
                type: "error",
                message: err?.message
                    ? err?.message
                    : "Deletion Unsuccessful !",
            });
        }
    };
    const handleTableChange: PaginationProps["onChange"] = (page, pageSize) => {
        setCurrentPage(page);
        setPageSize(pageSize);
    };

    return (
        <div className="md:pl-[80px] md:pr-2 pt-4">
            <div>
                <div className="mb-4 flex flex-col items-center justify-between gap-4 md:flex-row md:gap-0">
                    <h1 className="text-lg">
                        Attendance Device Configuration
                    </h1>
                    <div className="flex items-center gap-4">
                        <Search
                            placeholder="Search"
                            allowClear
                            onChange={(e: any) => {
                                const value = e.target.value;
                                setSearchKey(value);

                                if (value === "") {
                                    fetchList(1, 10, "");
                                    setCurrentPage(1);
                                }
                            }}
                            onSearch={(value: string) => {
                                fetchList(1, 10, value);
                                setCurrentPage(1);
                            }}
                            enterButton
                        />
                        <Button
                            type="primary"
                            icon={<PlusOutlined />}
                            onClick={() => setOpenForm({ visible: true })}
                        >
                            Add Device
                        </Button>
                    </div>
                </div>
                <Table
                    loading={loading}
                    bordered
                    dataSource={deviceList?.map((item) => ({
                        ...item,
                        key: item.id,
                    }))}
                    columns={columns}
                    pagination={{
                        current: currentPage,
                        pageSize: pageSize,
                        total: totalItems,
                        onChange: handleTableChange,
                        showSizeChanger: true,
                        pageSizeOptions: ["10", "20", "50", "100"],
                    }}
                    key={"schedule_id"}
                    rowClassName={(_record: any, index) =>
                        index % 2 === 0
                            ? "slate-background"
                            : "white-background"
                    }
                    style={{ whiteSpace: "nowrap" }}
                    scroll={{ x: "max-content" }}
                />
            </div>
            <AttendanceDeviceForm
                isModalOpen={openForm.visible}
                handleCancel={() => setOpenForm({ visible: false })}
                selectedDevice={openForm.record}
                fetchList={fetchList}
            />
            <ConfirmationModal
                open={deleteModal.visible}
                onCancel={() => setDeleteModal({ visible: false })}
                onOk={handleDeleteDevice}
                message="You won't be able to revert this !"
            />
        </div>
    );
};

export default AttendanceDeviceList;
