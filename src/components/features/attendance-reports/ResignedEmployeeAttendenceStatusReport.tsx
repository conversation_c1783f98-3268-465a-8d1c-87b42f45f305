import {dateTimeFormatForExcel, getComanyLogoById} from "@/utils/helper";
import ExcelJS from "exceljs";

const firstHeaders = ["ID", "Name", "Designation"];
const thirdHeaders = [
    "Present",
    "Absent",
    "Leave",
    "Off Day",
    "Holiday",
    "Late",
    "Early",
    "Night Shift",
    "L/E time",
    "Cut Days",
    "Off Day Duty",
];

export async function ResignedEmployeeAttendanceStatusReport(
    data: any,
    reportName: any,
    startTime: any,
    endTime: any,
    company: any, companyId: any
): Promise<void> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Attendance");

    const fetchLogoAndGenerateExcel = async (): Promise<string> => {
        if (companyId) {
            const data = await getComanyLogoById(companyId);
            return data;
        } else {
            console.log("No Comapny Id");
            return "data:image/jpeg;base64,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";
        }
    };

    worksheet.mergeCells(`A1:AH1`);
    const logoRow = worksheet.getRow(1);
    let companyImage = await fetchLogoAndGenerateExcel();

    let companyLogo = workbook.addImage({
        base64: companyImage,
        extension: "jpeg",
    });

    worksheet.addImage(companyLogo, {
        tl: {col: 0, row: 0},
        ext: {width: 200, height: 100},
    });
    logoRow.height = 90;

    worksheet.mergeCells(`A2:AH3`);
    const headerRow = worksheet.getRow(2);
    headerRow.height = 40;
    headerRow.getCell(1).value = reportName + " of " + company;
    headerRow.getCell(1).font = {size: 16, bold: true};

    // Add "Color Meaning" header row
    worksheet.mergeCells(`AI2:AP2`);
    const colorMeaningHeaderRow = worksheet.getRow(2); // Row 1 since it's the first row in the merged cells
    colorMeaningHeaderRow.height = 20;
    colorMeaningHeaderRow.getCell("AI").value = "Color Meaning"; // Setting the value in cell AI1
    colorMeaningHeaderRow.font = {bold: true}; // Making the font bold

    // Merge cells AI2 to AO2
    worksheet.mergeCells("AI3:AO3");

    // Set "Half Day" text in the merged cells
    const halfDayCell = worksheet.getCell("AI3");
    halfDayCell.value = "Half Day";
    halfDayCell.font = {bold: true};

    // Set "Half Day" text in the merged cells
    const colorCodeCell = worksheet.getCell("AP3");
    // @ts-ignore
    colorCodeCell.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: {
            argb: "98FB98",
        },
    };

    const today = new Date();

    // Add "Print Date" header row
    worksheet.mergeCells(`A4:AH4`);
    const fromDateHeaderRow = worksheet.getRow(4);
    fromDateHeaderRow.height = 20;
    fromDateHeaderRow.getCell(1).value = `From Date: ${startTime}`;
    fromDateHeaderRow.font = {bold: true}; // Making the font bold

    // Merge cells AI2 to AO2
    worksheet.mergeCells("AI4:AO4");

    // Set "Half Day" text in the merged cells
    const attendanceRequestCell = worksheet.getCell("AI4");
    attendanceRequestCell.value = "Attendance Request";

    // Set "Half Day" text in the merged cells
    const colorCode1Cell = worksheet.getCell("AP4");
    // @ts-ignore
    colorCode1Cell.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: {
            argb: "FFF200",
        },
    };

    // Add "Print Date" header row
    worksheet.mergeCells(`A5:AH5`);
    const toDateHeaderRow = worksheet.getRow(5);
    toDateHeaderRow.height = 20;
    toDateHeaderRow.getCell(1).value = `To Date: ${endTime}`;
    toDateHeaderRow.font = {bold: true}; // Making the font bold

    // Merge cells AI2 to AO2
    worksheet.mergeCells("AI5:AO5");

    // Set "Half Day" text in the merged cells
    const lateAttendanceCell = worksheet.getCell("AI5");
    lateAttendanceCell.value = "Late Attendance";

    // Set "Half Day" text in the merged cells
    const colorCode2Cell = worksheet.getCell("AP5");
    // @ts-ignore
    colorCode2Cell.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: {
            argb: "FF0000",
        },
    };

    // Add "Print Date" header row
    worksheet.mergeCells(`A6:AH6`);
    const printDateHeaderRow = worksheet.getRow(6);
    printDateHeaderRow.height = 20;
    printDateHeaderRow.getCell(1).value = `Print Date: ${dateTimeFormatForExcel(
        today
    )}`;
    printDateHeaderRow.font = {bold: true}; // Making the font bold

    // Merge cells AI2 to AO2
    worksheet.mergeCells("AI6:AO6");

    // Set "Half Day" text in the merged cells
    const earlyExitCell = worksheet.getCell("AI6");
    earlyExitCell.value = "Early Exit";

    // Set "Half Day" text in the merged cells
    const colorCode3Cell = worksheet.getCell("AP6");
    // @ts-ignore
    colorCode3Cell.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: {
            argb: "1E90FF",
        },
    };

    const secondHeaders = data[0]?.attendance?.attendance?.map((item: any) => {
        return item?.date;
    });

    function formatDate(inputDate: string) {
        const parts = inputDate.split('/');
        const day = parts[0];
        const month = parts[1];
        const year = parts[2];

        const months = [
            'January', 'February', 'March', 'April', 'May', 'June',
            'July', 'August', 'September', 'October', 'November', 'December'
        ];

        const monthIndex = parseInt(month) - 1;
        const monthName = months[monthIndex];

        return `${day}-${monthName.substring(0, 3)}`;
    }

    function getDateRange(startDate: Date, endDate: Date) {
        let dateRange = [];
        let currentDate = new Date(startDate);

        // Loop through each day from start date to end date
        while (currentDate <= endDate) {
            // Format the date as "DD/MM/YYYY"
            let formattedDate = currentDate.getDate() + '/' + (currentDate.getMonth() + 1) + '/' + currentDate.getFullYear();
            dateRange.push(formattedDate);

            // Move to the next day
            currentDate.setDate(currentDate.getDate() + 1);
        }

        return dateRange;
    }

    // var inputDate = "01/05/2024";

// Split the input date by "/"
    var parts = startTime.split("/");
    var parte = endTime.split("/");

// Rearrange the parts to form the desired format "YYYY-MM-DD"
    var outputSDate = parts[2] + "-" + parts[1].padStart(2, '0') + "-" + parts[0].padStart(2, '0');
    var outputEDate = parte[2] + "-" + parte[1].padStart(2, '0') + "-" + parte[0].padStart(2, '0');

    console.log(outputSDate);
    console.log(outputEDate);
    // Example usage:
    let startDate = new Date(outputSDate);
    let endDate = new Date(outputEDate);
    let dateRange = getDateRange(startDate, endDate);

    let mySecondHeaders: any = []

    dateRange.map((value: string, index) => {
        mySecondHeaders.push(formatDate(value));
    });

    const allHeaders = [...firstHeaders, ...mySecondHeaders, ...thirdHeaders];

    // Add data to the Excel sheet
    const row = worksheet.addRow(allHeaders);

    // Rotate cells after the 3rd element by 90 degrees
    for (let i = 4; i <= allHeaders.length; i++) {
        const cell = row.getCell(i);
        cell.alignment = {
            vertical: "middle",
            horizontal: "center",
            textRotation: 90,
        };
    }

    for (let i = 1; i <= 3; i++) {
        row.getCell(i).alignment = {vertical: "middle", horizontal: "center"};
    }

    // Your existing code to add rows to the worksheet
    // Reset rotation for header row cells

    data.forEach((entry: any, rowIndex: any) => {
        const {
            unique_id,
            employee_name,
            designation,
            attendance,
            present,
            absent,
            leave,
            off_day,
            holiday,
            late,
            early,
            night_shift,
            total_e_time,
            off_day_duty,
        } = entry;

        // Create the row data
        const rowData = [unique_id, employee_name, designation];

        let cell;

        mySecondHeaders.forEach((headerValue: any, index: number) => {
            let value = attendance.attendance[index];
            // console.log(`From Mysecondheaders: ${index} ${value} ${attendance.attendance[index]?.check_in_time}`);
            let color_status;
            let bg;

            if (value?.half_day_status) {
                bg = "Green";
            } else if (value?.attendance_request) {
                bg = "Yellow";
            }

            if (value?.late_status) {
                color_status = "Red";
            } else if (value?.exit_status) {
                color_status = "Blue";
            } else {
                color_status = "";
            }

            const cellValue =
                value.status === "Weekend"
                    ? (
                        value.check_in_time || value.check_out_time
                            ? `${value?.check_in_time !== null ? value?.check_in_time : ""} - ${
                                value?.check_out_time !== null ? value.check_out_time : ""
                            } (${value?.worked_hour}), ${bg ? bg : ""}, ${color_status} (W)` : "W"
                    )
                    : value.status === "Absence"
                        ? "A"
                        : value.status === "Public Off Day"
                            ? (
                                value.check_in_time || value.check_out_time
                                    ? `${value?.check_in_time !== null ? value?.check_in_time : ""} - ${
                                        value?.check_out_time !== null ? value.check_out_time : ""
                                    } (${value?.worked_hour}), (DO)` : "DO"
                            )
                            : value.status === "Casual Leave(CL)"
                                ? (
                                    value.check_in_time || value.check_out_time
                                        ? `${value?.check_in_time !== null ? value?.check_in_time : ""} - ${
                                            value?.check_out_time !== null ? value.check_out_time : ""
                                        } (${value?.worked_hour}), (CL)` : "CL"
                                )
                                : value.status === "Sick Leave (SL)"
                                    ? (
                                        value.check_in_time || value.check_out_time
                                            ? `${value?.check_in_time !== null ? value?.check_in_time : ""} - ${
                                                value?.check_out_time !== null ? value.check_out_time : ""
                                            } (${value?.worked_hour}), (SL)` : "SL"
                                    )
                                    : value.status === "Earned Leave"
                                        ? (
                                            value.check_in_time || value.check_out_time
                                                ? `${value?.check_in_time !== null ? value?.check_in_time : ""} - ${
                                                    value?.check_out_time !== null ? value.check_out_time : ""
                                                } (${value?.worked_hour}), (EL)` : "EL"
                                        )
                                        : value.status === "Compensatory"
                                            ? (
                                                value.check_in_time || value.check_out_time
                                                    ? `${value?.check_in_time !== null ? value?.check_in_time : ""} - ${
                                                        value?.check_out_time !== null ? value.check_out_time : ""
                                                    } (${value?.worked_hour}), (C/off)` : "C/off"
                                            )
                                            : value.status === "Miscarriage"
                                                ? "Mis"
                                                : value.status === "Paternity"
                                                    ? "PT"
                                                    : value.status === "Maternity"
                                                        ? "MT"
                                                        : value.status === "LWP"
                                                            ? "LWP"
                                                            : value.check_in_time || value.check_out_time
                                                                ? `${value?.check_in_time !== null ? value?.check_in_time : ""} - ${
                                                                    value?.check_out_time !== null ? value.check_out_time : ""
                                                                } (${value?.worked_hour}), ${bg ? bg : ""}, ${color_status}`
                                                                : value.status;


            rowData.push(cellValue);
        })

        // attendance.attendance.forEach((value: any, i: any) => {
        //     let color_status;
        //
        //     if (value?.attendance_request) {
        //         color_status = "Yellow";
        //     } else if (value?.late_status) {
        //         color_status = "Red";
        //     } else if (value?.exit_status) {
        //         color_status = "Blue";
        //     } else if (value?.half_day_status) {
        //         color_status = "Green";
        //     } else {
        //         color_status = "";
        //     }
        //
        //     // const cellValue =
        //     //     value.status === "Weekend"
        //     //         ? "W"
        //     //         : value.status === "Absence"
        //     //             ? "A"
        //     //             : value.status === "Public Off Day" ? "DO" : value.check_in_time && value.check_out_time
        //     //                 ? `${value.check_in_time} - ${value.check_out_time} (${value.worked_hour}), ${color_status}`
        //     //                 : "";
        //
        //     const cellValue =
        //         value?.status === "Weekend"
        //             ? "W"
        //             : value?.status === "Absence"
        //                 ? "A"
        //                 : value?.status === "Public Off Day"
        //                     ? "DO"
        //                     : value?.status === "Casual Leave(CL)"
        //                         ? "CL"
        //                         : value?.status === "Sick Leave (SL)"
        //                             ? "SL"
        //                             : value?.status === "Earned Leave"
        //                                 ? "EL"
        //                                 : value?.status === "Compensatory"
        //                                     ? "RL"
        //                                     : value?.status === "Miscarriage"
        //                                         ? "Mis"
        //                                         : value?.status === "Paternity"
        //                                             ? "PT"
        //                                             : value?.status === "Maternity"
        //                                                 ? "MT"
        //                                                 : value?.status === "LWP"
        //                                                     ? "LWP"
        //                                                     : value?.check_in_time || value?.check_out_time
        //                                                         ? `${value?.check_in_time !== null ? value?.check_in_time : ""} - ${
        //                                                             value?.check_out_time !== null ? value?.check_out_time : ""
        //                                                         } (${value?.worked_hour}), ${color_status}`
        //                                                         : !value?.check_in_time && !value?.check_out_time ? "" : value?.status;
        //
        //     rowData.push(cellValue);
        // });

        // Push the remaining values to the rowData array
        rowData.push(
            present,
            absent,
            leave,
            off_day,
            holiday,
            late,
            early,
            night_shift,
            total_e_time,
            Math.floor(total_e_time / 150) + absent,
            off_day_duty
        );
        // Add the row to the worksheet
        const row: any = worksheet.addRow(rowData);

        if (rowIndex % 2 === 0) {
            row.fill = {
                type: "pattern",
                pattern: "solid",
                fgColor: {argb: "D9E2F3"},
            };
        }

        for (let i = 3; i < rowData.length; i++) {
            if (row._cells[i]._value.model.value.toString().includes(", Yellow")) {
                row._cells[i].fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: {argb: 'FFFFC000'} // Yellow
                };

                row._cells[i]._value.model.value = row._cells[i]._value.model.value
                    .toString()
                    .replace(", Yellow", "");

                // row._cells[i].font = {color: {argb: "FFFFC000"}, bold: true};
            }

            if (
                row._cells[i]._value.model.value.toString().includes(" Green")
            ) {
                row._cells[i].fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: {argb: 'FF00B050'} // Green
                };

                row._cells[i]._value.model.value = row._cells[i]._value.model.value
                    .toString()
                    .replace(" Green", "");
                // row._cells[i].font = { color: { argb: "FF00B050" }, bold: true };
            } else {
                if (
                    row._cells[i]._value.model.value.toString().includes(", Red")
                ) {
                    row._cells[i]._value.model.value = row._cells[i]._value.model.value
                        .toString()
                        .replace(", Red", "");
                    row._cells[i].font = {color: {argb: "FFFF0000"}, bold: true};
                } else if (
                    row._cells[i]._value.model.value.toString().includes(", Blue")
                ) {
                    row._cells[i]._value.model.value = row._cells[i]._value.model.value
                        .toString()
                        .replace(", Blue", "");
                    row._cells[i].font = {color: {argb: "FF0000FF"}, bold: true};
                } else if (row._cells[i]._value.model.value.toString().includes(",")) {
                    row._cells[i]._value.model.value = row._cells[i]._value.model.value
                        .toString()
                        .replace(",", "");
                }
            }

            if (
                row._cells[i]._value.model.value.toString().includes(", Red")
            ) {
                row._cells[i]._value.model.value = row._cells[i]._value.model.value
                    .toString()
                    .replace(", Red", "");
                row._cells[i].font = {color: {argb: "FFFF0000"}, bold: true};
            } else if (
                row._cells[i]._value.model.value.toString().includes(", Blue")
            ) {
                row._cells[i]._value.model.value = row._cells[i]._value.model.value
                    .toString()
                    .replace(", Blue", "");
                row._cells[i].font = {color: {argb: "FF0000FF"}, bold: true};
            } else if (row._cells[i]._value.model.value.toString().includes(",")) {
                row._cells[i]._value.model.value = row._cells[i]._value.model.value
                    .toString()
                    .replace(",", "");
            }
        }

        worksheet.eachRow({includeEmpty: true}, (row, rIndex) => {
            row.eachCell({includeEmpty: true}, (cell) => {
                cell.border = {
                    top: {style: "thin", color: {argb: "FFB1B1B1"}}, // Border color is red
                    left: {style: "thin", color: {argb: "FFB1B1B1"}},
                    bottom: {style: "thin", color: {argb: "FFB1B1B1"}},
                    right: {style: "thin", color: {argb: "FFB1B1B1"}},
                };
            });
            if (rIndex > 6) {
                row.height = 150;
            }
        });

        // Apply alignment to cells
        for (let i = 4; i <= rowData.length; i++) {
            row.getCell(i).alignment = {
                vertical: "middle",
                horizontal: "center",
                textRotation: 90,
            };
            row.height = 150;
        }

        for (let i = 1; i <= 3; i++) {
            row.getCell(i).alignment = {vertical: "middle", horizontal: "center"};
            row.height = 150;
        }
    });
    for (let i = 1; i <= 20; i++) {
        // Assuming 20 is the maximum column index for headers
        headerRow.getCell(i).alignment = {
            textRotation: 0,
            vertical: "middle",
            horizontal: "center",
        };
        fromDateHeaderRow.getCell(i).alignment = {
            textRotation: 0,
            vertical: "middle",
            horizontal: "center",
        };
        toDateHeaderRow.getCell(i).alignment = {
            textRotation: 0,
            vertical: "middle",
            horizontal: "center",
        };
        printDateHeaderRow.getCell(i).alignment = {
            textRotation: 0,
            vertical: "middle",
            horizontal: "center",
        };

        worksheet.columns.forEach((column, index) => {
            if (index < 3) {
                // Columns D to AS
                column.width = 20;
            }
        });
    }


    try {
        // Generate buffer
        const buffer = await workbook.xlsx.writeBuffer();

        // Trigger file download
        const blob = new Blob([buffer], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `${reportName ? reportName : "Report"}.xlsx`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
    } catch (e) {
        console.log(e);
    }
}
