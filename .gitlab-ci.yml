variables:
    CENTRAL_HCR_REGISTRY: "registry.tn.harbor"
    IMAGE_NAME_DEV: erp-microservice-frontend-dev
    IMAGE_NAME_STAGE: erp-microservice-frontend-stage
    TAG_DEV: "${CI_PIPELINE_IID}"
    TAG_STAGE: "${CI_PIPELINE_IID}"
    APP_NAME_DEV: erp-dev
    APP_NAME_STAGE: erp-stage
    DOCKERFILE_PATH_DEV: /opt/erp-env-dockerfile/erp-microservice-frontend-dev/
    DOCKERFILE_PATH_STAGE: /opt/erp-env-dockerfile/erp-microservice-frontend-stage/

stages:
    - build_dev
    - deploy_dev
    - push_dev
    - build_stage
    - deploy_stage
    - push_stage

build_dev:
    stage: build_dev
    script:
        - cp -a $DOCKERFILE_PATH_DEV/Dockerfile $DOCKERFILE_PATH_DEV/.env $CI_PROJECT_DIR
        - echo "Building Docker image for dev"
        - docker build -t "$IMAGE_NAME_DEV:$TAG_DEV" .
    tags:
        - erp-dev-build-server
    only:
        - deployment-dev

deploy_dev:
    stage: deploy_dev
    script:
        - echo "Stopping and removing existing container"
        - docker rm -f erp-microservice-frontend-dev || true
        - echo "Running new container"
        - docker run -d -p 5000:3000 --name erp-microservice-frontend-dev --restart always "$IMAGE_NAME_DEV:$TAG_DEV"
    tags:
        - erp-dev-build-server
    only:
        - deployment-dev
        
push_dev:
    stage: push_dev
    script:
        - echo "Logging into HCR for Dev"
        - docker login -u "$CI_REGISTRY_USER_CENTRAL" -p "$CI_REGISTRY_PASSWORD_CENTRAL" "$CENTRAL_HCR_REGISTRY"
        - echo "Tagging image"
        - docker tag "$IMAGE_NAME_DEV:$TAG_DEV" "$CENTRAL_HCR_REGISTRY/$APP_NAME_DEV/$IMAGE_NAME_DEV:$TAG_DEV"
        - echo "Pushing image to HCR"
        - docker push "$CENTRAL_HCR_REGISTRY/$APP_NAME_DEV/$IMAGE_NAME_DEV:$TAG_DEV"
        - echo "Cleaning up local images"
        # - docker rmi "$IMAGE_NAME_DEV:$TAG_DEV" || true
        - docker rmi "$CENTRAL_HCR_REGISTRY/$APP_NAME_DEV/$IMAGE_NAME_DEV:$TAG_DEV" || true
    tags:
        - erp-dev-build-server
    only:
        - deployment-dev

build_stage:
    stage: build_stage
    script:
        - cp -a $DOCKERFILE_PATH_STAGE/Dockerfile $DOCKERFILE_PATH_STAGE/.env $CI_PROJECT_DIR
        - echo "Building Docker image for stage"
        - docker build -t "$IMAGE_NAME_STAGE:$TAG_STAGE" .
    tags:
        - erp-dev-build-server
    only:
        - deployment-stage

deploy_stage:
    stage: deploy_stage
    script:
        - echo "Stopping and removing existing container"
        - docker rm -f erp-microservice-frontend-stage || true
        - echo "Running new container"
        - docker run -d -p 5070:3000 --name erp-microservice-frontend-stage --restart always "$IMAGE_NAME_STAGE:$TAG_STAGE"
    tags:
        - erp-dev-build-server
    only:
        - deployment-stage

push_stage:
    stage: push_stage
    script:
        - echo "Logging into HCR for Stage"
        - docker login -u "$CI_REGISTRY_USER_CENTRAL" -p "$CI_REGISTRY_PASSWORD_CENTRAL" "$CENTRAL_HCR_REGISTRY"
        - echo "Tagging image"
        - docker tag "$IMAGE_NAME_STAGE:$TAG_STAGE" "$CENTRAL_HCR_REGISTRY/$APP_NAME_STAGE/$IMAGE_NAME_STAGE:$TAG_STAGE"
        - echo "Pushing image to HCR"
        - docker push "$CENTRAL_HCR_REGISTRY/$APP_NAME_STAGE/$IMAGE_NAME_STAGE:$TAG_STAGE"
        - echo "Cleaning up local images"
        # - docker rmi "$IMAGE_NAME_STAGE:$TAG_STAGE" || true
        - docker rmi "$CENTRAL_HCR_REGISTRY/$APP_NAME_STAGE/$IMAGE_NAME_STAGE:$TAG_STAGE" || true
    tags:
        - erp-dev-build-server
    only:
        - deployment-stage