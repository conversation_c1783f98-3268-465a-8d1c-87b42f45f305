import { useEffect, useState } from "react";
import { AppBar, Toolbar, Typography, Avatar, Box } from "@mui/material";

import { getCookie, setCookie } from "cookies-next"; // Import cookies-next
import { getEmployeeDetails } from "@/services/WorkStationServices/NotificationServies";

const Header = ({ reportType }: any) => {
  const [employee, setEmployee] = useState<any>(null);
  useEffect(() => {
    const fetchEmployee = async () => {
      const uId = getCookie("uId");
      if (!uId) return;
      const response = await getEmployeeDetails.employeeData(uId);
      if (response.status === "SUCCESS") {
        setEmployee(response.data);
        setCookie("employeeId", response.data.employee_id);
      }
    };

    fetchEmployee();
  }, []);

  return (
    <>
      {
        <AppBar position="static" className="bg-blue-700">
          <Toolbar sx={{ display: "flex", justifyContent: "space-between" }}>
            {/* Report Type Section */}
            {reportType !== "workspace" ? (
              <Typography
                component="div"
                className="text-md xl:text-lg font-semibold"
              >
                ERP / {reportType} / download
              </Typography>
            ) : (
              <Typography
                className="text-md xl:text-2xl font-semibold"
                component="div"
              >
                ERP / {reportType}
              </Typography>
            )}

            {/* Employee Info Section */}
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              {employee ? (
                <>
                  <Avatar alt={employee.name} src={employee?.employee_image} />
                  <Typography variant="body1">
                    {employee?.employee_name}
                  </Typography>
                </>
              ) : (
                <Typography variant="body1">Loading...</Typography>
              )}
            </Box>
          </Toolbar>
        </AppBar>
      }
    </>
  );
};

export default Header;
