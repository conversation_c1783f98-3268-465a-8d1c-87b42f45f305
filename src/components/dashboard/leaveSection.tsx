import { LeaveType } from "@/interface/dashboard-models";
import { getRequest } from "@/services/apiRequestHandlers";
import { API_ROUTE } from "@/constants/api-routes";
import { useEmployeeContext } from "@/store/EmployeeContext";
import dayjs from "dayjs";
import React, { useEffect, useState } from "react";
import { MdNoAccounts, MdRunningWithErrors, MdTimerOff } from "react-icons/md";
import { RiHomeOfficeFill } from "react-icons/ri";
import AddHomeWorkIcon from "@mui/icons-material/AddHomeWork";
const LeaveSection = () => {
  const [leaveTypes, setLeaveTypes] = useState<LeaveType[]>([]);
  const [leaveInfo, setLeaveInfo] = useState<any>();
  const { employeeId } = useEmployeeContext();
  const [attendanceInfo, setAttendanceInfo] = useState<any[]>([]);
  const [selectedSummary, setSelectedSummary] = useState<string>("from_21st_to_20th");
  const currentYear = dayjs().year();

  const getEmployeeLeaveDetails = async (
    year: number,
    employeeId: string,
    type: string
  ) => {
    const userId = localStorage.getItem("userEmployeeId");
    try {
      const filterType = type === "yearly" ? "" : type;
      let response:any;
      if(employeeId==userId){
        response = await getRequest<{data: any}>(
          `${API_ROUTE.DASHBOARD_MY_WORKSPACE_CARD}?year=${year}&filter_type=${filterType}`
        );
      } else {
        response = await getRequest<{data: any}>(
          `${API_ROUTE.DASHBOARD_MY_WORKSPACE_CARD}?year=${year}&employee_id=${employeeId}&filter_type=${filterType}`
        );
      }

      if (response && response.data) {
        setLeaveInfo(response.data);
      }
    } catch (error) {
      console.error("Error fetching leave details:", error);
    }
  };

  useEffect(() => {
    getEmployeeLeaveDetails(currentYear, employeeId, selectedSummary);
  }, [currentYear, employeeId, selectedSummary]);

  useEffect(() => {
    const constructData = (leaveInfo: any) => {
      const data = [
        {
          name: "Absent",
          icon: <MdNoAccounts />,
          count: leaveInfo?.absent,
        },
        {
          name: "Late",
          icon: <MdTimerOff />,
          count: leaveInfo?.late_in,
        },
        {
          name: "Early",
          icon: <MdRunningWithErrors />,
          count: leaveInfo?.early_exit,
        },
        {
          name: "WFH",
          icon: <RiHomeOfficeFill />,
          count: leaveInfo?.wfh_request,
        },
        {
          name: "Adj",
          icon: <AddHomeWorkIcon />,
          count: leaveInfo?.adj_request,
        },
      ];
      return data;
    };
    setAttendanceInfo(constructData(leaveInfo));
    function filterValidLeaves(leavesArray:LeaveType[]) {
      const validLeaves = [
          "Compensatory",
          "Casual Leave(CL)",
          "Sick Leave (SL)",
          "Earned Leave"
      ];

      return leavesArray?.filter((leave:LeaveType) => validLeaves.includes(leave.name));
  }
  const filteredLeaves = filterValidLeaves(leaveInfo?.leaves);
  setLeaveTypes(filteredLeaves);

  }, [leaveInfo]);

  const colCount = leaveTypes?.length || 1;
  const colClass =
    colCount === 1
      ? "lg:grid-cols-1"
      : colCount === 2
      ? "lg:grid-cols-2"
      : colCount === 3
      ? "lg:grid-cols-3"
      : colCount === 4
      ? "lg:grid-cols-4"
      : "lg:grid-cols-4";

  return (
    <div className="w-full  mb-4 flex flex-row lg:flex-col xl:flex-row gap-4 ">
    {/* Leave Summary */}
    <div className="w-full xl:w-[58%] shadow-lg">
      <h3 className="font-bold pl-2 pb-6 md:pb-0 text-md text-center">Leave Summary(Yearly)</h3>
      <div className={`grid grid-cols-1 md:grid-cols-2 ${colClass} my-4 mx-2 border-gray-300  bg-gray-100`}>
        {leaveTypes?.length > 0 &&
          leaveTypes?.map((leave: LeaveType, index: number) => (
            <div
              key={index}
              className={` lg:border-b-0 border-gray-300 relative flex justify-center items-center transition-transform
                duration-300 ease-in-out transform hover:scale-105 ${
                  index < leaveTypes.length - 1 ? "border-b-2" : ""
                }
                ${index >= leaveTypes.length - 2 ? "md:border-b-0" : ""}  ${
                index !== leaveTypes.length - 1 ? "lg:border-r-2" : ""
              }`}
            >
              <div className="flex flex-col items-center p-4">
                <div className="text-sm md:text-sm lg:text-lg xl:text-sm  font-semibold text-sky-800 flex items-center gap-2">
                  <img
                    src={leave.icon}
                    alt={leave.name}
                    className="h-[26px] w-[26px]"
                  />
                  <p className=" whitespace-nowrap">{leave.name}</p>
                </div>
                <div
                  className={`
                  ${leave.name === "Casual Leave(CL)" && "text-[#f8961e]"}
                  ${leave.name === "Sick Leave (SL)" && "text-[#43aa8b]"}
                  ${leave.name === "Compensatory" && "text-[#f9c74f]"}
                  ${leave.name === "Earned Leave" && "text-[#90be6d]"}
                  font-bold text-sm xl:text-xs`}
                >
                  {leave.available} - Days Available
                </div>
                <div className="text-red-500 font-bold text-sm xl:text-xs">
                  {leave.taken} - Days Taken
                </div>
              </div>
            </div>
          ))}
      </div>
    </div>

    {/* Attendance Summary */}
    <div className="w-full xl:w-[42%] shadow-lg">
      <div className="flex flex-col gap-1 md:gap-0 md:flex-row justify-center items-center">
        <h3 className="font-bold pr-2 lg:pl-2 text-md  text-end lg:text-start xl:text-center">
          Attendance Summary
        </h3>
        <select
          className="border border-gray-300 px-3 text-sm"
          value={selectedSummary}
          onChange={(e) => setSelectedSummary(e.target.value)}
        >
          <option value="yearly">Yearly</option>
          <option value="current_month">Current Month</option>
          <option value="last_month">Previous Month</option>
          <option value="from_21st_to_20th">Service Month</option>
        </select>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg_xl:grid-cols-5  my-4 mx-2 bg-gray-100">
        {attendanceInfo.length > 0 &&
          attendanceInfo.map((info: any, index) => (
            <div
              key={index}
              className={` lg:border-b-0 border-gray-300 relative flex justify-center items-center transition-transform
                duration-300 ease-in-out transform hover:scale-105 ${
                  index < attendanceInfo.length - 1 ? "border-b-2" : ""
                }
                ${
                  index >= attendanceInfo.length - 2 ? "md:border-b-0" : ""
                } ${
                index !== attendanceInfo.length - 1 ? "lg:border-r-2" : ""
              }`}
            >
              <div className=" px-4 py-4 xl:py-[13px] flex flex-col gap-[22px] xl:gap-4 items-center justify-center w-full">
                <div className="text-xs sm:text-sm md:text-sm lg:text-lg  font-semibold text-sky-800 flex items-center gap-2">
                  <div className="text-2xl text-[#875b7b]">{info.icon}</div>
                  <p className=" whitespace-nowrap xl:text-sm">{info.name}</p>
                </div>
                <div
                  className={`
                  ${info.name === "Absent" && "text-red-500"}
                  ${info.name === "Late Status" && "text-red-500"}
                  ${info.name === "Early Exit" && "text-[#004cff]"}
                  ${info.name === "WFH" && "text-[#008121]"}
                  font-bold text-sm xl:text-xs`}
                >
                  {info.count || 0} - Days
                </div>
              </div>
            </div>
          ))}
      </div>
    </div>
  </div>

  );
};

export default LeaveSection;

//////// Previous Leave card design

{
  /* <div
          className={`grid grid-cols-1 md:grid-cols-2  ${colClass} m-4 border-gray-300  bg-gray-100`}
        >
          {leaveTypes.length > 0 &&
            leaveTypes?.map((leave: LeaveType, index: number) => (
              <div
                key={index}
                className={` lg:border-b-0 border-gray-300 relative flex justify-center items-center transition-transform
                  duration-300 ease-in-out transform hover:scale-105 ${
                    index < leaveTypes.length - 1 ? "border-b-2" : ""
                  }
                  ${index >= leaveTypes.length - 2 ? "md:border-b-0" : ""}  ${
                  index !== leaveTypes.length - 1 ? "lg:border-r-2" : ""
                } ${colCount>5 && index===3 && "lg:border-r-0"} ${colCount>5  && "lg:border-r-2"}`}
              >
                <div className="flex flex-col items-center p-4">
                  <div className="text-sm md:text-sm lg:text-lg 2xl:text-sm  font-semibold text-sky-800 flex items-center gap-2">
                    <img
                      src={leave.icon}
                      alt={leave.name}
                      className="h-[26px] w-[26px]"
                    />
                    <p className=" whitespace-nowrap">
                      {leave.name || "Leave Type"}
                    </p>
                  </div>
                  <div
                    className={`
                    ${leave.name === "Casual Leave(CL)" && "text-[#f8961e]"}
                    ${leave.name === "Sick Leave (SL)" && "text-[#43aa8b]"}
                    ${leave.name === "Compensatory" && "text-[#8E1616]"}
                    ${leave.name === "Earned Leave" && "text-[#90be6d]"}
                    font-bold text-sm 2xl:text-xs`}
                  >
                    {leave.available || 0} - Days Available
                  </div>
                  <div className="text-red-500 font-bold text-sm xl:text-xs">
                    {leave.taken_days_for_card|| 0} - Days Taken
                  </div>
                </div>
              </div>
            ))}
        </div> */
}
