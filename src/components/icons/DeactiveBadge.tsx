interface Props {
    color?: string;
}
export const DeActiveBadge = ({ color }: Props) => {
    return (
        <svg
            width="82"
            height="22"
            viewBox="0 0 82 22"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g style={{ mixBlendMode: "multiply" }}>
                <rect x="0.5" width="81" height="22" rx="11" fill="#F2F2F7" />
                <path
                    d="M17 11C17 8.51562 14.9844 6.5 12.5 6.5C10.0156 6.5 8 8.51562 8 11C8 13.4844 10.0156 15.5 12.5 15.5C14.9844 15.5 17 13.4844 17 11Z"
                    stroke="#DBD2D2"
                    stroke-miterlimit="10"
                />
                <path
                    d="M12.5 14.375C14.364 14.375 15.875 12.864 15.875 11C15.875 9.13604 14.364 7.625 12.5 7.625C10.636 7.625 9.125 9.13604 9.125 11C9.125 12.864 10.636 14.375 12.5 14.375Z"
                    fill="#85868B"
                />
                <path
                    d="M24.2841 15H21.4588V6.27273H24.3736C25.2287 6.27273 25.9631 6.44744 26.5767 6.79688C27.1903 7.14347 27.6605 7.64205 27.9872 8.29261C28.3168 8.94034 28.4815 9.71733 28.4815 10.6236C28.4815 11.5327 28.3153 12.3139 27.983 12.9673C27.6534 13.6207 27.1761 14.1236 26.5511 14.4759C25.9261 14.8253 25.1705 15 24.2841 15ZM22.7756 13.8494H24.2116C24.8764 13.8494 25.429 13.7244 25.8693 13.4744C26.3097 13.2216 26.6392 12.8565 26.858 12.3793C27.0767 11.8991 27.1861 11.3139 27.1861 10.6236C27.1861 9.93892 27.0767 9.35795 26.858 8.88068C26.642 8.40341 26.3196 8.04119 25.8906 7.79403C25.4616 7.54687 24.929 7.4233 24.2926 7.4233H22.7756V13.8494ZM32.8377 15.1321C32.1928 15.1321 31.6374 14.9943 31.1715 14.7188C30.7085 14.4403 30.3505 14.0497 30.0977 13.5469C29.8477 13.0412 29.7227 12.4489 29.7227 11.7699C29.7227 11.0994 29.8477 10.5085 30.0977 9.99716C30.3505 9.4858 30.7028 9.08665 31.1545 8.79972C31.609 8.51278 32.1403 8.36932 32.7482 8.36932C33.1175 8.36932 33.4755 8.4304 33.8221 8.55256C34.1687 8.67472 34.4798 8.86648 34.7553 9.12784C35.0309 9.3892 35.2482 9.72869 35.4073 10.1463C35.5664 10.5611 35.646 11.0653 35.646 11.6591V12.1108H30.4428V11.1562H34.3974C34.3974 10.821 34.3292 10.5241 34.1928 10.2656C34.0565 10.0043 33.8647 9.7983 33.6175 9.64773C33.3732 9.49716 33.0863 9.42188 32.7567 9.42188C32.3988 9.42188 32.0863 9.50994 31.8192 9.68608C31.555 9.85937 31.3505 10.0866 31.2056 10.3679C31.0636 10.6463 30.9925 10.9489 30.9925 11.2756V12.0213C30.9925 12.4588 31.0692 12.831 31.2227 13.1378C31.3789 13.4446 31.5962 13.679 31.8746 13.8409C32.1531 14 32.4783 14.0795 32.8505 14.0795C33.092 14.0795 33.3121 14.0455 33.511 13.9773C33.7099 13.9062 33.8817 13.8011 34.0266 13.6619C34.1715 13.5227 34.2823 13.3509 34.359 13.1463L35.565 13.3636C35.4684 13.7187 35.2951 14.0298 35.0451 14.2969C34.7979 14.5611 34.4869 14.767 34.1119 14.9148C33.7397 15.0597 33.315 15.1321 32.8377 15.1321ZM38.9645 15.1449C38.5497 15.1449 38.1747 15.0682 37.8395 14.9148C37.5043 14.7585 37.2386 14.5327 37.0426 14.2372C36.8494 13.9418 36.7528 13.5795 36.7528 13.1506C36.7528 12.7812 36.8239 12.4773 36.9659 12.2386C37.108 12 37.2997 11.8111 37.5412 11.6719C37.7827 11.5327 38.0526 11.4276 38.3509 11.3565C38.6491 11.2855 38.9531 11.2315 39.2628 11.1946C39.6548 11.1491 39.973 11.1122 40.2173 11.0838C40.4616 11.0526 40.6392 11.0028 40.75 10.9347C40.8608 10.8665 40.9162 10.7557 40.9162 10.6023V10.5724C40.9162 10.2003 40.8111 9.91193 40.6009 9.70739C40.3935 9.50284 40.0838 9.40057 39.6719 9.40057C39.2429 9.40057 38.9048 9.49574 38.6577 9.68608C38.4134 9.87358 38.2443 10.0824 38.1506 10.3125L36.9531 10.0398C37.0952 9.64205 37.3026 9.32102 37.5753 9.0767C37.8509 8.82955 38.1676 8.65057 38.5256 8.53977C38.8835 8.42614 39.2599 8.36932 39.6548 8.36932C39.9162 8.36932 40.1932 8.40057 40.4858 8.46307C40.7813 8.52273 41.0568 8.63352 41.3125 8.79545C41.571 8.95739 41.7827 9.18892 41.9474 9.49006C42.1122 9.78835 42.1946 10.1761 42.1946 10.6534V15H40.9503V14.1051H40.8991C40.8168 14.2699 40.6932 14.4318 40.5284 14.5909C40.3636 14.75 40.152 14.8821 39.8935 14.9872C39.6349 15.0923 39.3253 15.1449 38.9645 15.1449ZM39.2415 14.1222C39.5938 14.1222 39.8949 14.0526 40.1449 13.9134C40.3977 13.7741 40.5895 13.5923 40.7202 13.3679C40.8537 13.1406 40.9205 12.8977 40.9205 12.6392V11.7955C40.875 11.8409 40.7869 11.8835 40.6562 11.9233C40.5284 11.9602 40.3821 11.9929 40.2173 12.0213C40.0526 12.0469 39.892 12.071 39.7358 12.0938C39.5795 12.1136 39.4489 12.1307 39.3438 12.1449C39.0966 12.1761 38.8707 12.2287 38.6662 12.3026C38.4645 12.3764 38.3026 12.483 38.1804 12.6222C38.0611 12.7585 38.0014 12.9403 38.0014 13.1676C38.0014 13.483 38.1179 13.7216 38.3509 13.8835C38.5838 14.0426 38.8807 14.1222 39.2415 14.1222ZM46.6488 15.1321C46.0153 15.1321 45.4698 14.9886 45.0124 14.7017C44.5579 14.4119 44.2085 14.0128 43.9641 13.5043C43.7198 12.9957 43.5977 12.4134 43.5977 11.7571C43.5977 11.0923 43.7227 10.5057 43.9727 9.99716C44.2227 9.4858 44.5749 9.08665 45.0295 8.79972C45.484 8.51278 46.0195 8.36932 46.636 8.36932C47.1332 8.36932 47.5763 8.46165 47.9656 8.64631C48.3548 8.82812 48.6687 9.08381 48.9073 9.41335C49.1488 9.7429 49.2923 10.1278 49.3377 10.5682H48.0977C48.0295 10.2614 47.8732 9.99716 47.6289 9.77557C47.3874 9.55398 47.0636 9.44318 46.6573 9.44318C46.3022 9.44318 45.9911 9.53693 45.7241 9.72443C45.4599 9.90909 45.2539 10.1733 45.1062 10.517C44.9585 10.858 44.8846 11.2614 44.8846 11.7273C44.8846 12.2045 44.957 12.6165 45.1019 12.9631C45.2468 13.3097 45.4513 13.5781 45.7156 13.7685C45.9826 13.9588 46.2965 14.054 46.6573 14.054C46.8988 14.054 47.1175 14.0099 47.3136 13.9219C47.5124 13.831 47.6786 13.7017 47.8121 13.5341C47.9485 13.3665 48.0437 13.1648 48.0977 12.929H49.3377C49.2923 13.3523 49.1545 13.7301 48.9244 14.0625C48.6942 14.3949 48.386 14.6562 47.9996 14.8466C47.6161 15.0369 47.1658 15.1321 46.6488 15.1321ZM53.7557 8.45455V9.47727H50.1804V8.45455H53.7557ZM51.1392 6.88636H52.4134V13.0781C52.4134 13.3253 52.4503 13.5114 52.5241 13.6364C52.598 13.7585 52.6932 13.8423 52.8097 13.8878C52.929 13.9304 53.0582 13.9517 53.1974 13.9517C53.2997 13.9517 53.3892 13.9446 53.4659 13.9304C53.5426 13.9162 53.6023 13.9048 53.6449 13.8963L53.875 14.9489C53.8011 14.9773 53.696 15.0057 53.5597 15.0341C53.4233 15.0653 53.2528 15.0824 53.0483 15.0852C52.7131 15.0909 52.4006 15.0312 52.1108 14.9062C51.821 14.7812 51.5866 14.5881 51.4077 14.3267C51.2287 14.0653 51.1392 13.7372 51.1392 13.3423V6.88636ZM55.1651 15V8.45455H56.4393V15H55.1651ZM55.8086 7.4446C55.587 7.4446 55.3967 7.37074 55.2376 7.22301C55.0813 7.07244 55.0032 6.89347 55.0032 6.68608C55.0032 6.47585 55.0813 6.29687 55.2376 6.14915C55.3967 5.99858 55.587 5.9233 55.8086 5.9233C56.0302 5.9233 56.2191 5.99858 56.3754 6.14915C56.5344 6.29687 56.614 6.47585 56.614 6.68608C56.614 6.89347 56.5344 7.07244 56.3754 7.22301C56.2191 7.37074 56.0302 7.4446 55.8086 7.4446ZM63.7528 8.45455L61.3793 15H60.0156L57.6378 8.45455H59.0057L60.6634 13.4915H60.7315L62.3849 8.45455H63.7528ZM67.5487 15.1321C66.9038 15.1321 66.3484 14.9943 65.8825 14.7188C65.4194 14.4403 65.0614 14.0497 64.8086 13.5469C64.5586 13.0412 64.4336 12.4489 64.4336 11.7699C64.4336 11.0994 64.5586 10.5085 64.8086 9.99716C65.0614 9.4858 65.4137 9.08665 65.8654 8.79972C66.32 8.51278 66.8512 8.36932 67.4592 8.36932C67.8285 8.36932 68.1864 8.4304 68.533 8.55256C68.8796 8.67472 69.1907 8.86648 69.4663 9.12784C69.7418 9.3892 69.9592 9.72869 70.1183 10.1463C70.2773 10.5611 70.3569 11.0653 70.3569 11.6591V12.1108H65.1538V11.1562H69.1083C69.1083 10.821 69.0401 10.5241 68.9038 10.2656C68.7674 10.0043 68.5756 9.7983 68.3285 9.64773C68.0842 9.49716 67.7972 9.42188 67.4677 9.42188C67.1097 9.42188 66.7972 9.50994 66.5302 9.68608C66.266 9.85937 66.0614 10.0866 65.9165 10.3679C65.7745 10.6463 65.7035 10.9489 65.7035 11.2756V12.0213C65.7035 12.4588 65.7802 12.831 65.9336 13.1378C66.0898 13.4446 66.3072 13.679 66.5856 13.8409C66.864 14 67.1893 14.0795 67.5614 14.0795C67.8029 14.0795 68.0231 14.0455 68.2219 13.9773C68.4208 13.9062 68.5927 13.8011 68.7376 13.6619C68.8825 13.5227 68.9933 13.3509 69.07 13.1463L70.2759 13.3636C70.1793 13.7187 70.006 14.0298 69.756 14.2969C69.5089 14.5611 69.1978 14.767 68.8228 14.9148C68.4506 15.0597 68.0259 15.1321 67.5487 15.1321Z"
                    fill="#AEAEB2"
                />
            </g>
        </svg>
    );
};
