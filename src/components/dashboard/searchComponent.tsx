import { useEmployeeContext } from "@/store/EmployeeContext";
import { Autocomplete, TextField } from "@mui/material";
import React, { useEffect, useState } from "react";
import { RxAvatar } from "react-icons/rx";
import { getRequest } from "@/services/apiRequestHandlers";
import { API_ROUTE } from "@/constants/api-routes";

interface Props {
  companyId : string
}
const SearchComponent = ({companyId}:Props) => {
  const [selectedOption, setSelectedOption] = useState<{
    image: string | undefined;
    name: string;
    id: string;
    label: string;
  } | null>(null);
 
  const [options, setOptions] = useState<
    {
      image: string | undefined;
      name: string;
      id: string;
      label: string;
    }[]
  >([]);
  const { setEmployeeId , employeeId } = useEmployeeContext();
  async function getEmployees(query: string) {
    if(companyId) {
      try {
        const response:any = await getRequest<{data: any[]}>(`${API_ROUTE.EMPLOYEE_DROPDOWN_WITH_IMAGE}?company_id=${companyId}&query=${query}`);

        if (response) {  
          const formattedOptions = response?.map((employee: any) => ({
            id: String(employee.id),
            label: employee.name,
            name: employee.name,
            image: employee.image,
          }));
          setOptions(formattedOptions);

          if (employeeId) {
            const matchedEmployee = formattedOptions.find((emp:any) => emp.id === String(employeeId));
            if (matchedEmployee) {
              setSelectedOption(matchedEmployee);
            }
          }
        } else {
          setOptions([]);
        }
      } catch (error) {
        setOptions([]);
        console.error("Error fetching employees:", error);
      }
    }
  }


  useEffect(() => {
    getEmployees("");
  }, [employeeId,companyId]);

  return (
    <div className="w-full mb-4 mt-2">
      <div className="w-full flex flex-col md:flex-row justify-center items-center gap-4">
        <p className="md:pb-4">Search a Particular Employee</p>
        <Autocomplete
          options={options}
          size="small"
          getOptionLabel={(option) => option?.label || ""}
          value={selectedOption}
          onChange={(event, newValue) => {
            if (newValue) {
              setSelectedOption(newValue);
              setEmployeeId(newValue?.id);
            } else {
              const defaultEmpId = localStorage.getItem("userEmployeeId");
              if (defaultEmpId) {
                setSelectedOption(null);
                setEmployeeId(defaultEmpId);
              }
            }
          }}
          renderOption={(props, option) => (
            <li {...props} className="flex items-center gap-3 relative group p-3">
              <div className="relative w-10 h-10">
                {
                  option.image ? (
                    <img
                  src={option.image}
                  alt={option.name}
                  className="w-10 h-10 rounded-full object-cover transition-transform duration-300 transform group-hover:scale-150 z-50"
                />
                  ) : <RxAvatar />
                }
              </div>
              <span>{option?.name}</span>
            </li>
          )}

          renderInput={(params) => (
            <TextField {...params} label="Select Employee" variant="outlined" />
          )}
          className="mb-4 w-64 lg:w-[350px]"
        />
      </div>
      <div className="w-full h-[1px] bg-gray-300" />
    </div>
  );
};

export default SearchComponent;
