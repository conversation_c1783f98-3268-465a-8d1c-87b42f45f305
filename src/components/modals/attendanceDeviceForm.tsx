import { Device } from "@/interface/device";
import { Form, Input, Modal, Select } from "antd";
import React, { useEffect, useState } from "react";
import showNotification from "../common/notification";
import { getRequest, postRequest, putRequest } from "@/services/apiRequestHandlers";
import { API_ROUTE } from "@/constants/api-routes";


interface Props {
    isModalOpen: boolean;
    handleCancel: () => void;
    selectedDevice?: Device;
    fetchList: () => void;
}
const AttendanceDeviceForm = ({
    isModalOpen,
    handleCancel,
    selectedDevice,
    fetchList,
}: Props) => {
    const [form] = Form.useForm();
    const [comapnyList, setComapnyList] = useState<any[]>([]);

    useEffect(() => {
        getCompanyList();
        if (!selectedDevice) form.resetFields();
    }, []);

    useEffect(() => {
        if (isModalOpen) {
            if (selectedDevice) {
                fetchDeviceInfo();
            } else {
                form.resetFields();
            }
        }
    }, [isModalOpen, selectedDevice]);
    const fetchDeviceInfo = async () => {
        try {
            const response = await getRequest(
                `${API_ROUTE.DEVICE_LIST}/${selectedDevice?.id}`
            );
            form.setFieldsValue(response);
        } catch (error) {
            console.error("Error fetching shift list:", error);
        }
    };
    const getCompanyList = async () => {
        try {
            const response: any = await getRequest(
                `${API_ROUTE.COMPANY_LIST}?company_filter=true&query=`
            );
            setComapnyList(response);
        } catch (error) {
            console.error("Error fetching shift list:", error);
        }
    };

    const handleSubmit = async (values: any) => {
        if (selectedDevice) {
            // update device
            try {
                const resp: any = await putRequest(
                    `${API_ROUTE.UPDATE_DEVICE}/${selectedDevice.id}`,
                    values
                );
                if (resp) {
                    showNotification({
                        type: "success",
                        message: resp?.message
                            ? resp?.message
                            : "Successfuly updated the device !",
                    });
                    fetchList();
                    handleCancel();
                    form.resetFields();
                }
            } catch (err: any) {
                console.log(err);
                showNotification({
                    type: "error",
                    message: err?.message
                        ? err?.message
                        : "Update Unsuccessful !",
                });
            }
        } else {
            //create new device
            try {
                const resp: any = await postRequest(
                    `${API_ROUTE.CREATE_DEVICE}`,
                    values
                );
                if (resp) {
                    showNotification({
                        type: "success",
                        message: resp?.message
                            ? resp?.message
                            : "Successfuly created the device !",
                    });
                    fetchList();
                    handleCancel();
                    form.resetFields();
                }
            } catch (err: any) {
                console.log(err);
                showNotification({
                    type: "error",
                    message: err?.message
                        ? err?.message
                        : "Failed to create device !",
                });
            }
        }
    };
    return (
        <Modal
            centered
            title={selectedDevice ? "Update Device" : "Add New Device"}
            open={isModalOpen}
            onCancel={handleCancel}
            onOk={() => {
                form.validateFields()
                    .then((values) => handleSubmit(values))
                    .catch((err) => {
                        console.log("Validation failed:", err);
                    });
            }}
            okText={selectedDevice ? "Update" : "Add"}
            cancelText="Discard"
            className="text-xl"
        >
            <Form layout="vertical" form={form}>
                <Form.Item
                    label="Serial No"
                    name="serial_no"
                    rules={[
                        {
                            required: true,
                            message: "Please input the serial number !",
                        },
                    ]}
                >
                    <Input placeholder="ABCDEFw323" />
                </Form.Item>

                <Form.Item
                    label="Device IP Address"
                    name="device_ip"
                    rules={[
                        {
                            required: true,
                            message: "Please input the ip !",
                        },
                    ]}
                >
                    <Input placeholder="---.----.---.--" />
                </Form.Item>

                <Form.Item
                    label="Device Port Address"
                    name="device_port"
                    rules={[
                        {
                            required: true,
                            message: "Please input the port !",
                        },
                    ]}
                >
                    <Input placeholder="----" />
                </Form.Item>

                <Form.Item
                    label="Company"
                    name="company_id"
                    rules={[
                        {
                            required: true,
                            message: "Please choose a company !",
                        },
                    ]}
                >
                    <Select placeholder="Select Your Company">
                        {comapnyList?.map((company) => (
                            <Select.Option key={company.id} value={company.id}>
                                {company.name}
                            </Select.Option>
                        ))}
                    </Select>
                </Form.Item>

                <Form.Item
                    label="Device Location"
                    name="device_location"
                    rules={[
                        {
                            required: true,
                            message: "Please input the location !",
                        },
                    ]}
                >
                    <Input.TextArea placeholder="Baridhara R/A" />
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default AttendanceDeviceForm;
