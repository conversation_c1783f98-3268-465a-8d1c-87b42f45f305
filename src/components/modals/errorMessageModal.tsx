import { Button, Modal } from "antd";

interface Employee {
    employee_id: number;
    employee_name: string;
}
export interface ErrorDetails {
    status_code: number;
    message: string;
    employees: Employee[];
}

interface Props {
    errorDetails: ErrorDetails | undefined;
    isModalOpen: boolean;
    from?: string;
    handleCancel: (e: React.MouseEvent<HTMLButtonElement>) => void;
}
const ErrorMessageModal = ({
    errorDetails,
    from,
    isModalOpen,
    handleCancel,
}: Props) => {
    return (
        <Modal
            centered
            title="Errors"
            open={isModalOpen}
            onCancel={handleCancel}
            footer={[
                <Button key="cancel" onClick={handleCancel}>
                    Ok
                </Button>,
            ]}
        >
            <div className="my-6 flex flex-col gap-4">
                <p className="text-lg font-bold text-red-600">
                    {errorDetails?.message}
                </p>
                <div className="flex flex-col gap-2">
                    {errorDetails &&
                        errorDetails?.employees?.length > 0 &&
                        errorDetails?.employees?.map(
                            (item: Employee, i: number) => (
                                <div key={i} className="flex flex-row gap-2">
                                    <p>{i + 1}.</p>
                                    <p>{item?.employee_name}</p>
                                </div>
                            )
                        )}
                </div>
            </div>
        </Modal>
    );
};

export default ErrorMessageModal;
