import { LeaveType } from "@/interface/dashboard-models";
import { getRequest } from "@/services/apiRequestHandlers";
import { API_ROUTE } from "@/constants/api-routes";
import { useEmployeeContext } from "@/store/EmployeeContext";
import dayjs from "dayjs";
import React, { useEffect, useState } from "react";

interface Props {
  type: number;
  fromMonth: number;
  toMonth: number;
}
const LeavePolicy = ({ type, fromMonth, toMonth }: Props) => {
  const [leaveTypes, setLeaveTypes] = useState<any[]>([]);
  const [leaveInfo, setLeaveInfo] = useState<any>();
  const { employeeId } = useEmployeeContext();
  const currentYear = dayjs().year();


  const getEmployeeLeaveDetails = async (
    year: number,
    employeeId: string,
    fromMonth: number,
    toMonth: number
  ) => {
    const userId = localStorage.getItem("userEmployeeId");
    try {
      let response:any;
      if(employeeId==userId){
        response = await getRequest<{data: any}>(
          `${API_ROUTE.DASHBOARD_MY_WORKSPACE_CARD}?year=${year}&from_month=${fromMonth}&to_month=${toMonth}`
        );
      } else {
        response = await getRequest<{data: any}>(
          `${API_ROUTE.DASHBOARD_MY_WORKSPACE_CARD}?year=${year}&employee_id=${employeeId}&from_month=${fromMonth}&to_month=${toMonth}`
        );
      }
      if (response && response.data) {
        setLeaveInfo(response.data);
      }
    } catch (error) {
      console.error("Error fetching leave policy data:", error);
    }
  };
  useEffect(() => {
    const takenLeaves = leaveInfo?.leaves.map((item: any) => ({
      name: item.name,
      count: item.taken,
    }));
    setLeaveTypes(takenLeaves);
  }, [leaveInfo]);

  useEffect(() => {
    getEmployeeLeaveDetails(currentYear, employeeId, fromMonth, toMonth);
  }, [currentYear, employeeId, fromMonth, toMonth]);

  return (
    <div className="p-4 bg-gray-100 w-full h-full text-sm shadow-md">
      {/* <h3 className="font-bold mb-2 text-base">Leave Policy</h3>
        <p>Casual Leave = 10 days (per year)</p>
        <p>Sick Leave = 14 days (per year)</p>
        <p>Leave applies on pro-rata basis for confirmed employees.</p> */}
      <div className="flex flex-col gap-4">
        <div className="flex flex-col gap-1">
          <h2 className=" text-base font-semibold">Leave Status</h2>
          <div className="flex flex-row gap-2 items-center">
            <div className="w-3 h-3 bg-[#f94144]" />
            <p>{`Absent : ${leaveInfo?.absent || 0}`}</p>
          </div>
          {leaveTypes?.map((leave) => (
            <div className="flex flex-row gap-2 items-center">
              <div className={`w-3 h-3 ${getStatusColor(leave?.name)}`} />
              <p className="whitespace-nowrap">{`${leave?.name} : ${leave?.count ? leave?.count : 0}`}</p>
            </div>
          ))}
          <div className="flex flex-row gap-2 items-center">
            <div className="w-3 h-3 bg-[#87abca]" />
            <p>Public Off day</p>
          </div>
        </div>
        <div className="flex flex-col gap-1">
          <h2 className=" text-base font-semibold">Attendance Status</h2>
          <div className="flex flex-row gap-2 items-center">
            <div className="w-2 h-2 rounded-full bg-[#e50000]" />
            <p>{`Late Status : ${leaveInfo?.late_in || 0}`}</p>
          </div>
          <div className="flex flex-row gap-2 items-center">
            <div className="w-2 h-2 rounded-full bg-[#004cff]" />
            <p>{`Early Exit : ${leaveInfo?.early_exit || 0}`}</p>
          </div>
          <div className="flex flex-row gap-2 items-center">
            <div className="w-2 h-2 rounded-full bg-[#008121]" />
            <p>{`WFH : ${leaveInfo?.wfh_request || 0}`}</p>
          </div>
          <div className="flex flex-row gap-2 items-center">
            <div className="w-2 h-2 rounded-full bg-[#A020F0]" />
            <p>{`Adj : ${leaveInfo?.adj_request || 0}`}</p>
          </div>
          <div className="flex flex-row gap-2 items-center">
            <div className="w-2 h-2 rounded-full bg-cyan-200" />
            <p>{`Below Threshold Hours : ${leaveInfo?.below_threshold_hours_count || 0}`}</p>
          </div>
          {/* <div className="flex flex-row gap-2 items-center">
              <div className="w-2 h-2 rounded-full bg-[#760188]" />
              <p>Off Day Duty</p>
            </div>
            <div className="flex flex-row gap-2 items-center">
              <div className="w-2 h-2 rounded-full bg-[#008121]" />
              <p>Over Time Duty</p>
            </div> */}
        </div>
      </div>
    </div>
  );
};

export default LeavePolicy;

const getStatusColor = (status: string): string => {
  switch (status) {
    case "Casual Leave(CL)":
      return "bg-[#f8961e]";
    case 'Sick Leave (SL)':
      return "bg-[#43aa8b]";
    case 'Earned Leave':
      return "bg-[#90be6d]";
    case 'Compensatory':
      return "bg-[#8E1616]";
      case "Maternity":
      return "bg-[#f472b6]";
      case "Paternity":
      return "bg-[#f472b6]";
    default:
      return "bg-white";
  }
};