import React, { createContext, useContext, useState } from 'react';


const EmployeeContext = createContext<any>(null);

export const useEmployeeContext = () => useContext(EmployeeContext);

export const EmployeeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [employeeId, setEmployeeId] = useState<string | null>();

  const updateEmployeeId = (newEmployeeId: string) => {
    setEmployeeId(newEmployeeId);
  };

  const value = { employeeId, setEmployeeId: updateEmployeeId };

  return <EmployeeContext.Provider value={value}>{children}</EmployeeContext.Provider>;
};
