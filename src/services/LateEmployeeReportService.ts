import { FAILED, SUCCESS } from "@/utils/common";
import {API} from "./apiService";
import { errorToast } from "@/utils/helper";

export const LateEmployeeReportService = {
  async lateReport(body: any) {
    try {
      const { data } = await API.post(`late/attendances`, body);

      return {
        status: SUCCESS,
        data: data,
      };
    } catch (e: any) {
      if (e.response) {
        const responseJson = JSON.parse(e.response.request.responseText);
        const errorDetail = responseJson.detail;
        errorToast(errorDetail);
      } else {
        console.error("Error:", e.message);
      }
      return {
        status: FAILED,
        message: "Something wrong!!",
        error: e,
      };
    }
  },
};
