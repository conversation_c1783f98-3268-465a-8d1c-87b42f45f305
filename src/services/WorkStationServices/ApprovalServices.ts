import useSWR from "swr";
import { APIForWorkspace } from "../apiService";
import {  ApprovalRequestSummary } from "@/types/ApprovalTypes";


// Fetcher function using Axios
const fetcher = async <T>(url: string): Promise<{ data: T }> => { 
  const response = await APIForWorkspace.get<{ data: T }>(url);
  return response.data; 
};

export const approvalRequestKey = "dashboard/card/pending-approvals";

// Function to fetch Attendance Summary
export const getApprovalRequestSummary = () => { 
  return useSWR<{data:ApprovalRequestSummary[]}>(approvalRequestKey, fetcher);
};



export const getPendingApprovalList = async(
  tab: string,
  state: string,
  searchQuery: string,
  last_id?: number
) => {
  let url = "";
  switch (tab) {
    case "Leave":
      url = `dashboard/pending-leave-approval-list?leave_state=${state}&search_query=${searchQuery}&last_id=${last_id || ""}`;
      break;
    case "Attendance":
      url = `dashboard/pending-attendance-approval-list?attendance_state=${state}&search_query=${searchQuery}&last_id=${last_id || ""}`;
      break;
    case "Self Services":
      url = `dashboard/pending-self-service-approval-list?self_service_state=${state}&search_query=${searchQuery}&last_id=${last_id || ""}`;
      break;
    default:
      throw new Error("Invalid tab");
  }
  
  const response = await APIForWorkspace.get(url);
  return response.data;
};

export const approveApplications = async (title: string, employeeId: number) => {
  try {
    let url = '';
    switch (title) {
      case 'Leave':
        url = `leave/approve?leave_id=${employeeId}`;
        break;
      case 'Attendance':
        url = `dashboard/attendance-adjustment-approve?adjustment_id=${employeeId}`;
        break;
      case 'Self Service':
        url = `/self-service/update`;
        break;
      default:
        throw new Error('Invalid title');
    }
    const response = await APIForWorkspace.put(url);
    return response.data;
  } catch (error) {
    throw error;
  }
};


export const rejectApplications = async (title: string, employeeId: number,reason:string) => {
  try {
    let url = '';
    switch (title) {
      case 'Leave':
        url = `leave/refuse?leave_id=${employeeId}`;
        break;
      case 'Attendance':
        url = `dashboard/attendance-adjustment-reject?adjustment_id=${employeeId}`;
        break;
      case 'Self Service':
        url = `/self-service/update`;
        break;
      default:
        throw new Error('Invalid title');
    }
    const payload = {
      refuse_reason: reason,
    };
    const response = await APIForWorkspace.put(url,payload);
    return response.data;
  } catch (error) {
    console.error('Error updating status:', error);
    throw error;
  }
};

export const getLeaveApplicationHistory = async (emp_id: string) => {
  try {
    const response = await APIForWorkspace.get(`leave/approved_leave_history?employee_id=${emp_id}`);
    return response.data; // assuming response returns { data: [...] }
  } catch (error) {
    console.error("Failed to fetch leave application history:", error);
    throw error;
  }
};


