import { TOKEN_KEY } from "@/utils/common";
import axios from "axios";

const baseUrl = process.env.NEXT_PUBLIC_API_URL_FOR_REPORT;
const baseUrlForWorkspace = process.env.NEXT_PUBLIC_API_URL_FOR_WORKSPACE;


const API = axios.create({
  baseURL: baseUrl + "/api/",
});

const APIForWorkspace = axios.create({
  baseURL: baseUrlForWorkspace + "/api/v1/",
});

// Attach token interceptor for both instances
const attachAuthInterceptor = (instance: any) => {
  instance.interceptors.request.use((config: any) => {
    const tokenFromLocalStorage = localStorage.getItem(TOKEN_KEY);

    if (tokenFromLocalStorage) {
      config.headers.Authorization = `Bearer ${tokenFromLocalStorage}`;
    }
    return config;
  });
};

attachAuthInterceptor(API);
attachAuthInterceptor(APIForWorkspace);

// ✅ Export both instances as named exports
export { API, APIForWorkspace };
