import React, { useEffect, useState, useCallback } from "react";
import {
  Avatar,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Tabs,
  Tab,
  Box,
  CircularProgress,
  Typography,
  IconButton,
  TextField,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from "@mui/material";
import { EmployeeTableData } from "@/types/ApprovalTypes";
import SearchIcon from "@mui/icons-material/Search";
import { ClearIcon } from "@mui/x-date-pickers";
import {
  approvalRequestKey,
  approveApplications,
  getPendingApprovalList,
  rejectApplications,
} from "@/services/WorkStationServices/ApprovalServices";
import toastNotification from "@/utils/notificationToster";
import {
  RiCheckboxBlankCircleLine,
  RiCheckboxCircleFill,
} from "react-icons/ri";
import index from "@/pages/report";
import EmployeeDetailsModal from "../common/EmployeeDetailsModal";
import { mutate } from "swr";
import { capitalizeFirstLetter } from "@/utils/helper";

type approvalRequestType = "Leave" | "Attendance" | "Self Service";

type ApprovalRequestTableProps = {
  approvalRequestName: approvalRequestType;
};

const EmployeeTable: React.FC<ApprovalRequestTableProps> = ({
  approvalRequestName,
}) => {
  const tabNames: Record<number, string> = {
    0: "Leave",
    1: "Attendance",
    2: "Self Service",
  };

  const defaultCategories = ["Leave", "Attendance", "Self Services"];
  const initialTabIndex = approvalRequestName
    ? defaultCategories.indexOf(approvalRequestName)
    : 0;

  const [activeTab, setActiveTab] = useState(initialTabIndex);
  const [employeeData, setEmployeeData] = useState<EmployeeTableData[]>([]);
  const [selectedEmployee, setSelectedEmployee] =
    useState<EmployeeTableData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [lastId, setLastId] = useState<number | undefined>(undefined);
  const [hasMore, setHasMore] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [isFetchingMore, setIsFetchingMore] = useState(false); // For infinite scroll loading
  const [tempSearchQuery, setTempSearchQuery] = useState("");
  const [openRejectModal, setOpenRejectModal] = useState(false);
  const [rejectReason, setRejectReason] = useState("");
  const [selectedEmployeeId, setSelectedEmployeeId] = useState<number | null>(
    null
  );
  const [selectedTabName, setSelectedTabName] = useState<string>("");
  const [reasonError, setReasonError] = useState(false);

  // Fetch data based on active tab
  const fetchData = async (isNewFetch: boolean = false) => {
    // if (isNewFetch && searchQuery.trim() === "") return;
    if (isNewFetch) {
      setLoading(true);
      setEmployeeData([]); // Reset employeeData for new fetch
      setLastId(undefined); // Reset lastId for new fetch
      setHasMore(true);
    } else {
      setIsFetchingMore(true);
    }
    setError("");

    try {
      const data = await getPendingApprovalList(
        defaultCategories[activeTab],
        "requested",
        searchQuery,
        isNewFetch ? undefined : lastId // Pass undefined for initial fetch, lastId for subsequent fetches
      );
      if (data) {
        // If no more data is available, set hasMore to false
        if (data.data.length === 0) {
          setHasMore(false);
        } else {
          // Append new data to the existing employeeData
          setEmployeeData((prev) =>
            isNewFetch ? data.data : [...prev, ...data.data]
          );
          // Update lastId to the ID of the last row in the fetched data
          const lastFetchedId = data.data[data.data.length - 1]?.last_id;
          if (lastFetchedId) {
            setLastId(lastFetchedId);
          }
        }
      }
    } catch (err) {
      setError("Failed to fetch data");
    } finally {
      if (isNewFetch) {
        setLoading(false);
      } else {
        setIsFetchingMore(false);
      }
    }
  };

  // Fetch new data when activeTab or searchQuery changes
  useEffect(() => {
    setHasMore(true);
    fetchData(true);
  }, [activeTab, searchQuery]);

  // Update activeTab when approvalRequestName changes
  useEffect(() => {
    if (approvalRequestName) {
      const normalizedCategory =
        approvalRequestName === "Self Service"
          ? "Self Services"
          : approvalRequestName;
      const index = defaultCategories.indexOf(normalizedCategory);
      if (index !== -1) setActiveTab(index);
    }
  }, [approvalRequestName]);

  // Handle scroll for infinite loading
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    const isNearBottom = scrollHeight - (scrollTop + clientHeight) < 50;

    if (isNearBottom && hasMore && !loading && !isFetchingMore) {
      fetchData(); // Fetch more data
    }
  };

  const tableHeaders = [
    ["Name", "LT", "SD", "ED", "Status", "LM", "HOD", "HR", "Actions"], // Leave
    ["Name", "RT", "RD", "Status", "LM", "HOD", "HR", "Actions"], // Attendance
    [
      "Name",
      "Designation",
      "Department",
      "ST",
      "DH",
      "HrH",
      "HR",
      // "Status",
      "Actions",
    ], // Self Service
  ];

  const currentHeaders = tableHeaders[activeTab];

  const handleApprove = async (employeeId: number, tabName: string) => {
    setLastId(undefined);
    try {
      const response = await approveApplications(tabName, employeeId);
      toastNotification("success", response.message);
      // Refetch data after approval
      fetchData(true);
      await mutate(approvalRequestKey);
    } catch (error) {
      const axiosError = error as any;
      console.log(axiosError.response.data.message);

      toastNotification(
        "error",
        axiosError.response?.data?.detail || axiosError?.response?.data?.message
      );
    }
  };

  // const handleReject = async (employeeId: number, tabName: string) => {
  //   setLastId(undefined);
  //   try {
  //     const response = await rejectApplications(tabName, employeeId);
  //     toastNotification("success", response.message);
  //     // Refetch data after rejection
  //     fetchData(true);
  //   } catch (error) {
  //     const axiosError = error as any;
  //     toastNotification(
  //       "error",
  //       axiosError.response?.data?.detail || "Something went wrong"
  //     );
  //   }
  // };

  const handleReject = async (
    employeeId: number,
    tabName: string,
    reason: string
  ) => {
    setLastId(undefined);
    try {
      const response = await rejectApplications(tabName, employeeId, reason); // ensure your API supports the `reason` param
      toastNotification("success", response.message);
      fetchData(true);
      setSelectedEmployee(null);
      setOpenRejectModal(false);
      await mutate(approvalRequestKey);
    } catch (error) {
      setSelectedEmployee(null);
      const axiosError = error as any;
      if (activeTab === 1) {
        toastNotification("error", axiosError.response?.data?.detail);
      }
      toastNotification("error", axiosError.response?.data?.detail);
    }
  };

  const handleSearch = () => {
    if (!tempSearchQuery) {
      return toastNotification("warning", "Input value for search !");
    }

    // Force a refresh even if the search query is the same
    // by temporarily setting searchQuery to a different value and then to the actual value
    if (tempSearchQuery === searchQuery) {
      // Use a timestamp to ensure the value is different
      setSearchQuery(tempSearchQuery + "_" + Date.now());
      // Use setTimeout to ensure state updates in sequence
      setTimeout(() => {
        setSearchQuery(tempSearchQuery);
      }, 10);
    } else {
      setSearchQuery(tempSearchQuery);
    }

    setEmployeeData([]);
    setLastId(undefined);
    setHasMore(true); // Reset hasMore when performing a new search
  };

  // Handle clear search
  const handleClearSearch = () => {
    setTempSearchQuery("");
    setSearchQuery("");
    setLastId(undefined);
    setHasMore(true); // Reset hasMore when clearing the search
  };
  return (
    <Paper
      sx={{
        width: "100%",
        position: "relative",
        overflow: "hidden",
        borderRadius: "8px",
        boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
      }}
    >
      {/* Responsive Tabs */}
      <div className="w-full pr-5 overflow-x-auto">
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Tabs
            value={activeTab}
            onChange={(e: any, newValue: any) => setActiveTab(newValue)}
            variant="scrollable"
            scrollButtons="auto"
            sx={{
              borderBottom: "2px solid #ddd",
              "& .MuiTabs-flexContainer": { justifyContent: "flex-start" },
              "& .MuiTab-root": {
                textTransform: "none",
                fontWeight: 600,
                fontSize: "1rem",
                color: "#333",
                transition: "0.3s",
                whiteSpace: "nowrap",
                "&:hover": {
                  background: "linear-gradient(45deg, #FFA726, #FF7043)",
                  color: "white",
                },
                "&.Mui-selected": { color: "#FF5722" },
              },
              "& .MuiTabs-indicator": { backgroundColor: "#FF5722" },
            }}
          >
            {defaultCategories.map((category, index) => (
              <Tab key={index} label={category} />
            ))}
          </Tabs>

          <Box
            display="flex"
            alignItems="center"
            border="1px solid #ccc"
            borderRadius="5px"
            overflow="hidden"
            sx={{
              "&:hover": {
                borderColor: "#1976d2",
              },
            }}
          >
            <TextField
              size="small"
              variant="outlined"
              placeholder="Search..."
              value={tempSearchQuery}
              onChange={(e: any) => setTempSearchQuery(e.target.value)}
              onKeyDown={(e: any) => e.key === "Enter" && handleSearch()}
              sx={{
                flex: 1,
                "& fieldset": { border: "none" },
              }}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    {tempSearchQuery && (
                      <IconButton onClick={handleClearSearch} size="small">
                        <ClearIcon fontSize="small" />
                      </IconButton>
                    )}
                  </InputAdornment>
                ),
              }}
            />
            <IconButton
              onClick={handleSearch}
              color="primary"
              sx={{
                transition: "transform 0.3s ease",
                "&:hover": {
                  transform: "scale(1.4)",
                  backgroundColor: "#1976d2",
                  color: "#FFFFFF",
                },
              }}
            >
              <SearchIcon />
            </IconButton>
          </Box>
        </Box>
      </div>

      {/* Show loading spinner */}
      {loading && (
        <Box display="flex" justifyContent="center" alignItems="center" py={3}>
          <CircularProgress />
        </Box>
      )}

      {/* Show error message if any */}
      {error && (
        <Box display="flex" justifyContent="center" alignItems="center" py={3}>
          <Typography color="error">{error}</Typography>
        </Box>
      )}

      {/* Scrollable Table */}
      {!loading && !error && (
        <TableContainer
          sx={{
            height: 380,
            overflowY: "auto",
            "&::-webkit-scrollbar": {
              width: "5px",
              height: "5px",
            },
            "&::-webkit-scrollbar-thumb": {
              backgroundColor: "#7ae075",
              borderRadius: "10px",
            },
            "&::-webkit-scrollbar-track": {
              backgroundColor: "#f3f4f6",
            },
          }}
          onScroll={handleScroll}
        >
          <Table stickyHeader className="table-scroll-container">
            <TableHead>
              <TableRow
                sx={{
                  backgroundColor: "#f3f4f6",
                }}
              >
                {currentHeaders.map((header, index) => (
                  <TableCell
                    key={header}
                    sx={{
                      p: 1,
                      paddingRight: header === "Actions" ? 4 : 0,
                      paddingLeft: header === "Name" ? 4 : 0,
                      textAlign: header==="Name" ? "start" : header==="Actions" ? "end" : "center",
                      fontWeight: "bold",
                      color: "#d1d5db",
                      border: "none",
                    }}
                  >
                    {header}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {employeeData.length > 0 ? (
                Array.from(
                  new Map(employeeData.map((item) => [item.id, item])).values()
                ).map((employee, rowIndex) => (
                  <TableRow
                    key={employee.id}
                    sx={{
                      backgroundColor:
                        rowIndex % 2 === 0 ? "#f9fafb" : "#f3f4f6",
                      transition: "background-color 0.5s ease",
                    }}
                  >
                    <TableCell
                      sx={{
                        p: 1,
                        textAlign: "start",
                        border: "none",
                        fontSize: "12px",
                      }}
                    >
                      <Box
                        display="flex"
                        alignItems="center"
                        justifyContent="between"
                        gap={1}
                      >
                        <Avatar src={employee?.image} alt={employee?.name} />
                        <Typography
                          variant="body2"
                          fontSize="13px"
                          fontWeight="medium"
                          sx={{
                            whiteSpace: "nowrap",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            maxWidth: "140px", // adjust as needed
                          }}
                        >
                          {employee.name}
                        </Typography>
                      </Box>
                    </TableCell>

                    {/* Render different table data based on activeTab */}
                    {activeTab === 0 && (
                      <>
                        <TableCell
                          sx={{
                            p: 1,
                            textAlign: "center",
                            border: "none",
                            fontSize: "12px",
                            whiteSpace: "nowrap",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            maxWidth: "120px", // optional, tune per layout
                          }}
                        >
                          {employee?.leave_type}
                        </TableCell>
                        <TableCell
                          sx={{
                            p: 1,
                            textAlign: "center",
                            border: "none",
                            fontSize: "12px",
                            whiteSpace: "nowrap",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            maxWidth: "120px", // optional, tune per layout
                          }}
                        >
                          {employee?.start_date}
                        </TableCell>
                        <TableCell
                          sx={{
                            p: 1,
                            textAlign: "center",
                            border: "none",
                            fontSize: "12px",
                            whiteSpace: "nowrap",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            maxWidth: "120px", // optional, tune per layout
                          }}
                        >
                          {employee?.end_date}
                        </TableCell>
                        <TableCell
                          sx={{
                            p: 1,
                            textAlign: "center",
                            border: "none",
                            fontSize: "12px",
                            whiteSpace: "nowrap",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            maxWidth: "120px", // optional, tune per layout
                          }}
                        >
                          <div
                            className={`py-1 text-center rounded-xl ${
                              employee?.status === "To Approve"
                                ? "bg-yellow-500"
                                : employee?.status === "Approved"
                                ? "bg-green-100 text-green-800"
                                : ""
                            }`}
                          >
                            {employee?.status}
                          </div>
                        </TableCell>
                        <TableCell
                          sx={{
                            p: 1,
                            border: "none",
                            whiteSpace: "nowrap",
                          }}
                        >
                          <div className="flex justify-center">
                            {employee?.line_manager_status ? (
                              <RiCheckboxCircleFill className="text-green-600 text-xl" />
                            ) : (
                              <RiCheckboxBlankCircleLine className=" text-xl" />
                            )}
                          </div>
                        </TableCell>
                        <TableCell sx={{ p: 1, border: "none" }}>
                          <div className="flex justify-center">
                            {employee?.dept_manager_status ? (
                              <RiCheckboxCircleFill className="text-green-600 text-xl" />
                            ) : (
                              <RiCheckboxBlankCircleLine className=" text-xl" />
                            )}
                          </div>
                        </TableCell>
                        <TableCell sx={{ p: 1, border: "none" }}>
                          <div className="flex justify-center">
                            {employee?.hr_status ? (
                              <RiCheckboxCircleFill className="text-green-600 text-xl" />
                            ) : (
                              <RiCheckboxBlankCircleLine className=" text-xl" />
                            )}
                          </div>
                        </TableCell>
                      </>
                    )}

                    {activeTab === 1 && (
                      <>
                        <TableCell
                          sx={{
                            p: 1,
                            textAlign: "center",
                            border: "none",
                            fontSize: "12px",
                            whiteSpace: "nowrap",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            maxWidth: "120px", // optional, tune per layout
                          }}
                        >
                          {employee?.request_type}
                        </TableCell>
                        <TableCell
                          sx={{
                            p: 1,
                            textAlign: "center",
                            border: "none",
                            fontSize: "12px",
                            whiteSpace: "nowrap",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            maxWidth: "120px", // optional, tune per layout
                          }}
                        >
                          {employee?.requested_date}
                        </TableCell>
                        <TableCell
                          sx={{
                            p: 1,
                            textAlign: "center",
                            border: "none",
                            fontSize: "12px",
                            whiteSpace: "nowrap",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            maxWidth: "120px", // optional, tune per layout
                          }}
                        >
                          <div
                            className={`py-1 text-center rounded-xl ${
                              employee?.state === "requested"
                                ? "bg-yellow-500"
                                : employee?.state === "Approved"
                                ? "bg-green-100 text-green-800"
                                : ""
                            }`}
                          >
                            {capitalizeFirstLetter(employee?.state)}
                          </div>
                        </TableCell>
                        <TableCell
                          sx={{
                            p: 1,
                            textAlign: "center",
                            border: "none",
                            fontSize: "12px",
                            whiteSpace: "nowrap",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            maxWidth: "120px", // optional, tune per layout
                          }}
                        >
                          <div className="flex justify-center">
                            {employee?.line_manager_status ? (
                              <RiCheckboxCircleFill className="text-green-600 text-xl" />
                            ) : (
                              <RiCheckboxBlankCircleLine className=" text-xl" />
                            )}
                          </div>
                        </TableCell>
                        <TableCell
                          sx={{
                            p: 1,
                            textAlign: "start",
                            border: "none",
                            fontSize: "12px",
                            whiteSpace: "nowrap",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            maxWidth: "120px", // optional, tune per layout
                          }}
                        >
                          <div className="flex justify-center">
                            {employee?.dept_manager_status ? (
                              <RiCheckboxCircleFill className="text-green-600 text-xl" />
                            ) : (
                              <RiCheckboxBlankCircleLine className=" text-xl" />
                            )}
                          </div>
                        </TableCell>
                        <TableCell
                          sx={{ p: 1, textAlign: "start", border: "none" }}
                        >
                          <div className="flex justify-center">
                            {employee?.hr_status ? (
                              <RiCheckboxCircleFill className="text-green-600 text-xl" />
                            ) : (
                              <RiCheckboxBlankCircleLine className=" text-xl" />
                            )}
                          </div>
                        </TableCell>
                      </>
                    )}

                    {activeTab === 2 && (
                      <>
                        <TableCell
                          sx={{
                            p: 1,
                            textAlign: "center",
                            border: "none",
                            fontSize: "12px",
                            whiteSpace: "nowrap",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            maxWidth: "120px", // optional, tune per layout
                          }}
                        >
                          {employee?.designation}
                        </TableCell>
                        <TableCell
                          sx={{
                            p: 1,
                            textAlign: "center",
                            border: "none",
                            fontSize: "12px",
                            whiteSpace: "nowrap",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            maxWidth: "120px", // optional, tune per layout
                          }}
                        >
                          {employee?.department}
                        </TableCell>
                        <TableCell
                          sx={{
                            p: 1,
                            textAlign: "center",
                            border: "none",
                            fontSize: "12px",
                            whiteSpace: "nowrap",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            maxWidth: "120px", // optional, tune per layout
                          }}
                        >
                          {employee?.service_type}
                        </TableCell>
                        <TableCell
                          sx={{
                            p: 1,
                            textAlign: "center",
                            border: "none",
                            fontSize: "12px",
                            whiteSpace: "nowrap",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            maxWidth: "120px", // optional, tune per layout
                          }}
                        >
                          <div className="flex justify-center ">
                            {employee?.dept_head_approval_status ? (
                              <RiCheckboxCircleFill className="text-green-600 text-xl" />
                            ) : (
                              <RiCheckboxBlankCircleLine className=" text-xl" />
                            )}
                          </div>
                        </TableCell>
                        <TableCell
                          sx={{ p: 1, textAlign: "start", border: "none" }}
                        >
                          <div className="flex justify-center ">
                            {employee?.hr_head_approval_status ? (
                              <RiCheckboxCircleFill className="text-green-600 text-xl" />
                            ) : (
                              <RiCheckboxBlankCircleLine className=" text-xl" />
                            )}
                          </div>
                        </TableCell>
                        <TableCell
                          sx={{ p: 1, textAlign: "start", border: "none" }}
                        >
                          <div className="flex justify-center">
                            {employee?.hr_approval_status ? (
                              <RiCheckboxCircleFill className="text-green-600 text-xl" />
                            ) : (
                              <RiCheckboxBlankCircleLine className=" text-xl" />
                            )}
                          </div>
                        </TableCell>
                      </>
                    )}

                    {/* Actions for all tabs */}
                    <TableCell
                      sx={{
                        p: 1,
                        border: "none",
                        display:"flex",
                        justifyContent:"end"
                      }}
                    >
                      <Box
                        display="flex"
                        justifyContent="start"
                        alignItems="center"
                        height="100%"
                        gap={1}
                      >
                        <Button
                          variant="contained"
                          disabled={activeTab === 2}
                          color="primary"
                          size="small"
                          onClick={() => setSelectedEmployee(employee as any)}
                        >
                          {/* <RemoveRedEyeIcon/> */}
                          Details
                        </Button>
                        <Button
                          variant="contained"
                          color="success"
                          size="small"
                          onClick={() =>
                            handleApprove(employee.id, tabNames[activeTab])
                          }
                          disabled={activeTab === 2 || !employee?.show_button}
                        >
                          Approve
                        </Button>
                        <Button
                          variant="contained"
                          color="error"
                          size="small"
                          onClick={() => {
                            setSelectedEmployeeId(employee.id);
                            setSelectedTabName(tabNames[activeTab]);
                            setOpenRejectModal(true);
                          }}
                          disabled={activeTab === 2 || !employee?.show_button}
                        >
                          Reject
                        </Button>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={currentHeaders.length}
                    sx={{ textAlign: "center", py: 3 }}
                  >
                    No records found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Modal for Employee Details */}
      <EmployeeDetailsModal
        setSelectedEmployee={setSelectedEmployee}
        setSelectedEmployeeId={setSelectedEmployeeId}
        setSelectedTabName={setSelectedTabName}
        setOpenRejectModal={setOpenRejectModal}
        selectedEmployee={selectedEmployee}
        activeTab={activeTab}
        openRejectModal={openRejectModal}
        handleApprove={handleApprove}
      />
      {/* Reject reason modal */}
      <Dialog
        className="!absolute !top-[-50px]  xl:[top:-100px!important] "
        open={openRejectModal}
        onClose={() => setOpenRejectModal(false)}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle className="text-md font-semibold">
          Refuse Reason
        </DialogTitle>
        <DialogContent className="flex flex-col gap-4">
          <TextField
            multiline
            required
            minRows={3}
            value={rejectReason}
            error={reasonError}
            placeholder="Reason for rejection"
            helperText={
              reasonError
                ? "Please enter a reason."
                : `${rejectReason.length}/300 characters`
            }
            onChange={(e) => {
              const text = e.target.value;
              if (text.length <= 300) {
                setRejectReason(text);
                if (text.trim() !== "") setReasonError(false); // remove error when typing
              }
            }}
          />
        </DialogContent>
        <DialogActions className="px-6 pb-4">
          <Button
            onClick={() => {
              setOpenRejectModal(false);
              setRejectReason("");
            }}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            color="error"
            onClick={async () => {
              if (!rejectReason.trim()) {
                setReasonError(true); // show error below field
                return;
              }
              if (selectedEmployeeId && selectedTabName) {
                await handleReject(
                  selectedEmployeeId,
                  selectedTabName,
                  rejectReason
                );
              }
              setRejectReason("");
              setReasonError(false);
              setOpenRejectModal(false);
            }}
          >
            Submit
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default EmployeeTable;
