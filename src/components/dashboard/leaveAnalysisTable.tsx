import React, { useEffect, useState } from "react";
import {
  Avatar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Box,
  CircularProgress,
  Typography,
  useMediaQuery,
  Checkbox,
} from "@mui/material";
import { FaTableCellsLarge } from "react-icons/fa6";
import { FaTableCells } from "react-icons/fa6";
import dayjs from "dayjs";
import { LeaveHistory } from "@/interface/dashboard-models";
import { useEmployeeContext } from "@/store/EmployeeContext";
import { formatISODate } from "@/utils/helper";
import { getRequest } from "@/services/apiRequestHandlers";
import { API_ROUTE } from "@/constants/api-routes";
import {
  RiCheckboxBlankCircleLine,
  RiCheckboxCircleFill,
} from "react-icons/ri";

const LeaveAnalysisTable = () => {
  const isSmallScreen = useMediaQuery("(max-width: 600px)");
  const [employeeData, setEmployeeData] = useState<LeaveHistory[]>([]);
  const [loading, setLoading] = useState(false);
  const { employeeId } = useEmployeeContext();
  // Fetch employee data
  const getEmployeeLeaveDetails = async (id: string, year: number) => {
    if (id) {
      try {
        setLoading(true);
        const data = await getRequest<LeaveHistory[]>(`${API_ROUTE.LEAVE_LEAVES}?employee_id=${id}&year=${year}`);
        if (data) {
          setEmployeeData(data);
        }
      } catch (error) {
        console.error("Error fetching leave details:", error);
      } finally {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    const currentYear = dayjs().year();
    if (employeeId) getEmployeeLeaveDetails(employeeId, currentYear);
  }, [employeeId]);

  const tableHeaders = [
    ["Type", "Date", "Leave Duration", "LM", "DM", "HR", "Status"],
  ];

  const getModifiedStatus = (status: string) => {
    switch (status) {
      case "validate":
        return "Approved";
      case "draft":
        return "To Submit";
      case "confirm":
        return "To Approve";
      case "refuse":
        return "Rejected";
      default:
        return "";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "validate":
        return "#3c8038";
      case "draft":
        return "#86cbd6";
      case "confirm":
        return "#075985";
      case "refuse":
        return "#f94144";
      default:
        return "";
    }
  };
  return (
    <Box
      sx={{
        width: "100%",
        overflowX: "auto",
        bgcolor: "#ffffff",
        color: "#063c7a",
        p: 2,
        boxShadow: 1,
        border: "none",
        outline: "none",
      }}
    >
      <Typography className="text-lg !font-bold text-center pb-5 flex justify-center gap-2">
        {/* <span className="pt-1 text-xl"><FaTableCells color="#ea580c"/></span> */}
        <span className="text-md text-orange-600">Yearly Leave Table</span>

      </Typography>

      {loading && (
        <Box display="flex" justifyContent="center" alignItems="center" py={3}>
          <CircularProgress />
        </Box>
      )}
      {!loading && (
        <TableContainer
          sx={{
            maxHeight: 400,
            overflowY: "auto",
            "&::-webkit-scrollbar": {
              width: "5px",
              height: "5px",
            },
            "&::-webkit-scrollbar-thumb": {
              backgroundColor: "#7ae075",
              borderRadius: "10px",
            },
            "&::-webkit-scrollbar-track": {
              backgroundColor: "#f3f4f6",
            },
          }}
        >
          <Table stickyHeader className="table-scroll-container">
            <TableHead>
              <TableRow sx={{ backgroundColor: "#f5f5f5" }}>
                {tableHeaders[0]?.map((header) => (
                  <TableCell
                    key={header}
                    sx={{
                      p: 1,
                      textAlign: "center",
                      fontWeight: "bold",
                      border: "none",
                      color: "#063c7a",
                      whiteSpace: "nowrap",
                      backgroundColor: "#f3f4f6",
                    }}
                  >
                    {header}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {employeeData?.length > 0 ? (
                employeeData?.map((leave: LeaveHistory, rowIndex) => (
                  <TableRow
                    key={leave?.id}
                    sx={{
                      "& > td": {
                        p: "11px",
                        borderBottom: "1px solid #e0e0e0",
                      },
                      transition: "background-color 0.5s ease",
                    }}
                  >
                    <TableCell
                      sx={{
                        // p: 1,
                        textAlign: "start",
                        border: "none",
                        color:getStatusColor(leave?.status),
                        whiteSpace: "nowrap",
                      }}
                    >
                      {leave?.leave_type}
                    </TableCell>
                    <TableCell
                      sx={{
                        // p: 1,
                        textAlign: "center",
                        border: "none",
                        color:getStatusColor(leave?.status),
                        whiteSpace: "nowrap",
                      }}
                    >
                      {formatISODate(leave?.start_date)}
                    </TableCell>
                    <TableCell
                      sx={{
                        // p: 1,
                        textAlign: "center",
                        border: "none",
                        color:getStatusColor(leave?.status),
                        whiteSpace: "nowrap",
                      }}
                    >
                      {leave?.duration}
                    </TableCell>
                    <TableCell
                      sx={{
                        // p: 1,
                        border: "none",
                        color: "#063c7a",
                        whiteSpace: "nowrap",
                      }}
                    >
                      <div className="flex justify-center">
                        {leave?.line_manager_approve ? (
                          <RiCheckboxCircleFill className="text-green-600 text-xl" />
                        ) : (
                          <RiCheckboxBlankCircleLine className=" text-xl" />
                        )}
                      </div>
                    </TableCell>
                    <TableCell
                      sx={{
                        // p: 1,
                        border: "none",
                        color: "#063c7a",
                        whiteSpace: "nowrap",
                      }}
                    >
                      <div className="flex justify-center">
                        {leave?.dept_manager_approve ? (
                          <RiCheckboxCircleFill className="text-green-600 text-xl" />
                        ) : (
                          <RiCheckboxBlankCircleLine className=" text-xl" />
                        )}
                      </div>
                    </TableCell>
                    <TableCell
                      sx={{
                        // p: 1,
                        border: "none",
                        color: "#063c7a",
                        whiteSpace: "nowrap",
                      }}
                    >
                      <div className="flex justify-center">
                        {leave?.hr_approve ? (
                          <RiCheckboxCircleFill className="text-green-600 text-xl" />
                        ) : (
                          <RiCheckboxBlankCircleLine className=" text-xl" />
                        )}
                      </div>
                    </TableCell>
                    <TableCell
                      sx={{
                        // p: 1,
                        textAlign: "center",
                        border: "none",
                      }}
                      align="center"
                    >
                      <Box
                        display="flex"
                        justifyContent="center"
                        alignItems="center"
                        height="100%"
                      >
                        <span
                          className={`p-1 w-24 rounded-md`}
                          style={{
                            backgroundColor: getStatusColor(leave?.status),
                            color:"#ffffff",
                            opacity: 0.65,
                          }}
                        >
                          {getModifiedStatus(leave?.status)}
                        </span>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={tableHeaders[0]?.length}
                    sx={{ textAlign: "center", py: 3 }}
                  >
                    No records found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Box>
  );
};

export default LeaveAnalysisTable;
