interface Props {
    color?: string;
}
export const ActiveBadge = ({ color }: Props) => {
    return (
        <svg
            width="64"
            height="22"
            viewBox="0 0 64 22"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g style={{ mixBlendMode: "multiply" }}>
                <rect width="64" height="22" rx="11" fill="#ECFDF3" />
                <path
                    d="M16.5 11C16.5 8.51562 14.4844 6.5 12 6.5C9.51562 6.5 7.5 8.51562 7.5 11C7.5 13.4844 9.51562 15.5 12 15.5C14.4844 15.5 16.5 13.4844 16.5 11Z"
                    stroke="#31DC01"
                    stroke-miterlimit="10"
                />
                <path
                    d="M12 14.375C13.864 14.375 15.375 12.864 15.375 11C15.375 9.13604 13.864 7.625 12 7.625C10.136 7.625 8.625 9.13604 8.625 11C8.625 12.864 10.136 14.375 12 14.375Z"
                    fill="#2AC200"
                />
                <path
                    d="M21.696 15H20.2983L23.4389 6.27273H24.9602L28.1009 15H26.7031L24.2358 7.85795H24.1676L21.696 15ZM21.9304 11.5824H26.4645V12.6903H21.9304V11.5824ZM31.9105 15.1321C31.277 15.1321 30.7315 14.9886 30.2741 14.7017C29.8196 14.4119 29.4702 14.0128 29.2259 13.5043C28.9815 12.9957 28.8594 12.4134 28.8594 11.7571C28.8594 11.0923 28.9844 10.5057 29.2344 9.99716C29.4844 9.4858 29.8366 9.08665 30.2912 8.79972C30.7457 8.51278 31.2813 8.36932 31.8977 8.36932C32.3949 8.36932 32.8381 8.46165 33.2273 8.64631C33.6165 8.82812 33.9304 9.08381 34.169 9.41335C34.4105 9.7429 34.554 10.1278 34.5994 10.5682H33.3594C33.2912 10.2614 33.1349 9.99716 32.8906 9.77557C32.6491 9.55398 32.3253 9.44318 31.919 9.44318C31.5639 9.44318 31.2528 9.53693 30.9858 9.72443C30.7216 9.90909 30.5156 10.1733 30.3679 10.517C30.2202 10.858 30.1463 11.2614 30.1463 11.7273C30.1463 12.2045 30.2188 12.6165 30.3636 12.9631C30.5085 13.3097 30.7131 13.5781 30.9773 13.7685C31.2443 13.9588 31.5582 14.054 31.919 14.054C32.1605 14.054 32.3793 14.0099 32.5753 13.9219C32.7741 13.831 32.9403 13.7017 33.0739 13.5341C33.2102 13.3665 33.3054 13.1648 33.3594 12.929H34.5994C34.554 13.3523 34.4162 13.7301 34.1861 14.0625C33.956 14.3949 33.6477 14.6562 33.2614 14.8466C32.8778 15.0369 32.4276 15.1321 31.9105 15.1321ZM39.0174 8.45455V9.47727H35.4421V8.45455H39.0174ZM36.4009 6.88636H37.6751V13.0781C37.6751 13.3253 37.712 13.5114 37.7859 13.6364C37.8597 13.7585 37.9549 13.8423 38.0714 13.8878C38.1907 13.9304 38.32 13.9517 38.4592 13.9517C38.5614 13.9517 38.6509 13.9446 38.7276 13.9304C38.8043 13.9162 38.864 13.9048 38.9066 13.8963L39.1367 14.9489C39.0629 14.9773 38.9577 15.0057 38.8214 15.0341C38.685 15.0653 38.5146 15.0824 38.31 15.0852C37.9748 15.0909 37.6623 15.0312 37.3725 14.9062C37.0827 14.7812 36.8484 14.5881 36.6694 14.3267C36.4904 14.0653 36.4009 13.7372 36.4009 13.3423V6.88636ZM40.4268 15V8.45455H41.701V15H40.4268ZM41.0703 7.4446C40.8487 7.4446 40.6584 7.37074 40.4993 7.22301C40.343 7.07244 40.2649 6.89347 40.2649 6.68608C40.2649 6.47585 40.343 6.29687 40.4993 6.14915C40.6584 5.99858 40.8487 5.9233 41.0703 5.9233C41.2919 5.9233 41.4808 5.99858 41.6371 6.14915C41.7962 6.29687 41.8757 6.47585 41.8757 6.68608C41.8757 6.89347 41.7962 7.07244 41.6371 7.22301C41.4808 7.37074 41.2919 7.4446 41.0703 7.4446ZM49.0146 8.45455L46.641 15H45.2773L42.8995 8.45455H44.2674L45.9251 13.4915H45.9933L47.6467 8.45455H49.0146ZM52.8104 15.1321C52.1655 15.1321 51.6101 14.9943 51.1442 14.7188C50.6811 14.4403 50.3232 14.0497 50.0703 13.5469C49.8203 13.0412 49.6953 12.4489 49.6953 11.7699C49.6953 11.0994 49.8203 10.5085 50.0703 9.99716C50.3232 9.4858 50.6754 9.08665 51.1271 8.79972C51.5817 8.51278 52.1129 8.36932 52.7209 8.36932C53.0902 8.36932 53.4482 8.4304 53.7947 8.55256C54.1413 8.67472 54.4524 8.86648 54.728 9.12784C55.0036 9.3892 55.2209 9.72869 55.38 10.1463C55.5391 10.5611 55.6186 11.0653 55.6186 11.6591V12.1108H50.4155V11.1562H54.37C54.37 10.821 54.3018 10.5241 54.1655 10.2656C54.0291 10.0043 53.8374 9.7983 53.5902 9.64773C53.3459 9.49716 53.0589 9.42188 52.7294 9.42188C52.3714 9.42188 52.0589 9.50994 51.7919 9.68608C51.5277 9.85937 51.3232 10.0866 51.1783 10.3679C51.0362 10.6463 50.9652 10.9489 50.9652 11.2756V12.0213C50.9652 12.4588 51.0419 12.831 51.1953 13.1378C51.3516 13.4446 51.5689 13.679 51.8473 13.8409C52.1257 14 52.451 14.0795 52.8232 14.0795C53.0646 14.0795 53.2848 14.0455 53.4837 13.9773C53.6825 13.9062 53.8544 13.8011 53.9993 13.6619C54.1442 13.5227 54.255 13.3509 54.3317 13.1463L55.5376 13.3636C55.4411 13.7187 55.2678 14.0298 55.0178 14.2969C54.7706 14.5611 54.4595 14.767 54.0845 14.9148C53.7124 15.0597 53.2876 15.1321 52.8104 15.1321Z"
                    fill="#027A48"
                />
            </g>
        </svg>
    );
};
