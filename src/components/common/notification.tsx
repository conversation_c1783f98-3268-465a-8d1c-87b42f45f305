import { notification } from "antd";

type NotificationType = "success" | "info" | "warning" | "error";

interface NotificationProps {
    type: NotificationType;
    message: string;
    description?: string;
}
const showNotification = ({
    type,
    message,
    description,
}: NotificationProps) => {
    notification[type]({
        message,
        description,
    });
};

export default showNotification;
