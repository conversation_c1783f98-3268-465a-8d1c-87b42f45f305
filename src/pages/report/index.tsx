import ReportFilter from '@/components/features/ReportFilter'
import Header from '@/components/layout/Header'
import MainLayout from '@/components/layout/MainLaylout'
import { Container, CssBaseline, Toolbar } from '@mui/material'
import React from 'react'
import { <PERSON><PERSON><PERSON>, ToastContainer } from 'react-toastify'

const index = () => {
  return (
    <>
          <CssBaseline />
          <ToastContainer
            position="top-right"
            autoClose={2000}
            hideProgressBar={false}
            newestOnTop={false}
            closeOnClick
            rtl={false}
            pauseOnFocusLoss
            draggable
            pauseOnHover
            theme="light"
            transition={Bounce}
          />
          <MainLayout>
            <Toolbar />
            <Container>
              <ReportFilter />
            </Container>
          </MainLayout>
        </>
      
    
  )
}

export default index