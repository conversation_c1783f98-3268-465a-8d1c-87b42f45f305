"use client";
import showNotification from "@/components/common/notification";
import ErrorMessageModal, {
  ErrorDetails,
} from "@/components/modals/errorMessageModal";
import ScheduleListModal from "@/components/modals/scheduleListModal";
import { API_ROUTE } from "@/constants/api-routes";
import { useLoadScript } from "@react-google-maps/api";
import { Button, Form } from "antd";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import ScheduleCreateForm from "./scheduleCreateForm";
import EmployeeTable from "./employeeTable";
import GoogleMapForm from "./googleMapForm";
import { GoogleLocation, RadiusInfo } from "@/interface/schedule";
import { User } from "@/interface/user";
import { getRequest, postRequest } from "@/services/apiRequestHandlers";
import Loader from "../common/Loader";
import { capitalizeFirstLetter } from "@/utils/helper";
import EmployeeDropDown from "./employeeDropDown";
const libraries: any = ["places"] as const;

const ScheduleSetting: React.FC = () => {
  const [form] = Form.useForm();
  const router = useRouter();
  const { isLoaded } = useLoadScript({
    googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY!,
    libraries,
  });
  const [locations, setLocations] = useState<GoogleLocation[]>([]);
  const [schedulingType, setSchedulingType] = useState<"Repetitive" | "Single">(
    "Single"
  );
  const [selectedUser, setSelectedUser] = useState<User[]>([]);
  const [isDateSelected, setIsDateSelected] = useState<boolean>(false);
  const [showErrorMessageModal, setShowErrorMessageModal] = useState<{
    visible: boolean;
    details?: ErrorDetails;
  }>({ visible: false });
  const [showScheduleModal, setShowScheduleModal] = useState<{
    visible: boolean;
    record?: any;
  }>({ visible: false });
  const [singleDate, setSingleDate] = useState<any>(null);
  const [dateRange, setDateRange] = useState<any>(null);
  const [radiusInfo, setRadiusInfo] = useState<RadiusInfo>();
  const [companyId, setCompanyId] = useState<number>();

  const handleSchedulingTypeChange = (e: any) => {
    setSchedulingType(e.target.value);
  };


  useEffect(() => {
    getRadiusInfo();
    getUserInfo();
  }, []);

  const getUserInfo = async () => {
    const userId = localStorage.getItem("uId");
    if (userId) {
      try {
        const userInfo: any = await getRequest(
          `${API_ROUTE.GET_USER_DETAILS}/${userId}`
        );
        setCompanyId(userInfo?.employee_company_id);
        localStorage.setItem("userCompanyId", userInfo?.employee_company_id);
      } catch (error) {
        return null;
      }
    }
  };
  const getRadiusInfo = async () => {
    try {
      const response: any = await getRequest(`${API_ROUTE.GET_CONFIG}`);
      if (response) setRadiusInfo(response);
    } catch (error) {
      console.error("Error fetching radius info:", error);
    }
  };

  if (!isLoaded)
    return (
      <div>
        <Loader />
      </div>
    );

  const handleConfirm = () => {
    if (selectedUser.length === 0 || locations.length === 0) {
      showNotification({
        type: "warning",
        message: "Please choose atleast one employee and location !",
      });
      return;
    }
    // Get form values
    form
      .validateFields()
      .then(async (values) => {
        const {
          schedulingType,
          shift,
          reason,
          description,
          singleDate,
          dateRange,
        } = values;

        // Format the payload
        const payload = {
          employees: selectedUser?.map((user) => user?.employee_id),
          shift_id: shift,
          scheduling_type: capitalizeFirstLetter(schedulingType),
          start_date:
            schedulingType === "Repetitive"
              ? dateRange[0]?.format("YYYY-MM-DD")
              : singleDate?.format("YYYY-MM-DD"),
          end_date:
            schedulingType === "Repetitive"
              ? dateRange[1]?.format("YYYY-MM-DD")
              : singleDate?.format("YYYY-MM-DD"),
          reason: reason,
          description: description,
          locations: locations.map((location: GoogleLocation) => ({
            name: location.address,
            latitude: location.lat,
            longitude: location.lng,
            radius: radiusInfo?.default_radius,
            measurement: radiusInfo?.measurement,
          })),
        };

        const response: any = await postRequest(
          API_ROUTE.SCHEDULE_CREATE,
          payload
        );
        if (response?.status_code === 200) {
          showNotification({
            type: "success",
            message: "Scheduling created successfully!",
          });
          router.push("/schedule-list");
        } else {
          if (selectedUser.length === 1 && response?.employees.length === 1) {
            showNotification({
              type: "error",
              message:
                "The following employee already has schedules during the specified period !",
            });
          } else {
            setShowErrorMessageModal({
              visible: true,
              details: response,
            });
          }
        }
      })
      .catch((errorInfo) => {
        console.error("Validation Failed:", errorInfo);
      });
  };

  return (
    <div className="md:pl-[70px] md:pr-2 pb-4 bg-white">
      <div className="px-2">
        <div className="py-3 pr-8 flex justify-end fixed right-0 z-50 bg-white w-full">
          <Button
            onClick={() => router.push("/schedule-list")}
            className="mr-4"
          >
            Discard
          </Button>
          <Button className="bg-primary text-white" onClick={handleConfirm}>
            Confirm
          </Button>
        </div>
        <div className="flex flex-wrap gap-4 lg:flex-nowrap pt-[50px]">
          <div className="flex w-full flex-col gap-2 lg:w-1/2">
            <EmployeeDropDown
              companyId={companyId}
              selectedUser={selectedUser}
              setSelectedUser={setSelectedUser}
            />
            <GoogleMapForm
              locations={locations}
              radiusInfo={radiusInfo}
              setLocations={setLocations}
            />
            <ScheduleCreateForm
              selectedUser={selectedUser}
              form={form}
              schedulingType={schedulingType}
              radiusInfo={radiusInfo}
              handleSchedulingTypeChange={handleSchedulingTypeChange}
              setSingleDate={setSingleDate}
              setDateRange={setDateRange}
              setIsDateSelected={setIsDateSelected}
            />
          </div>
          <EmployeeTable
            selectedUser={selectedUser}
            setSelectedUser={setSelectedUser}
            setShowScheduleModal={setShowScheduleModal}
            isDateSelected={isDateSelected}
          />
        </div>
      </div>
      <ErrorMessageModal
        isModalOpen={showErrorMessageModal.visible}
        handleCancel={() => setShowErrorMessageModal({ visible: false })}
        errorDetails={showErrorMessageModal.details}
      />
      <ScheduleListModal
        isModalVisible={showScheduleModal.visible}
        handleCancel={() => setShowScheduleModal({ visible: false })}
        details={showScheduleModal.record}
        singleDate={singleDate}
        dateRange={dateRange}
        schedulingType={schedulingType}
      />
    </div>
  );
};

export default ScheduleSetting;
