import { Form, Modal } from "antd";
import React, { useEffect } from "react";
import { API_ROUTE } from "@/constants/api-routes";
import { putRequest } from "@/services/apiRequestHandlers";
import TextArea from "antd/es/input/TextArea";
import showNotification from "../common/notification";

interface Props {
    record: any;
    isModalOpen: boolean;
    handleCancel: () => void;
    fetchList: () => void;
    type: string | undefined;
}
const DeclineModal = ({
    record,
    isModalOpen,
    handleCancel,
    fetchList,
    type,
}: Props) => {
    const [form] = Form.useForm();

    useEffect(() => {
        form.resetFields();
    }, [record]);
    const handleDecline = async (values: any) => {
        form.validateFields();
        const payLoad = {
            schedules: type === "bulk" ? record : undefined,
            status: "Declined",
            reason: values.reason,
        };
        try {
            const resp = await putRequest(
                type === "single"
                    ? `${API_ROUTE.DECLINE_SCHEDULE}/${record}`
                    : `${API_ROUTE.BULK_DECLINE_SCHEDULE}`,
                payLoad
            );
            if (resp) {
                showNotification({
                    type: "success",
                    message: "Successfuly Declined !",
                });
                fetchList();
                handleCancel();
            }
        } catch (err) {
            console.log(err);
            showNotification({
                type: "error",
                message: "Decline Unsuccessful !",
            });
        }
    };

    return (
        <Modal
            centered
            title="Are you sure about this ?"
            open={isModalOpen}
            onCancel={handleCancel}
            onOk={() => {
                form.validateFields()
                    .then((values) => handleDecline(values))
                    .catch((err) => {
                        console.log("Validation failed:", err);
                    });
            }}
            okText="Yes"
            className="text-xl"
        >
            <Form form={form} layout="vertical" className="w-full">
                <p className="text-center text-red-600">
                    You are declining this Schedule !
                </p>
                <Form.Item
                    label={<span className="text-xl">Reason</span>}
                    name="reason"
                    rules={[
                        {
                            required: true,
                            message: "Please enter the reason behind this !",
                        },
                    ]}
                    className="w-full"
                >
                    <TextArea placeholder="Type Here..." />
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default DeclineModal;
