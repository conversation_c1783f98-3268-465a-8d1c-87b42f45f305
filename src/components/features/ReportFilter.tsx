"use client";

import { AbsenceEmployeeReportService } from "@/services/AbsenceEmployeeReportService";
import { AttendenceReportService } from "@/services/AttendenceReportService";
import { AttendenceStatusReportService } from "@/services/AttendenceStatusReportService";
import { EarlyExitEmployeeReportService } from "@/services/EarlyExitEmployeeReportService";
import { EmployeeListReportService } from "@/services/EmployeeListReportService";
import { FilterReportService } from "@/services/FilterReportService";
import { LateEmployeeReportService } from "@/services/LateEmployeeReportService";
import { SameCheckinCheckoutReportService } from "@/services/SameCheckinCheckoutReportService";
import { SUCCESS } from "@/utils/common";
import { dateTimeFormatForExcel } from "@/utils/helper";
import DownloadIcon from "@mui/icons-material/Download";
import {
  Autocomplete,
  Box,
  Checkbox,
  CircularProgress,
  Divider,
  TextField,
  Typography,
} from "@mui/material";
import { DatePicker } from "@mui/x-date-pickers";
import dayjs from "dayjs";
import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import CommonButton from "../common/CommonButton";
import { AbsenceEmployeeReport } from "./attendance-reports/AbsenceEmployeeReport";
import { AttendanceDetailsReport } from "./attendance-reports/AttendanceDetailsReport";

import { MovementLogReport } from "@/components/features/attendance-reports/MovementLogReport";
import { EmployeeBloodReport } from "@/components/features/employee-reports/EmployeeBloodGroupReport";
import { EmployeeContactReport } from "@/components/features/employee-reports/EmployeeContactReport";
import { EmployeeEducationInfoReport } from "@/components/features/employee-reports/EmployeeEducationInfoReport";
import { EmployeeHistoryReport } from "@/components/features/employee-reports/EmployeeHistoryReport";
import { EmployeeNomineeReport } from "@/components/features/employee-reports/EmployeeNomineeReport";
import { EmployeeSalaryHistoryReport } from "@/components/features/employee-reports/EmployeeSalaryHistory";
import { EmployeeAddressReportService } from "@/services/EmployeeAddressReportService";
import { EmployeeCvService } from "@/services/EmployeeCvService";
import { EmployeeEducationInfoService } from "@/services/EmployeeEducationInfoService";
import { EmployeeHistoryService } from "@/services/EmployeeHistoryReportService";
import { EmployeeNomineeService } from "@/services/EmployeeNomineeService";
import { EmployeePersonalService } from "@/services/EmployeePersonalService";
import { EmployeeSalaryHistoryService } from "@/services/EmployeeSalaryHistoryService";
import { MovementLogReportService } from "@/services/MovementLogReportService";
import { NewJoinerReportService } from "@/services/NewJoinerReportService";
import { PlaylistAddCheck } from "@mui/icons-material";
import { BlobProvider } from "@react-pdf/renderer";
import { AttendanceReport } from "./attendance-reports/AttendanceReport";
import { AttendanceStatusReport } from "./attendance-reports/AttendenceStatusReport";
import { EarlyExitEmployeeReport } from "./attendance-reports/EarlyExitEmployeeReport";
import { LateEmployeeReport } from "./attendance-reports/LateEmployeeReport";
import { ResignedEmployeeAttendanceStatusReport } from "./attendance-reports/ResignedEmployeeAttendenceStatusReport";
import { SameCheckinCheckoutReport } from "./attendance-reports/SameCheckinCheckoutReport";
import { EmployeeAddressReport } from "./employee-reports/EmployeeAddressReport";
import EmployeeCv from "./employee-reports/EmployeeCv";
import { EmployeeListReport } from "./employee-reports/EmployeeListReport";
import { EmployeePersonalReport } from "./employee-reports/EmployeePersonalReport";
import { EmployeePersonalReportForAirlinesReport } from "./employee-reports/EmployeePersonalReportForAirlines";
import { EmployeeSeparationReport } from "./employee-reports/EmployeeSeparationReport";
import {EmployeePersonalReportForOTA} from "@/components/features/employee-reports/EmployeePersonalReportForOTA";
import {EmployeePersonalServiceForOTA} from "@/services/EmployeePersonalServiceForOTA";
import {EmployeeSeparationDetailsReport} from "@/components/features/employee-reports/EmployeeSeparationDetailsReport";
import {EmployeeSeparationService} from "@/services/EmployeeSeparationService";
import { getCookie } from "cookies-next";
import {medicalInsuranceReport} from "./employee-reports/MedicInsuranceReport";
import {MedicalInsuranceReportService} from "@/services/MedicalInsuranceReportService";

interface Company {
  id: number;
  company_name: string;
}

interface Division {
  division_id: number;
  name: string;
}

interface Department {
  division_id?: number | null;
  department_id: number;
  department_name: string;
  name: string;
}

interface Location {
  location_id: number;
  location_name: string;
}

interface Employee {
  division_id?: number | null;
  department_id: number | null;
  employee_id: number;
  employee_name: string;
  location_id: number | null;
  unique_id: string | null;
}

interface Report {
  id: string;
  name: string;
}

interface ConstractedData {
  company_id: number | null;
  active: boolean;
  start_date: string;
  end_date: string;
  employee_id: number | null;
  division_id: number | null;
  department_id: number | null;
  work_location_id?: number | null;
}

const reports = [
  {
    id: "monthly_attendance_status_report",
    name: "Monthly Attendance Status Report",
  },
  {
    id: "resign_employee_monthly_attendance_status_report",
    name: "Resign Employee Monthly Attendance Status Report",
  },
  {
    id: "attendance_report_dynamic",
    name: "Monthly Attendance Details Report",
  },
  { id: "late_employee_report", name: "Late Employee Report" },
  {
    id: "early_exit_employee_report",
    name: "Early Exit Status Employee Report",
  },
  { id: "absence_employee_report", name: "Absence Employee Report" },
  { id: "attendance_report", name: "Attendance Report" },
  { id: "movement_log_report", name: "Movement Log Report" },
  //   { id: "same_checkin_checkout", name: "Same Check In Check Out" },
  //   { id: "employee_job_card_report", name: "Employee Job Card Report" },
  //   { id: "daily_department_wise_report", name: "Daily Department Wise Report" },
  //   { id: "daily_attendance_report", name: "Daily Attendance Report" },
  //   { id: "daily_check_in_out_report", name: "Daily Check In, Out Report" },
  {
    id: "daily_absence_employee_report",
    name: "Employee Daily Absence Report",
  },
];

const employeeReports = [
  {
    id: "employee_list_report",
    name: "Employee List Report",
  },
  {
    id: "employee_resign_list_report",
    name: "Resign Employee List Report",
  },
  {
    id: "employee_blood_group_report",
    name: "Employee Blood Group List Report",
  },
  {
    id: "employee_contact_report",
    name: "Employee Contact Report",
  },
  {
    id: "employee_cv",
    name: "Employee CV",
  },
  {
    id: "employee_history_report",
    name: "Employment History Report",
  },
  {
    id: "employee_salary_history_report",
    name: "Employee Salary History Report",
  },
  {
    id: "employee_educational_info_report",
    name: "Employee Educational Info Report",
  },
  {
    id: "employee_nominee_report",
    name: "Employee Nominee Report",
  },
  {
    id: "employee_newjoin_report",
    name: "Employee New joiner report",
  },
  {
    id: "employee_separation_report",
    name: "Employee Separation Report",
  },
  {
    id: "employee_separation_details_report",
    name: "Employee Separation Details Report",
  },
  {
    id: "employee_personal_details_report",
    name: "Employee Personal Details Report",
  },
  {
    id: "employee_address_report",
    name: "Employee Address Report",
  },
  {
    id: "medical_insurance_report",
    name: "Medical Insurance Report",
  },

];

const ReportFilter = () => {
  const [dateTimePicker, setDateTimePicker] = useState({
    startTime: null,
    endTime: null,
  });
  const [errorText, setErrorText] = useState("");
  const [errorTextReportType, setErrorTextReportType] = useState("");
  const [departmentError, setDepartmentError] = useState("");
  const [companies, setCompanies] = useState([]);
  const [divisions, setDivisions] = useState([]);
  const [departments, setDepartments] = useState([]);
  const [locations, setLocations] = useState([]);
  const [employees, setEmployees] = useState([]);
  const [reportTypes, setReportTypes] = useState<Report[]>(reports);
  const [employeeReportTypes, setemployeeReportTypes] =
    useState<Report[]>(employeeReports);
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null);
  const [selectedDivision, setSelectedDivision] = useState<Division | null>(
    null
  );
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(
    null
  );
  const [selectedLocaiton, setSelectedLocation] = useState<Location | null>(
    null
  );
  const [selectedreport, setSelectedReport] = useState<Report | null>(null);
  const [selectedDepartment, setSelectedDepartment] =
    useState<Department | null>(null);
  const [companyError, setCompanyError] = useState("");
  const [loading, setLoading] = useState(false);
  const [queryReportType, setQueryReportType] = useState("attendance");
  const [isSeparated, setIsSeparated] = useState(false);
  const [cvData, setCvData] = useState<null | any>(null);
  const today: any = dayjs();
  const currentDate: any = dayjs(new Date());
  const dateTimeFormat = (date: any) =>
    !date ? null : dayjs(date).format("YYYY-MM-DD");

  const hasStartTime = dateTimePicker.startTime;
  const hasEndTime = dateTimePicker.endTime;
  

  useEffect(() => {
    const reportType =localStorage.getItem("rTyp");
    if (reportType ==="employee" ) {
      setQueryReportType(reportType as string);
    } else {
      setQueryReportType("attendance");
    }
  }, []);

  const handleReset = () => {
    setSelectedCompany(null);
    setSelectedDivision(null);
    setSelectedDepartment(null);
    setSelectedLocation(null);
    setSelectedEmployee(null);
    setSelectedReport(null);
    setErrorText("");
    setCompanyError("");
    setIsSeparated(false);
    setErrorTextReportType("");
    setDateTimePicker({
      startTime: null,
      endTime: null,
    });
    setCvData(null);
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const { data, status } = await FilterReportService.getCompany();
        if (status === SUCCESS) {
          setCompanies(data);
        }
      } catch (error) {
        console.log("My Log error: ", error);
      }
    };

    fetchData();
  }, []);

  const showDatePicker =
    queryReportType === "attendance" ||
    //put report id to show date picker for employee reports
    ((selectedreport?.id === "employee_rh" ||
      selectedreport?.id === "early_exit_e" ||
      selectedreport?.id === "employee_separation_details_report" ||
      selectedreport?.id === "employee_separation_report") &&
      queryReportType === "employee");

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    if (!selectedCompany?.id) {
      setCompanyError("Please select a company.");
      return;
    }
    if (!selectedreport?.id) {
      setErrorTextReportType("Select Report type");
      return;
    }
    if (showDatePicker) {
      if (!dateTimePicker?.startTime || !dateTimePicker?.endTime) {
        setErrorText("Start or end date is empty");
        return;
      }
    }
    if (
      selectedreport.id === "employee_personal_details_report" &&
      selectedCompany.id == 9
    ) {
      if (!selectedDepartment?.department_id) {
        setDepartmentError("Please Select Department!");
        return;
      }
    }

    const startTime = dateTimeFormat(dateTimePicker?.startTime);
    const endTime = dateTimeFormat(dateTimePicker?.endTime);
    const startDate = dayjs(dateTimePicker.startTime);
    const endDate = dayjs(dateTimePicker.endTime || currentDate);
    const differenceInDays = endDate.diff(startDate, "day");
    if (selectedreport?.id !== "employee_separation_details_report" && differenceInDays >= 34) {
      setErrorText("Date range has to be one month or less");
      return;
    }
    if (differenceInDays < 0) {
      setErrorText("Start date should be less than end date");
      return;
    }

    const constructedData: ConstractedData = {
      company_id: selectedCompany?.id || null,
      active: !isSeparated,
      start_date: startTime || "",
      end_date: endTime || "",
      employee_id: selectedEmployee?.employee_id || null,
      division_id: selectedDivision?.division_id || null,
      department_id: selectedDepartment?.department_id || null,
    };
    if (selectedLocaiton?.location_id) {
      constructedData.work_location_id = selectedLocaiton?.location_id;
    }

    if (queryReportType == "attendance") {
      setLoading(true);
      if (selectedreport?.id == "monthly_attendance_status_report") {
        try {
          const { data, status } =
            await AttendenceStatusReportService.statusReport(constructedData);
          if (status === SUCCESS) {
            await AttendanceStatusReport(
              data,
              selectedreport?.name,
              dateTimeFormatForExcel(dateTimePicker?.startTime),
              dateTimeFormatForExcel(dateTimePicker.endTime),
              selectedCompany?.company_name,
              selectedCompany?.id
            );
          }
        } catch (error) {
          console.log("My Log error: ", error);
        }
      }

      if (
        selectedreport?.id == "resign_employee_monthly_attendance_status_report"
      ) {
        try {
          constructedData.active = false;
          const { data, status } =
            await AttendenceStatusReportService.statusReport(constructedData);
          if (status === SUCCESS) {
            await ResignedEmployeeAttendanceStatusReport(
              data,
              selectedreport?.name,
              dateTimeFormatForExcel(dateTimePicker?.startTime),
              dateTimeFormatForExcel(dateTimePicker.endTime),
              selectedCompany?.company_name,
              selectedCompany?.id
            );
          }
        } catch (error) {
          console.log("My Log error: ", error);
        }
      }

      if (selectedreport?.id == "same_checkin_checkout") {
        try {
          const { data, status } =
            await SameCheckinCheckoutReportService.sameCheckinCheckoutReport(
              constructedData
            );
          if (status === SUCCESS) {
            await SameCheckinCheckoutReport(
              data,
              selectedreport?.name,
              dateTimeFormatForExcel(dateTimePicker?.startTime),
              dateTimeFormatForExcel(dateTimePicker.endTime),
              selectedCompany?.company_name,
              selectedCompany?.id
            );
          }
        } catch (error) {
          console.log("My Log error: ", error);
        }
      }

      if (selectedreport?.id == "attendance_report_dynamic") {
        try {
          const { data, status } =
            await AttendenceStatusReportService.statusReport(constructedData);
          if (status === SUCCESS) {
            await AttendanceDetailsReport(
              data,
              selectedreport?.name,
              dateTimeFormatForExcel(dateTimePicker?.startTime),
              dateTimeFormatForExcel(dateTimePicker.endTime),
              selectedCompany?.company_name,
              selectedCompany?.id
            );
          }
        } catch (error) {
          console.log("My Log error: ", error);
        }
      }

      if (selectedreport?.id == "attendance_report") {
        try {
          const { data, status } =
            await AttendenceReportService.attendenceReport(constructedData);
          if (status === SUCCESS) {
            await AttendanceReport(
              data,
              selectedreport?.name,
              dateTimeFormatForExcel(dateTimePicker?.startTime),
              dateTimeFormatForExcel(dateTimePicker.endTime),
              selectedCompany?.company_name,
              selectedDepartment?.department_name,
              selectedCompany?.id
            );
          }
        } catch (error) {
          console.log("My Log error: ", error);
        }
      }

      if (selectedreport?.id == "absence_employee_report") {
        try {
          const { data, status } =
            await AbsenceEmployeeReportService.absenceReport(constructedData);
          if (status === SUCCESS) {
            await AbsenceEmployeeReport(
              data,
              selectedreport?.name,
              dateTimeFormatForExcel(dateTimePicker?.startTime),
              dateTimeFormatForExcel(dateTimePicker.endTime),
              selectedCompany?.company_name,
              selectedCompany?.id
            );
          }
        } catch (error) {
          console.log("My Log error: ", error);
        }
      }
      if (selectedreport?.id == "movement_log_report") {
        try {
          const { data, status } =
            await MovementLogReportService.movementLogReport(constructedData);
          if (status === SUCCESS) {
            console.log(data);
            await MovementLogReport(
              data,
              selectedreport?.name,
              dateTimeFormatForExcel(dateTimePicker?.startTime),
              dateTimeFormatForExcel(dateTimePicker.endTime),
              selectedCompany?.company_name,
              selectedCompany?.id
            );
          }
        } catch (error) {
          console.log("My Log error: ", error);
        }
      }

      if (selectedreport?.id == "early_exit_employee_report") {
        try {
          const { data, status } =
            await EarlyExitEmployeeReportService.earlyExitReport(
              constructedData
            );
          if (status === SUCCESS) {
            await EarlyExitEmployeeReport(
              data,
              selectedreport?.name,
              dateTimeFormatForExcel(dateTimePicker?.startTime),
              dateTimeFormatForExcel(dateTimePicker.endTime),
              selectedCompany?.company_name,
              selectedCompany?.id
            );
          }
        } catch (error) {
          console.log("My Log error: ", error);
        }
      }

      if (selectedreport?.id == "late_employee_report") {
        try {
          const { data, status } = await LateEmployeeReportService.lateReport(
            constructedData
          );
          if (status === SUCCESS) {
            await LateEmployeeReport(
              data,
              selectedreport?.name,
              dateTimeFormatForExcel(dateTimePicker?.startTime),
              dateTimeFormatForExcel(dateTimePicker.endTime),
              selectedCompany?.company_name,
              selectedCompany?.id
            );
          }
        } catch (error) {
          console.log("My Log error: ", error);
        }
      }
      setLoading(false);
    } else {
      setLoading(true);
      if (selectedreport?.id == "employee_list_report") {
        try {
          const { data, status } =
            await EmployeeListReportService.employeeListReport(constructedData);
          if (status === SUCCESS) {
            await EmployeeListReport(
              data,
              selectedreport?.name,
              selectedCompany?.company_name,
              selectedDepartment?.department_name,
              selectedCompany?.id
            );
          }
        } catch (error) {
          console.log("My Log error: ", error);
        }
      }

      if (selectedreport?.id == "employee_resign_list_report") {
        constructedData.active = false;
        try {
          const { data, status } =
            await EmployeeListReportService.employeeListReport(constructedData);
          if (status === SUCCESS) {
            await EmployeeListReport(
              data,
              selectedreport?.name,
              selectedCompany?.company_name,
              selectedDepartment?.department_name,
              selectedCompany?.id
            );
          }
        } catch (error) {
          console.log("My Log error: ", error);
        }
      }

      if (selectedreport?.id == "employee_blood_group_report") {
        try {
          const { data, status } =
            await EmployeeListReportService.employeeListReport(constructedData);
          if (status === SUCCESS) {
            await EmployeeBloodReport(
              data,
              selectedreport?.name,
              selectedCompany?.company_name,
              selectedDepartment?.department_name,
              selectedCompany?.id
            );
          }
        } catch (error) {
          console.log("My Log error: ", error);
        }
      }

      if (selectedreport?.id == "employee_contact_report") {
        try {
          const { data, status } =
            await EmployeeListReportService.employeeListReport(constructedData);
          if (status === SUCCESS) {
            await EmployeeContactReport(
              data,
              selectedreport?.name,
              selectedCompany?.company_name,
              selectedDepartment?.department_name,
              selectedCompany?.id
            );
          }
        } catch (error) {
          console.log("My Log error: ", error);
        }
      }

      if (selectedreport?.id == "employee_history_report") {
        try {
          const { data, status } =
            await EmployeeHistoryService.employeeHistoryReport(constructedData);
          if (status === SUCCESS) {
            await EmployeeHistoryReport(
              data,
              selectedreport?.name,
              selectedCompany?.company_name,
              selectedDepartment?.department_name,
              selectedCompany?.id
            );
          }
        } catch (error) {
          console.log("My Log error: ", error);
        }
      }

      if (selectedreport?.id == "employee_salary_history_report") {
        try {
          const { data, status } =
            await EmployeeSalaryHistoryService.employeeSalaryHistoryReport(
              constructedData
            );
          if (status === SUCCESS) {
            await EmployeeSalaryHistoryReport(
              data,
              selectedreport?.name,
              selectedCompany?.company_name,
              selectedDepartment?.department_name,
              selectedCompany?.id
            );
          }
        } catch (error) {
          console.log("My Log error: ", error);
        }
      }

      if (selectedreport?.id == "employee_educational_info_report") {
        try {
          const { data, status } =
            await EmployeeEducationInfoService.educationInfoReport(
              constructedData
            );
          if (status === SUCCESS) {
            await EmployeeEducationInfoReport(
              data,
              selectedreport?.name,
              selectedCompany?.company_name,
              selectedDepartment?.department_name,
              selectedCompany?.id
            );
          }
        } catch (error) {
          console.log("My Log error: ", error);
        }
      }

      if (selectedreport?.id == "employee_nominee_report") {
        try {
          const { data, status } = await EmployeeNomineeService.nomineeReport(
            constructedData
          );
          if (status === SUCCESS) {
            await EmployeeNomineeReport(
              data,
              selectedreport?.name,
              selectedCompany?.company_name,
              selectedDepartment?.department_name,
              selectedCompany?.id
            );
          }
        } catch (error) {
          console.log("My Log error: ", error);
        }
      }

      if (selectedreport?.id == "employee_cv") {
        try {
          const { data, status } = await EmployeeCvService.employeeCv(
            constructedData
          );
          if (status === SUCCESS) {
            setCvData(data);
          }
        } catch (error) {
          console.log("My Log error: ", error);
        }
      }
      if (selectedreport?.id == "employee_separation_report") {
        try {
          constructedData.active = false;
          const { data, status } =
            await EmployeeListReportService.employeeListReport(constructedData);
          if (status === SUCCESS) {
            await EmployeeSeparationReport(
              data,
              selectedreport?.name,
              selectedCompany?.company_name,
              selectedDepartment?.department_name,
              selectedCompany?.id
            );
          }
        } catch (error) {
          console.log("My Log error: ", error);
        }
      }
      if (selectedreport?.id == "employee_newjoin_report") {
        try {
          const { data, status } = await NewJoinerReportService.newJoinerList(
            constructedData
          );
          if (status === SUCCESS) {
            await EmployeeListReport(
              data,
              selectedreport?.name,
              selectedCompany?.company_name,
              selectedDepartment?.department_name,
              selectedCompany?.id
            );
          }
        } catch (error) {
          console.log("My Log error: ", error);
        }
      }
      if (selectedreport?.id == "employee_separation_details_report") {
        try {
          constructedData['active'] = false;
          const { data, status } =
              await EmployeeSeparationService.separationDetails(constructedData);
          if (status === SUCCESS) {
            await EmployeeSeparationDetailsReport(
                data,
                selectedreport?.name,
                selectedCompany?.company_name,
                selectedDepartment?.department_name,
                selectedCompany?.id
            );
          }
        } catch (error) {
          console.log("My Log error: ", error);
        }
      }
      if (selectedreport?.id == "employee_personal_details_report") {
        try {
          if (selectedCompany?.company_name === "US-Bangla Airlines Ltd") {
            const { data, status } =
                await EmployeePersonalService.perosnalDetails(constructedData);
            if (status === SUCCESS) {
              await EmployeePersonalReportForAirlinesReport(
                  data,
                  selectedreport?.name,
                  selectedCompany?.company_name,
                  selectedDepartment?.department_name,
                  selectedCompany?.id
              );
            }
          }
          else if (selectedCompany?.company_name !== "US-Bangla Airlines Ltd") {
            const { data, status } =
                await EmployeePersonalServiceForOTA.perosnalDetails(constructedData);
            if (status === SUCCESS) {
              await EmployeePersonalReportForOTA(
                  data,
                  selectedreport?.name,
                  selectedCompany?.company_name,
                  selectedDepartment?.department_name,
                  selectedCompany?.id
              );
            }
          }
        } catch (error) {
          console.log("My Log error: ", error);
        }
      }
      if (selectedreport?.id == "employee_address_report") {
        try {
          const { data, status } =
            await EmployeeAddressReportService.employeeAddress(constructedData);
          if (status === SUCCESS) {
            await EmployeeAddressReport(
              data,
              selectedreport?.name,
              selectedCompany?.company_name,
              selectedDepartment?.department_name,
              selectedCompany?.id
            );
          }
        } catch (error) {
          console.log("My Log error: ", error);
        }
      }

      if (selectedreport?.id == "medical_insurance_report") {
        try {
          const { data, status } =
              await MedicalInsuranceReportService.medicalReport(constructedData);
          if (status === SUCCESS) {
            await medicalInsuranceReport(
                data,
                selectedreport?.name,
                selectedCompany?.company_name,
                selectedDepartment?.department_name,
                selectedCompany?.id
            );
          }
        } catch (error) {
          console.log("My Log error: ", error);
        }
      }
      setLoading(false);
    }
  };

  return (
    <div className="md:pl-[50px]">
      <Box
      className=" !shadow-xl"
        sx={{
          backgroundColor: `#fff !important`,
          p: 3,
          mb: 3,
        }}
      >
        <Typography
          variant="h6"
          sx={{
            fontSize: "16px",
            fontWeight: 600,
          }}
        >
          Filter
        </Typography>
        <Divider sx={{ mb: 3 }} />
        <Box>
          <form onSubmit={handleSubmit}>
            <Box className="grid grid-cols-1 md:grid-cols-2 gap-5">
              <Box className="flex flex-col gap-5">
                <Box>
                  <Autocomplete
                    id="company-select"
                    options={companies}
                    getOptionLabel={(option) => option.company_name}
                    value={selectedCompany}
                    onInputChange={(event, newInputValue, reason) => {
                      if (reason == "clear") {
                        setSelectedEmployee(null);
                        setSelectedDivision(null);
                        setSelectedDepartment(null);
                        setSelectedLocation(null);
                        setDivisions([]);
                        setDepartments([]);
                        setLocations([]);
                        setEmployees([]);
                        setIsSeparated(false);
                      } else {
                        setIsSeparated(false);
                        setSelectedEmployee(null);
                      }
                    }}
                    onChange={async (event, newValue) => {
                      setSelectedCompany(newValue);
                      setCompanyError("");
                      setCvData(null);
                      setSelectedDepartment(null);
                      if (newValue?.id) {
                        try {
                          const { data, status } =
                            await FilterReportService.getDivision(newValue?.id);
                          if (status === SUCCESS) {
                            setDivisions(data?.divisions);
                          }
                        } catch (error) {
                          console.log("My Log error: ", error);
                        }

                        try {
                          const { data, status } =
                            await FilterReportService.getDepartment(
                              newValue?.id,
                              selectedDivision?.division_id
                            );
                          if (status === SUCCESS) {
                            setDepartments(data?.departments);
                          }
                        } catch (error) {
                          console.log("My Log error: ", error);
                        }
                        try {
                          const { data, status } =
                            await FilterReportService.getLocations(
                              newValue?.id
                            );
                          if (status === SUCCESS) {
                            setLocations(data?.locations);
                          }
                        } catch (error) {
                          console.log("My Log error: ", error);
                        }
                        try {
                          const { data, status } =
                            await FilterReportService.getEmployees(
                              newValue?.id,
                              null,
                              null,
                              null,
                              false
                            );
                          if (status === SUCCESS) {
                            setEmployees(data?.employee);
                          }
                        } catch (error) {
                          console.log("My Log error: ", error);
                        }
                      }
                    }}
                    // renderInput={(params) => (
                    //   <TextField
                    //     {...params}
                    //     label="Select Company"
                    //     variant="outlined"
                    //     helperText={
                    //       <p className="text-red-600">{companyError}</p>
                    //     }
                    //   />
                    // )}
                    renderInput={(params) => (
                        <TextField
                            {...params}
                            label="Select Company"
                            variant="outlined"
                            helperText={
                              companyError ? (
                                  <span className="text-red-600">{companyError}</span>
                              ) : null
                            }
                            error={!!companyError}
                        />
                    )}

                  />
                </Box>
                <Box>
                  <Autocomplete
                    id="division-select"
                    options={divisions}
                    getOptionLabel={(option) => option?.name}
                    value={selectedDivision}
                    onInputChange={async (event, newInputValue, reason) => {
                      if (reason == "clear") {
                        setSelectedDepartment(null);
                        setSelectedEmployee(null);
                        try {
                          const { data, status } =
                            await FilterReportService.getDepartment(
                              selectedCompany?.id,
                              null
                            );
                          if (status === SUCCESS) {
                            setDepartments(data?.departments);
                          }
                        } catch (error) {
                          console.log("My Log error: ", error);
                        }

                        try {
                          const { data, status } =
                            await FilterReportService.getEmployees(
                              selectedCompany?.id,
                              null,
                              null,
                              null,
                              false
                            );
                          if (status === SUCCESS) {
                            setEmployees(data?.employee);
                          }
                        } catch (error) {
                          console.log("My Log error: ", error);
                        }
                      } else {
                        setSelectedEmployee(null);
                        setSelectedDepartment(null);
                        setIsSeparated(false);
                      }
                    }}
                    onChange={async (event, newValue) => {
                      setSelectedDivision(newValue);
                      setCvData(null);
                      if (newValue?.division_id) {
                        try {
                          const { data, status } =
                            await FilterReportService.getDepartment(
                              selectedCompany?.id,
                              newValue?.division_id
                            );
                          if (status === SUCCESS) {
                            setDepartments(data?.departments);
                          }
                        } catch (error) {
                          console.log("My Log error: ", error);
                        }
                        try {
                          const { data, status } =
                            await FilterReportService.getEmployees(
                              selectedCompany?.id,
                              newValue?.division_id,
                              null,
                              null,
                              false
                            );
                          if (status === SUCCESS) {
                            setEmployees(data?.employee);
                          }
                        } catch (error) {
                          console.log("My Log error: ", error);
                        }
                      }
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Select Division"
                        variant="outlined"
                      />
                    )}
                  />
                </Box>
                <Box>
                  <Autocomplete
                    id="department-select"
                    options={departments}
                    getOptionLabel={(option) => option?.department_name}
                    value={selectedDepartment}
                    onInputChange={async (event, newInputValue, reason) => {
                      if (reason == "clear") {
                        setSelectedEmployee(null);
                        try {
                          const { data, status } =
                            await FilterReportService.getEmployees(
                              selectedCompany?.id,
                              selectedDivision?.division_id,
                              null,
                              null,
                              false
                            );
                          if (status === SUCCESS) {
                            setEmployees(data?.employee);
                          }
                        } catch (error) {
                          console.log("My Log error: ", error);
                        }
                      } else {
                        setSelectedEmployee(null);
                        setIsSeparated(false);
                      }
                    }}
                    onChange={async (event, newValue) => {
                      setSelectedDepartment(newValue);
                      setDepartmentError("");
                      setCvData(null);
                      if (newValue?.department_id) {
                        try {
                          const { data, status } =
                            await FilterReportService.getEmployees(
                              selectedCompany?.id,
                              selectedDivision?.division_id,
                              newValue?.department_id,
                              null,
                              false
                            );
                          if (status === SUCCESS) {
                            setEmployees(data?.employee);
                          }
                        } catch (error) {
                          console.log("My Log error: ", error);
                        }
                      }
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Select Department"
                        variant="outlined"
                      />
                    )}
                  />
                  {departmentError && (
                    <Box sx={{ color: "error.main", fontSize: "12px" }}>
                      {departmentError}
                    </Box>
                  )}
                </Box>
                <Box>
                  <Autocomplete
                    id="location-select"
                    options={locations}
                    getOptionLabel={(option) => option?.location_name}
                    value={selectedLocaiton}
                    onInputChange={async (event, newInputValue, reason) => {
                      if (reason == "clear") {
                        setSelectedEmployee(null);
                        try {
                          const { data, status } =
                            await FilterReportService.getEmployees(
                              selectedCompany?.id,
                              selectedDivision?.division_id,
                              selectedDepartment?.department_id,
                              null,
                              false
                            );
                          if (status === SUCCESS) {
                            setEmployees(data?.employee);
                          }
                        } catch (error) {
                          console.log("My Log error: ", error);
                        }
                      } else {
                        setSelectedEmployee(null);
                        setIsSeparated(false);
                      }
                    }}
                    onChange={async (event, newValue) => {
                      setSelectedLocation(newValue);
                      setCvData(null);
                      if (newValue?.location_id) {
                        try {
                          const { data, status } =
                            await FilterReportService.getEmployees(
                              selectedCompany?.id,
                              selectedDivision?.division_id,
                              selectedDepartment?.department_id,
                              newValue?.location_id,
                              false
                            );
                          if (status === SUCCESS) {
                            setEmployees(data?.employee);
                          }
                        } catch (error) {
                          console.log("My Log error: ", error);
                        }
                      }
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Select Location"
                        variant="outlined"
                      />
                    )}
                  />
                </Box>
                {selectedreport?.id !== "employee_separation_report" && (
                  <Box className="flex items-center">
                    <Checkbox
                      checked={isSeparated}
                      onChange={async (e) => {
                        setSelectedEmployee(null);
                        setCvData(null);
                        setIsSeparated(e.target.checked);
                        if (e.target.checked && selectedCompany?.id) {
                          try {
                            const { data, status } =
                              await FilterReportService.getEmployees(
                                selectedCompany?.id,
                                selectedDivision?.division_id,
                                selectedDepartment?.department_id,
                                selectedEmployee?.location_id,
                                true
                              );
                            if (status === SUCCESS) {
                              setEmployees(data?.employee);
                            }
                          } catch (error) {
                            console.log("My Log error: ", error);
                          }
                        } else {
                          if (selectedCompany?.id) {
                            try {
                              const { data, status } =
                                await FilterReportService.getEmployees(
                                  selectedCompany?.id,
                                  selectedDivision?.division_id,
                                  selectedDepartment?.department_id,
                                  selectedEmployee?.location_id,
                                  false
                                );
                              if (status === SUCCESS) {
                                setEmployees(data?.employee);
                              }
                            } catch (error) {
                              console.log("My Log error: ", error);
                            }
                          }
                        }
                      }}
                      color="error"
                    />
                    <span>Separated Employee</span>
                  </Box>
                )}

                <Box>
                  <Autocomplete
                    id="employee-select"
                    options={employees}
                    getOptionLabel={(option) =>
                      option?.employee_name + ` [${option?.unique_id}]`
                    }
                    value={selectedEmployee}
                    onChange={async (event, newValue) => {
                      setSelectedEmployee(newValue);
                      setCvData(null);
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Select Employee"
                        variant="outlined"
                      />
                    )}
                  />
                </Box>
              </Box>
              <Box className="flex flex-col gap-5 ">
                {queryReportType == "attendance" ? (
                  <>
                    <Box>
                      <Autocomplete
                        id="report-select"
                        options={reportTypes}
                        getOptionLabel={(option) => option?.name}
                        value={selectedreport}
                        onChange={async (event, newValue) => {
                          setSelectedReport(newValue);
                          setErrorTextReportType("");
                        }}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            label="Select Report Type"
                            variant="outlined"
                          />
                        )}
                      />
                    </Box>
                    {errorTextReportType && (
                      <Box
                        sx={{ color: "error.main", fontSize: "12px" }}
                        className="-mt-4"
                      >
                        {errorTextReportType}
                      </Box>
                    )}
                  </>
                ) : (
                  <>
                    <Box>
                      <Autocomplete
                        id="report-select"
                        options={employeeReportTypes}
                        getOptionLabel={(option) => option?.name}
                        value={selectedreport}
                        onChange={async (event, newValue) => {
                          setCvData(null);
                          setSelectedReport(newValue);
                          setErrorTextReportType("");
                        }}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            label="Select Report Type"
                            variant="outlined"
                          />
                        )}
                      />
                    </Box>
                    {errorTextReportType && (
                      <Box
                        sx={{ color: "error.main", fontSize: "12px" }}
                        className="-mt-4"
                      >
                        {errorTextReportType}
                      </Box>
                    )}
                  </>
                )}

                <Box className="flex  flex-col md:flex-row gap-5 justify-between">
                  {showDatePicker && (
                    <>
                      <Box>
                        <DatePicker
                          closeOnSelect
                          maxDate={today.endOf("day")}
                          slotProps={{
                            actionBar: { actions: ["clear", "accept"] },
                          }}
                          label={"Start Time"}
                          value={dateTimePicker?.startTime}
                          onChange={(newValue) => {
                            setDateTimePicker((preValue) => ({
                              ...preValue,
                              startTime: newValue,
                            }));
                            setErrorText("");
                          }}
                          sx={{
                            width: "100%",
                            "& .MuiOutlinedInput-notchedOutline": {
                              borderColor:
                                !hasStartTime && hasEndTime
                                  ? "#d32f2f"
                                  : "rgba(0, 0, 0, 0.23)",
                            },
                          }}
                        />
                        {!hasStartTime && hasEndTime && (
                          <Box sx={{ color: "error.main", fontSize: "12px" }}>
                            Please select start time.
                          </Box>
                        )}
                      </Box>
                      <Box>
                        <DatePicker
                          closeOnSelect
                          maxDate={today.endOf("day")}
                          slotProps={{
                            actionBar: { actions: ["clear", "accept"] },
                          }}
                          label={"End Time"}
                          value={dateTimePicker?.endTime}
                          onChange={(newValue) => {
                            setDateTimePicker((preValue) => ({
                              ...preValue,
                              endTime: newValue,
                            }));
                            setErrorText("");
                          }}
                          sx={{
                            width: "100%",
                            "& .MuiOutlinedInput-notchedOutline": {
                              borderColor:
                                hasStartTime && !hasEndTime
                                  ? "#d32f2f"
                                  : "rgba(0, 0, 0, 0.23)",
                            },
                          }}
                        />
                        {hasStartTime && !hasEndTime && (
                          <Box sx={{ color: "error.main", fontSize: "12px" }}>
                            Please select end time.
                          </Box>
                        )}
                      </Box>
                      {errorText && (
                        <Box
                          sx={{ color: "error.main", fontSize: "12px" }}
                          className="-mt-4"
                        >
                          {errorText}
                        </Box>
                      )}
                    </>
                  )}
                </Box>

                {loading && (
                  <Box className="flex items-center justify-center gap-4 text-xl">
                    Processing... <CircularProgress />
                  </Box>
                )}
              </Box>
            </Box>

            <Box
              sx={{
                display: "flex",
                flexDirection: { xs: "column", sm: "row" },
                justifyContent: { xs: "flex-start", sm: "flex-end" },
                marginTop: 3,
              }}
              className="gap-5"
            >
              <CommonButton
                variant="contained"
                color="error"
                size="large"
                onClick={handleReset}
              >
                Clear
              </CommonButton>
              {selectedreport?.id === "employee_cv" && (
                <CommonButton
                  variant="contained"
                  color="primary"
                  startIcon={<PlaylistAddCheck />}
                  size="large"
                  type="submit"
                >
                  Generate Cv
                </CommonButton>
              )}
              {selectedreport?.id != "employee_cv" && (
                <CommonButton
                  variant="contained"
                  color="success"
                  startIcon={<DownloadIcon />}
                  size="large"
                  type="submit"
                >
                  Download
                </CommonButton>
              )}
              {cvData && (
                <BlobProvider document={<EmployeeCv data={cvData} />}>
                  {({ blob, url, loading, error }: any) => {
                    // if (loading) return "Loading document...";
                    return (
                      <a href={url} target="_blank" rel="noopener noreferrer">
                        <CommonButton
                          variant="contained"
                          color="success"
                          startIcon={<DownloadIcon />}
                          size="large"
                        >
                          Download Cv
                        </CommonButton>
                      </a>
                    );
                  }}
                </BlobProvider>
              )}
            </Box>
          </form>
        </Box>
      </Box>
    </div>
  );
};

export default ReportFilter;
